<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Switches and Toggles | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Switches and Toggles | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="switches-and-toggles">Switches and Toggles</h1>

<p>DrawnUi provides toggle controls with platform-specific styling, including switches and checkboxes.</p>
<h2 id="skiaswitch">SkiaSwitch</h2>
<p><code>SkiaSwitch</code> is a toggle control styled according to platform conventions, similar to an on/off switch.</p>
<h3 id="basic-usage">Basic Usage</h3>
<pre><code class="lang-xml">&lt;draw:SkiaSwitch
    IsToggled=&quot;false&quot;
    WidthRequest=&quot;50&quot;
    HeightRequest=&quot;30&quot;
    ColorFrameOff=&quot;Gray&quot;
    ColorFrameOn=&quot;Green&quot;
    ColorThumbOff=&quot;White&quot;
    ColorThumbOn=&quot;White&quot;
    Toggled=&quot;OnSwitchToggled&quot; /&gt;
</code></pre>
<h3 id="platform-specific-styling">Platform-Specific Styling</h3>
<p>Set the <code>ControlStyle</code> property to apply platform-specific styling:</p>
<ul>
<li><code>Platform</code>: Automatically selects the appropriate style for the current platform</li>
<li><code>Cupertino</code>: iOS-style switch with pill-shaped track</li>
<li><code>Material</code>: Android Material Design switch</li>
<li><code>Windows</code>: Windows-style switch</li>
</ul>
<pre><code class="lang-xml">&lt;draw:SkiaSwitch
    ControlStyle=&quot;Cupertino&quot;
    IsToggled=&quot;true&quot; /&gt;
</code></pre>
<h3 id="code-behind-example">Code-Behind Example</h3>
<pre><code class="lang-csharp">private void OnSwitchToggled(object sender, bool isToggled)
{
    // Handle the toggle state change
    if (isToggled)
    {
        // Switch is ON
        DisplayAlert(&quot;Switch&quot;, &quot;Turned ON&quot;, &quot;OK&quot;);
    }
    else
    {
        // Switch is OFF
        DisplayAlert(&quot;Switch&quot;, &quot;Turned OFF&quot;, &quot;OK&quot;);
    }
}
</code></pre>
<h3 id="properties">Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>IsToggled</code></td>
<td>bool</td>
<td>Whether the switch is toggled on or off</td>
</tr>
<tr>
<td><code>ColorFrameOn</code></td>
<td>Color</td>
<td>The color of the track when toggled on</td>
</tr>
<tr>
<td><code>ColorFrameOff</code></td>
<td>Color</td>
<td>The color of the track when toggled off</td>
</tr>
<tr>
<td><code>ColorThumbOn</code></td>
<td>Color</td>
<td>The color of the thumb when toggled on</td>
</tr>
<tr>
<td><code>ColorThumbOff</code></td>
<td>Color</td>
<td>The color of the thumb when toggled off</td>
</tr>
<tr>
<td><code>ControlStyle</code></td>
<td>PrebuiltControlStyle</td>
<td>The platform-specific style</td>
</tr>
<tr>
<td><code>IsAnimated</code></td>
<td>bool</td>
<td>Whether state changes are animated</td>
</tr>
<tr>
<td><code>AnimationSpeed</code></td>
<td>uint</td>
<td>Animation duration in milliseconds (default: 200)</td>
</tr>
</tbody>
</table>
<h3 id="events">Events</h3>
<ul>
<li><code>Toggled</code>: Raised when the switch is toggled on or off
<ul>
<li>Event signature: <code>EventHandler&lt;bool&gt;</code></li>
<li>The bool parameter indicates the new toggle state (true = on, false = off)</li>
</ul>
</li>
</ul>
<h2 id="skiacheckbox">SkiaCheckbox</h2>
<p><code>SkiaCheckbox</code> is a toggle control styled as a checkbox with platform-specific appearance.</p>
<h3 id="basic-usage-1">Basic Usage</h3>
<pre><code class="lang-xml">&lt;draw:SkiaCheckbox
    IsToggled=&quot;false&quot;
    WidthRequest=&quot;24&quot;
    HeightRequest=&quot;24&quot;
    ColorFrameOff=&quot;Gray&quot;
    ColorFrameOn=&quot;Blue&quot;
    ColorThumbOff=&quot;Transparent&quot;
    ColorThumbOn=&quot;White&quot;
    Toggled=&quot;OnCheckboxToggled&quot; /&gt;
</code></pre>
<h3 id="platform-specific-styling-1">Platform-Specific Styling</h3>
<p>Like SkiaSwitch, SkiaCheckbox supports platform-specific styling through the <code>ControlStyle</code> property.</p>
<h3 id="properties-1">Properties</h3>
<p>SkiaCheckbox shares most properties with SkiaSwitch, both inheriting from SkiaToggle.</p>
<h2 id="skiatoggle">SkiaToggle</h2>
<p><code>SkiaToggle</code> is the base class for toggle controls. You can use it to create custom toggle controls with similar behavior to switches and checkboxes.</p>
<h3 id="key-properties">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>IsToggled</code></td>
<td>bool</td>
<td>Whether the control is toggled on or off</td>
</tr>
<tr>
<td><code>DefaultValue</code></td>
<td>bool</td>
<td>The default toggle state</td>
</tr>
<tr>
<td><code>ColorFrameOn/Off</code></td>
<td>Color</td>
<td>The color of the frame in each state</td>
</tr>
<tr>
<td><code>ColorThumbOn/Off</code></td>
<td>Color</td>
<td>The color of the thumb in each state</td>
</tr>
<tr>
<td><code>IsAnimated</code></td>
<td>bool</td>
<td>Whether state changes are animated</td>
</tr>
</tbody>
</table>
<h3 id="events-1">Events</h3>
<ul>
<li><code>Toggled</code>: Raised when the toggle state changes
<ul>
<li>Event signature: <code>EventHandler&lt;bool&gt;</code></li>
<li>The bool parameter indicates the new toggle state</li>
</ul>
</li>
</ul>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/switches.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>

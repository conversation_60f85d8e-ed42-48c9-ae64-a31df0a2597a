﻿using DrawnUi.Controls;
using Sandbox;

namespace AppoMobi.Mobile.Views
{
    /// <summary>
    /// Will become RootLayout
    /// </summary>
    public class ScreenMain : AppScreen
    {

        void InitStyles()
        {
            fontTabs = "FontText";
            unselectedTextSize = 11; //pts
            indicatorUnselectedHeight = 40;
            indicatorUnselectedCharSpacing = 1.6;

            colorButton = AppColors.Primary;
            colorSelected = AppColors.Text;

            colorBar = AppColors.ControlPrimary;
            colorUnselectedText = AppColors.TextPrimaryInvert;
            colorUnselected = AppColors.TextPrimaryInvert;
            selectedIconSize = 40; //pts
            unselectedIconSize = 26; //pts
            unselectedSpacing = 3; //pts
        }

        public ScreenMain(MainPageViewModel vm)
        {
            Model = vm;

            BindingContext = Model;

            VerticalOptions = LayoutOptions.Fill;
            HorizontalOptions = LayoutOptions.Fill;

            InitStyles();

            SkiaLabel LabelChatNotificationsCount;
            var vmChat = App.Instance.ServiceProvider.GetService<TabChatViewModel>();

            Children = new List<SkiaControl>()
            {
                new SkiaViewSwitcher()
                    {
                        Tag = "NavigationLayout",
                        VerticalOptions = LayoutOptions.Fill,
                        HorizontalOptions = LayoutOptions.Fill,
                        Children = new List<SkiaControl>()
                        {
                            new TabRequests(vm),
                            new TabChat(vmChat),
                            new TabSettings(vm)
                        }
                    }
                    .Adapt((switcher) =>
                    {
                        switcher.SetBinding(SkiaViewSwitcher.SelectedIndexProperty,
                            "SelectedTab", BindingMode.TwoWay);
                    }),

                // BOTTOM TABS
               new SkiaLayout()
                {
                    HorizontalOptions = LayoutOptions.Fill,
                    VerticalOptions = LayoutOptions.End,
                    Spacing = 0,
                    Type = LayoutType.Column,
                    Children = new List<SkiaControl>()
                    {
                        new CircleTabs()
                        {
                            UseCache = SkiaCacheType.GPU,
                            AnimationSpeedMs = 200,
                            HeightRequest = 92,
                            BarColor = colorBar,
                            ButtonColor = colorButton,
                            ButtonPadding = 4,
                            ButtonAdjustY = 5,
                            ButtonSize = 70,
                            HorizontalOptions = LayoutOptions.Fill,
                            IndicatorsUnselected =
                            [
                                new SkiaLayout()
                                {
                                    HeightRequest = indicatorUnselectedHeight,
                                    VerticalOptions = LayoutOptions.Center,
                                    HorizontalOptions = LayoutOptions.Center,
                                    UseCache = SkiaCacheType.Image,
                                    Children = new List<SkiaControl>()
                                    {
                                        new SkiaSvg()
                                        {
                                            SvgString = App.Current.Resources.Get<string>("SvgTruckUnselected"),
                                            TintColor = colorUnselected,
                                            WidthRequest = unselectedIconSize,
                                            LockRatio = 1,
                                            HorizontalOptions = LayoutOptions.Center,
                                        },
                                        new SkiaLabel()
                                        {
                                            CharacterSpacing = indicatorUnselectedCharSpacing,
                                            TextColor = colorUnselectedText,
                                            Text = ResStrings.Deliveries,
                                            FontFamily = fontTabs,
                                            FontSize = unselectedTextSize,
                                            VerticalOptions = LayoutOptions.End,
                                            HorizontalOptions = LayoutOptions.Center,
                                        }
                                    }
                                },

                                new SkiaLayout()
                                {
                                    UseCache = SkiaCacheType.Image,
                                    VerticalOptions = LayoutOptions.Fill,
                                    HorizontalOptions = LayoutOptions.Center,
                                    Children = new List<SkiaControl>()
                                    {
                                        new SkiaLayout()
                                        {
                                            HorizontalOptions = LayoutOptions.Center,
                                            HeightRequest = indicatorUnselectedHeight,
                                            UseCache = SkiaCacheType.Image,
                                            VerticalOptions = LayoutOptions.Center,
                                            Children = new List<SkiaControl>()
                                            {
                                                new SkiaSvg()
                                                {
                                                    SvgString = App.Current.Resources.Get<string>("SvgChatUnselected"),
                                                    TintColor = colorUnselected,
                                                    WidthRequest = unselectedIconSize,
                                                    LockRatio = 1,
                                                    HorizontalOptions = LayoutOptions.Center,
                                                },
                                                new SkiaLabel()
                                                {
                                                    CharacterSpacing = indicatorUnselectedCharSpacing,

                                                    VerticalOptions = LayoutOptions.End,
                                                    FontFamily = fontTabs,
                                                    TextColor = colorUnselectedText,
                                                    Text = ResStrings.Support,
                                                    FontSize = unselectedTextSize,
                                                    HorizontalOptions = LayoutOptions.Center,
                                                },
                                            }
                                        },

                                        //notification
                                        new SkiaShape()
                                        {
                                            TranslationX= 20,
                                            TranslationY = -20,
                                            Type = ShapeType.Circle,
                                            BackgroundColor = AppColors.Danger,
                                            VerticalOptions = LayoutOptions.Center,
                                            HorizontalOptions = LayoutOptions.Center,
                                            Children =
                                            {
                                                new SkiaLabel()
                                                {
                                                    Margin = new(4),
                                                    TextColor = Colors.White,
                                                    FontSize = 13,
                                                    Text = "1"
                                                }.Assign(out LabelChatNotificationsCount)
                                            }
                                        }.Observe(Model, (me, prop) =>
                                        {
                                            if (prop.IsEither(nameof(BindingContext), nameof(Model.Notifications)))
                                            {
                                                if (Model.Presentation.NotificationsChat > 0)
                                                {
                                                    me.IsVisible = true;
                                                    LabelChatNotificationsCount.Text = Model.Presentation.NotificationsChat.ToString();
                                                }
                                                else
                                                {
                                                    me.IsVisible = false;
                                                }
                                                Debug.WriteLine($"[Notifications] CHAT {Model.Presentation.NotificationsChat}");
                                            }
                                        })

                                    }
                                },

                                new SkiaLayout()
                                {
                                    HeightRequest = indicatorUnselectedHeight,
                                    VerticalOptions = LayoutOptions.Center,
                                    HorizontalOptions = LayoutOptions.Center,
                                    UseCache = SkiaCacheType.Image,
                                    Children = new List<SkiaControl>()
                                    {
                                        new SkiaSvg()
                                        {
                                            SvgString = App.Current.Resources.Get<string>("SvgProfileUnselected"),
                                            TintColor = colorUnselected,
                                            WidthRequest = unselectedIconSize - 4.5,
                                            TranslationY = 1,
                                            LockRatio = 1,
                                            HorizontalOptions = LayoutOptions.Center,
                                        },
                                        new SkiaLabel()
                                        {
                                            CharacterSpacing = indicatorUnselectedCharSpacing,
                                            VerticalOptions = LayoutOptions.End,
                                            FontFamily = fontTabs,
                                            TextColor = colorUnselectedText,
                                            Text = ResStrings.Profile,
                                            FontSize = unselectedTextSize, 
                                            HorizontalOptions = LayoutOptions.Center,
                                        }
                                    }
                                }
                            ],
                            IndicatorsSelected =
                            [
                                new SkiaSvg()
                                {
                                    Tag = "Home",
                                    UseCache = SkiaCacheType.Image,
                                    SvgString = App.Current.Resources.Get<string>("SvgTruckSelected"),
                                    VerticalOffset = 0,
                                    TintColor = colorSelected,
                                    VerticalAlignment = DrawImageAlignment.Start,
                                    WidthRequest = selectedIconSize + 3,
                                    LockRatio = 1
                                },
                                new SkiaSvg()
                                {
                                    Tag = "Chat",
                                    VerticalOffset = 1,
                                    UseCache = SkiaCacheType.Image,
                                    SvgString = App.Current.Resources.Get<string>("SvgChatSelected"),
                                    TintColor = colorSelected,
                                    WidthRequest = selectedIconSize,
                                    LockRatio = 1
                                },
                                new SkiaSvg()
                                {
                                    Tag = "Cogs",
                                    UseCache = SkiaCacheType.Image,
                                    SvgString = App.Current.Resources.Get<string>("SvgProfileSelected"),
                                    TintColor = colorSelected,
                                    WidthRequest = selectedIconSize - 2,
                                    LockRatio = 1
                                },
                            ],
                        }.Adapt((tabs) =>
                        {
                            tabs.CommandTabReselected = Model.CommandTabReselected;

                            tabs.SetBinding(CustomTabsSelector.SelectedIndexProperty,
                                "SelectedTab", BindingMode.TwoWay);
                        }),

                        new SkiaControl() //tabs padding
                        {
                            BackgroundColor = AppColors.ControlPrimary,
                            HorizontalOptions = LayoutOptions.Fill
                        }.Adapt((padding) =>
                        {
                            padding.SetBinding(SkiaControl.HeightRequestProperty,
                                "Presentation.PaddingBottom");
                        })
                    }
                }
            };
        }

        private double indicatorUnselectedCharSpacing;
        private double indicatorUnselectedHeight;
        private string fontTabs;
        private Color colorButton;
        private Color colorBar;
        private Color colorUnselectedText;
        private Color colorUnselected;
        private Color colorSelected;
        private float selectedIconSize;
        private float unselectedIconSize;
        private float unselectedTextSize;
        private float unselectedSpacing;
        private readonly MainPageViewModel Model;
    }
}
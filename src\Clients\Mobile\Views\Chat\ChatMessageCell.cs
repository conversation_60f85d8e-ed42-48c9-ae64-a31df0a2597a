﻿using AppoMobi.Common.Enums.Project;
using DrawnUi.Controls;
using System.ComponentModel;
using AppoMobi.Maui.Gestures;

namespace AppoMobi.Mobile.Views;

public class ChatMessageCell : SkiaDynamicDrawnCell //FastCellGroupChatMessage
{
    public ChatMetaType Template { get; protected set; } = ChatMetaType.Default;

    public bool IsSelected { get; set; }
    public bool CanBeSelected { get; set; } = true;
    public bool CanBeTapped { get; set; } = true;
    public bool IsNew { get; set; }
    public bool ShowDate { get; set; }

    protected SkiaSvg BubbleArrowIncoming;
    protected SkiaSvg BubbleArrowOutcoming;
    protected SkiaSvg IconWasSent;
    protected SkiaSvg IconWasDelivered;
    protected ChatMessageLabel LabelMessage;
    protected SkiaShape MainFrame;
    protected SkiaLayout AttachedMessageStack;
    protected SkiaLayout MainHorizontalStack;
    protected SkiaLayout MessageStack;
    protected SkiaLayout MessageWithBannerStack;
    protected SkiaLabel LabelTime;
    protected SkiaLabel LabelFirstDate;
    protected SkiaLabel LabelNameAttached;
    protected SkiaLabel LabelMessageAttached;
    protected SkiaImage Banner;
    protected SkiaSvg IconAttachment;
    protected bool ShowName { get; set; }

    protected static Color ColorSystemTitle;
    protected static Color ColorIncoming;
    protected static Color ColorOutcoming;
    protected static Color ColorSeparatorText;
    protected static Color ColorText;
    protected static Color ColorTextTime; //AppColors.BackgroundPrimaryOpacity4;
    protected static Color ColorCheck;

    protected static float MessageMaxWidth = 200.0f;

    public ChatMessageCell()
    {
        FastMeasurement = true;

        ColorSystemTitle = AppColors.Primary;
        ColorIncoming = AppColors.BackgroundMinor;
        ColorOutcoming = AppColors.BackgroundNotify;
        ColorSeparatorText = Colors.Gainsboro;
        ColorText = AppColors.Text;
        ColorTextTime = AppColors.TextSecondary; //AppColors.BackgroundPrimaryOpacity4;
        ColorCheck = AppColors.Text;

        IsParentIndependent = true;
        Rotation = 180;
        UseCache = SkiaCacheType.Image;
        HorizontalOptions = LayoutOptions.Fill;
        Children = new List<SkiaControl>()
        {
            new SkiaStack()
            {
                Spacing = 0,
                Children =
                {
                    //new date
                    new SkiaLabel()
                    {
                        Margin = new Thickness(10, 4, 10, 8),
                        LineBreakMode = LineBreakMode.NoWrap,
                        MaxLines = 1,
                        FontFamily = AppFonts.Bold,
                        Text = $"Time",
                        FontSize = 10,
                        HorizontalOptions = LayoutOptions.Center,
                        TextColor = AppColors.TextMinor,
                    }.Assign(out LabelFirstDate),

                    //container to glue the bubble with its tail
                    new SkiaLayout() //MainHorizontalStack
                        {
                            Spacing = 0,
                            Type = LayoutType.Row,
                            Margin = new Thickness(8, 0),
                            Children = new List<SkiaControl>()
                            {
                                new SkiaSvg()
                                {
                                    UseCache = SkiaCacheType.Operations,
                                    TranslationY = 6,
                                    Aspect = TransformAspect.AspectFit,
                                    TintColor = ColorIncoming,
                                    SvgString = App.Current.Resources.Get<string>("SvgChatFromLeft"),
                                    HeightRequest = 7,
                                    WidthRequest = 7
                                }.Assign(out BubbleArrowIncoming),

                                // MainFrame auto-width
                                new SkiaShape()
                                {
                                    Padding = 0,
                                    MaximumWidthRequest = MessageMaxWidth,
                                    CornerRadius = 8,
                                    Children =
                                    {
                                        new SkiaLayout()
                                        {
                                            Type = LayoutType.Row,
                                            Spacing = 0,
                                            Children = new List<SkiaControl>()
                                            {
                                                //icon for file attachments
                                                new SkiaSvg()
                                                {
                                                    UseCache = SkiaCacheType.Operations,
                                                    Tag = "Attachment",
                                                    IsVisible = false,
                                                    Margin = new Thickness(8, 0, 0, 0),
                                                    Aspect = TransformAspect.AspectFit,
                                                    SvgString = App.Current.Resources.Get<string>("SvgAttachment"),
                                                    TintColor = ColorTextTime,
                                                    HeightRequest = 20,
                                                    LockRatio = 1,
                                                    VerticalOptions = LayoutOptions.Center
                                                }.Assign(out IconAttachment),

                                                //message stack auto-width
                                                new SkiaLayout()
                                                {
                                                    Type = LayoutType.Column,
                                                    Spacing = 0,
                                                    Children = new List<SkiaControl>()
                                                    {
                                                        //banner for attachment like image or link
                                                        new SkiaImage()
                                                        {
                                                            UseCache = SkiaCacheType.Image,
                                                            IsVisible = false,
                                                            Tag = "Banner",
                                                            Aspect = TransformAspect.AspectCover,
                                                            BackgroundColor = Colors.DarkGray,
                                                            HorizontalOptions = LayoutOptions.Fill,
                                                            EraseChangedContent = true
                                                        }.Assign(out Banner),

                                                        //ATTACHED MESSAGE
                                                        new SkiaLayout()
                                                            {
                                                                //WidthRequest = MessageMaxWidth,
                                                                BackgroundColor = Color.FromHex("#11000000"),
                                                                IsVisible = false,
                                                                Spacing = 0,
                                                                Children = new List<SkiaControl>()
                                                                {
                                                                    new SkiaShape()
                                                                    {
                                                                        Margin = new Thickness(8, 8, 0, 8),
                                                                        CornerRadius = 0,
                                                                        BackgroundColor = ColorSystemTitle,
                                                                        HorizontalOptions = LayoutOptions.Start,
                                                                        WidthRequest = 2,
                                                                        VerticalOptions = LayoutOptions.Fill,
                                                                    },

                                                                    new SkiaLabel()
                                                                    {
                                                                        LineBreakMode = LineBreakMode.TailTruncation,
                                                                        MaxLines = 1,
                                                                        Text = $"APlayer",
                                                                        FontSize = 15,
                                                                        HorizontalOptions = LayoutOptions.Fill,
                                                                        Margin = new Thickness(16, 8, 8, 0),
                                                                        TextColor = ColorSystemTitle,
                                                                    }.Assign(out LabelNameAttached),

                                                                    new SkiaLabel()
                                                                    {
                                                                        LineBreakMode = LineBreakMode.TailTruncation,
                                                                        MaxLines = 1,
                                                                        Text = $"...",
                                                                        FontSize = 15,
                                                                        WidthRequest = MessageMaxWidth,
                                                                        Margin = new Thickness(16, 28, 8, 8),
                                                                        TextColor = ColorText,
                                                                    }.Assign(out LabelMessageAttached)
                                                                }
                                                            }
                                                            .OnTapped((me) =>
                                                            {
                                                                if (Context is ChatMessage item)
                                                                {
                                                                    Debug.WriteLine(
                                                                        $"Tapped [{item.DbId}] '{item.Text}'");
                                                                    if (Parent.BindingContext is ChatViewModel vm)
                                                                    {
                                                                        vm.CommandScrollToMessage.Execute(
                                                                            item.AttachedMessage);
                                                                    }
                                                                }
                                                            })
                                                            .Assign(out AttachedMessageStack),

                                                        //layout message with statuses and time
                                                        new SkiaLayout()
                                                        {
                                                            Padding = new(10, 8),
                                                            Children = new List<SkiaControl>()
                                                            {
                                                                // MESSAGE TEXT
                                                                new ChatMessageLabel()
                                                                {
                                                                    FontSize = 14,
                                                                    LineSpacing = 1.2,
                                                                    FontFamily = AppFonts.SemiBold,
                                                                    TextColor = ColorText,
                                                                    Margin = new Thickness(0, 0, 0, 20)
                                                                }.Assign(out LabelMessage),

                                                                // TIME SENT
                                                                new SkiaLabel()
                                                                {
                                                                    LineBreakMode = LineBreakMode.NoWrap,
                                                                    MaxLines = 1,
                                                                    FontFamily = AppFonts.SemiBold,
                                                                    FontSize = 9,
                                                                    HorizontalOptions = LayoutOptions.End,
                                                                    Margin = new Thickness(0, 0, 2, 2),
                                                                    VerticalOptions = LayoutOptions.End,
                                                                    TextColor = ColorTextTime,
                                                                }.Assign(out LabelTime),

                                                                // STATUS SENT
                                                                new SkiaSvg()
                                                                {
                                                                    UseCache = SkiaCacheType.Operations,
                                                                    Aspect = TransformAspect.AspectFit,
                                                                    Margin = new Thickness(0, 0, 4, 2),
                                                                    TintColor = ColorCheck,
                                                                    SvgString = App.Current.Resources.Get<string>(
                                                                        "SvgCheck"),
                                                                    VerticalOptions = LayoutOptions.End,
                                                                    HorizontalOptions = LayoutOptions.End,
                                                                    HeightRequest = 11,
                                                                    WidthRequest = 11
                                                                }.Assign(out IconWasSent),

                                                                // STATUS DELIVERED
                                                                new SkiaSvg()
                                                                {
                                                                    UseCache = SkiaCacheType.Operations,
                                                                    Aspect = TransformAspect.AspectFit,
                                                                    TintColor = ColorCheck,
                                                                    Margin = new Thickness(0, 0, 0, 2),
                                                                    SvgString = App.Current.Resources.Get<string>(
                                                                        "SvgCheck"),
                                                                    VerticalOptions = LayoutOptions.End,
                                                                    HorizontalOptions = LayoutOptions.End,
                                                                    HeightRequest = 11,
                                                                    WidthRequest = 11
                                                                }.Assign(out IconWasDelivered)

                                                                //STATUS SEEN will just change colors of existing icons above
                                                            }
                                                        }.Assign(out MessageStack)
                                                    }
                                                }.Assign(out MessageWithBannerStack)
                                            }
                                        }
                                    }
                                }.Assign(out MainFrame),

                                new SkiaSvg()
                                {
                                    UseCache = SkiaCacheType.Operations,
                                    TranslationY = 6,
                                    TintColor = ColorOutcoming,
                                    Aspect = TransformAspect.AspectFit,
                                    SvgString = App.Current.Resources.Get<string>("SvgChatFromRight"),
                                    HeightRequest = 7,
                                    WidthRequest = 7
                                }.Assign(out BubbleArrowOutcoming),
                            }
                        }.Assign(out MainHorizontalStack)
                        .OnTapped((me) =>
                        {
                            if (Context is ChatMessage item)
                            {
                                Debug.WriteLine($"Tapped [{item.DbId}] '{item.Text}'");

                                //item.IsFirstDate = !item.IsFirstDate;
                                //SetContentFull(item);
                                //this.Parent.InvalidateByChild(this);
                                //return;

                                if (Parent.BindingContext is ChatViewModel vm)
                                {
                                    vm.CommandSelectedItem.Execute(item);
                                }
                            }
                        })
                        .OnLongPressing((me) =>
                        {
                            if (Context is ChatMessage item)
                            {
                                Debug.WriteLine($"Tapped [{item.DbId}] '{item.Text}'");
                                if (Parent.BindingContext is ChatViewModel vm)
                                {
                                    vm.CommandMessageOptions.Execute(item);
                                }
                            }
                        }),
                }
            }
        };

        //this.OnTapped((me) =>
        //{
        //    if (me.Context is ChatMessage item)
        //    {
        //        Debug.WriteLine($"Tapped '{item.Text}'");
        //    }
        //});
    }

    //public override ISkiaGestureListener ProcessGestures(SkiaGesturesParameters args, GestureEventProcessingInfo apply)
    //{

    //    if (args.Type == TouchActionResult.Tapped)
    //    {
    //        if (Context is ChatMessage item)
    //        {
    //            Debug.WriteLine($"Tapped '{item.Text}'");
    //            if (Parent.Context is ChatViewModel vm)
    //            {
    //                vm.CommandSelectedItem.Execute(item);
    //            }
    //            return this;
    //        }
    //    }

    //    return base.ProcessGestures(args, apply);
    //}

    protected override void SetContent(object ctx)
    {
        base.SetContent(ctx);

        var item = ctx as ChatMessage;
        if (item != null)
        {
            SetContentFull(item);
        }
    }

    protected override void ContextPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == "Id") //Message was sent and received id from server
        {
            ApplyContent(Context as ChatMessage);
            return;
        }

        if (e.PropertyName == "Read"
            || e.PropertyName == "Sent"
            || e.PropertyName == "Delivered")
        {
            UpdateStatus(Context as ChatMessage);
            Update();
        }
        else if (e.PropertyName == "Notify")
        {
            var item = Context as ChatMessage;
            if (item != null)
            {
                IsNew = item.Notify;
            }
            else
            {
                IsNew = false;
            }

            UpdateContainer(Context as ChatMessage);
            Update();
        }

        base.ContextPropertyChanged(sender, e);
    }

    protected virtual void SetContentFull(ChatMessage item)
    {
        IsNew = item.Notify;
        Template = item.PresentAs;
        if (item.Metadata != null && item.Metadata.Domain == "request")
        {
            Template = ChatMetaType.System;
            //request key is in the meta Url
        }

        ShowDate = item.IsFirstDate;
        CanBeTapped = Template != ChatMetaType.Default || Template == ChatMetaType.System;

        ApplyContent(item);
    }

    public void ApplyContent(ChatMessage? item)
    {
        if (item == null)
            return;

        UpdateContainer(item);
        UpdateContent(item);
        UpdateStatus(item);
    }

    protected virtual void UpdateContainer(ChatMessage? item)
    {
        if (item == null)
            return;

        IconWasSent.IsVisible = false;
        IconWasDelivered.IsVisible = false;

        if (item.Outgoing)
        {
            UpdateContainerForOutgoing(item);
        }
        else
        {
            UpdateContainerForIncoming(item);
        }


        if (Template == ChatMetaType.File)
        {
            IconAttachment.IsVisible = true;
            LabelMessage.TextColor = ColorText;
        }
        else
        {
            IconAttachment.IsVisible = false;
            LabelMessage.TextColor = ColorText;
        }

        if (Banner != null)
        {
            if (Template == ChatMetaType.Image || Template == ChatMetaType.Video)
            {
                Banner.HeightRequest = 200;
                Banner.IsVisible = true;
                MainFrame.WidthRequest = MessageMaxWidth;
                MessageWithBannerStack.HorizontalOptions = LayoutOptions.Fill;
            }
            else if (Template == ChatMetaType.Article)
            {
                Banner.HeightRequest = 80;
                Banner.IsVisible = true;
                MainFrame.WidthRequest = MessageMaxWidth;
                MessageWithBannerStack.HorizontalOptions = LayoutOptions.Fill;
            }
            else
            {
                Banner.IsVisible = false;
                MainFrame.WidthRequest = -1;
                MessageWithBannerStack.HorizontalOptions = LayoutOptions.Start;
            }
        }
        else
        {
            Banner.IsVisible = false;
            MainFrame.WidthRequest = -1;
            MessageWithBannerStack.HorizontalOptions = LayoutOptions.Start;
        }
    }

    protected virtual void UpdateContainerForIncoming(ChatMessage item)
    {
        if (item == null)
            return;

        LabelTime.Margin = new Thickness(0, 0, 0, 2);
        //LabelTime.TextColor = AppColors.BackgroundPrimaryOpacity4;

        MainFrame.BackgroundColor = ColorIncoming;
        MainHorizontalStack.HorizontalOptions = LayoutOptions.Start;
        MessageStack.HorizontalOptions = LayoutOptions.Start;

        BubbleArrowOutcoming.IsVisible = false;
        BubbleArrowIncoming.IsVisible = true;
        IconWasSent.IsVisible = false;
        IconWasDelivered.IsVisible = false;

        BubbleArrowIncoming.IsGhost = !item.IsFirst;
    }

    protected virtual void UpdateContainerForOutgoing(ChatMessage item)
    {
        if (item == null)
            return;

        LabelTime.Margin = new Thickness(0, 0, 20, 2);
        //LabelTime.TextColor = ColorTextTime;

        MainFrame.BackgroundColor = ColorOutcoming;
        MainHorizontalStack.HorizontalOptions = LayoutOptions.End;
        MessageStack.HorizontalOptions = LayoutOptions.End;

        BubbleArrowIncoming.IsVisible = false;
        BubbleArrowOutcoming.IsVisible = true;
        BubbleArrowOutcoming.IsGhost = !item.IsFirst;
    }

    protected virtual void UpdateStatus(ChatMessage? item)
    {
        if (item == null)
            return;

        if (item.Outgoing)
        {
            if (item.Read)
            {
                IconWasSent.TintColor = Colors.DeepSkyBlue;
                IconWasDelivered.TintColor = Colors.DeepSkyBlue;
                IconWasSent.IsVisible = true;
                IconWasDelivered.IsVisible = true;
            }
            else
            {
                IconWasSent.TintColor = ColorCheck;
                IconWasDelivered.TintColor = ColorCheck;
                IconWasSent.IsVisible = item.Sent;
                IconWasDelivered.IsVisible = item.Delivered;
            }
        }
        else
        {
            IconWasSent.IsVisible = false;
            IconWasDelivered.IsVisible = false;
        }
    }

    protected virtual void UpdateContent(ChatMessage item)
    {
        if (item == null)
            return;

        //todo !!!
        if (ShowDate)
        {
            var wasInvisible = !LabelFirstDate.IsVisible;
            LabelFirstDate.Text = item.WhenDesc;
            LabelFirstDate.IsVisible = true;
            //if (wasInvisible && WasMeasured)
            //    Parent.Invalidate();
        }
        else
        {
            var wasVisible = LabelFirstDate.IsVisible;
            LabelFirstDate.IsVisible = false;
            //if (wasVisible && WasMeasured)
            //    Parent.Invalidate();
        }


        //if (Template == ChatMetaType.Image || Template == ChatMetaType.Video)
        //{
        //    LabelMessage.IsVisible = false;
        //}
        //else
        //{
        //    LabelMessage.IsVisible = true;
        //}

        var text = item.Text;

        if (item.AttachmentType == 5)
        {
            if (item.AttachedMessage != null)
            {
                //       text = $"Attached: from {item.AttachedMessage.PlayerName}, {item.AttachedMessage.Text} --- {item.Text}";
                LabelNameAttached.Text = item.AttachedMessage.PlayerName;
                LabelMessageAttached.Text = item.AttachedMessage.Text;
            }
            else
                text = $"Meta: {item.Meta}";

            AttachedMessageStack.IsVisible = true;
            MessageStack.Margin = MessageStack.Margin.WithTop(AttachedMessageStack.HeightRequest);
        }
        else
        {
            MessageStack.Margin = MessageStack.Margin.WithTop(0);
            AttachedMessageStack.IsVisible = false;
        }


        if (item.Outgoing)
        {
            //LabelName.IsVisible = false;

            LabelMessage.Text = text + "           ";
        }
        else
        {
            //LabelName.IsVisible = ShowName && item.IsFirst;
            //LabelName.Text = item.PlayerName;

            LabelMessage.Text = text + "      ";
        }

        //LabelName.Measure(200 * RenderingScale, Height * RenderingScale); //reduce by side padding 
        LabelTime.Text = item.DisplayTime;

        if (Banner != null)
        {
            if (Template == ChatMetaType.Image || Template == ChatMetaType.Video)
            {
                Banner.PreviewBase64 = item.Preview;
                Banner.Source = item.ImageMain.Large;
            }
            else if (Template == ChatMetaType.Article && item.Metadata != null)
            {
                Banner.PreviewBase64 = null;
                Banner.Source = item.Metadata.Image;
            }
            else
            {
                Banner.Source = null;
                Banner.PreviewBase64 = null;
            }
        }
    }
}
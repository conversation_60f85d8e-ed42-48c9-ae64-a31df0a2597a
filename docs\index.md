﻿# DrawnUi.Maui

**Build beautiful, high-performance mobile apps with C# code-behind instead of XAML**

DrawnUi is a rendering engine that draws your entire UI on a hardware-accelerated Skia canvas. Create pixel-perfect custom controls with gestures and animations, all powered by [SkiaSharp](https://github.com/mono/SkiaSharp) 😍.

**Supports:** iOS • MacCatalyst • Android • Windows

---

## 🚀 Quick Start

### 1. Install DrawnUi
```bash
dotnet add package DrawnUi.Maui
```

### 2. Learn the Basics
- **[📖 Getting Started](articles/getting-started.md)** - Installation and setup guide
- **[🎯 Your First App](articles/first-app.md)** - Build your first DrawnUI app in 5 minutes
- **[⚡ Fluent Extensions](articles/fluent-extensions.md)** - Master the code-behind fluent API

### 3. Explore Controls
- **[🎛️ All Controls](articles/controls/index.md)** - Buttons, layouts, animations, and more
- **[📱 Live Demo](demo.md)** - Interactive examples you can try

### 4. Go Advanced
- **[🏗️ Advanced Topics](articles/advanced/index.md)** - Architecture, performance, and platform-specific features
- **[📚 API Reference](api/index.md)** - Complete technical documentation

---

## ✨ Why DrawnUi?

**🎨 Code-Behind First**
- Write UI in C# with fluent extensions - no XAML needed
- Type-safe, IntelliSense-friendly development
- Reactive property observation without traditional bindings

**⚡ High Performance**
- Hardware-accelerated Skia rendering
- Efficient caching and virtualization
- Smooth 60fps animations and gestures

**🎯 Pixel Perfect**
- Consistent UI across all platforms
- Custom controls that look exactly how you want
- Full control over every pixel

**🔧 Flexible Architecture**
- Use alongside existing MAUI controls
- Or go fully drawn with SkiaShell navigation
- MIT licensed and production-ready

---

## 📱 See It In Action

**Live Examples:**
- **[Engine Demo](https://github.com/taublast/AppoMobi.Maui.DrawnUi.Demo)** - Comprehensive control showcase
- **[Space Shooter Game](https://github.com/taublast/AppoMobi.Maui.DrawnUi.SpaceShooter)** - Full arcade game built with DrawnUI
- **[CollectionView Demo](https://github.com/taublast/SurfAppCompareDrawn)** - Performance comparison with native controls
- **[Shaders Carousel](https://github.com/taublast/ShadersCarousel/)** - Advanced SkiaSharp v3 effects

---

## 🆘 Need Help?

- **[❓ Troubleshooting](articles/fluent-extensions.md#troubleshooting)** - Common issues and solutions
- **[💬 GitHub Issues](https://github.com/taublast/DrawnUi.Maui/issues)** - Report bugs or ask questions
- **[📖 Background Article](https://taublast.github.io/posts/MauiJuly/)** - Why DrawnUI was created

---

**Ready to get started?** → **[Install and Setup Guide](articles/getting-started.md)**
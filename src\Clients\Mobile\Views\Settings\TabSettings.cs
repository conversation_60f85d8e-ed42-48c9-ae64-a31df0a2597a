﻿using AppoMobi.Common.ResX;
using AppoMobi.Mobile.Infrastructure;
using AppoMobi.Mobile.ViewModels;
using DrawnUi.Draw;
using Microsoft.Maui.Controls;
using System.Collections.Generic;

namespace AppoMobi.Mobile.Views
{
    /// <summary>
    /// Settings tab with options list
    /// </summary>
    public class TabSettings : AppScreen
    {
        private readonly MainPageViewModel Model;
        private SkiaScroll MainScroll;
        private SkiaLayout StackContainer;
        private SkiaLayout StackOptions;

        public TabSettings(MainPageViewModel vm)
        {
            Model = vm;
            BindingContext = Model;

            CreateContent();
        }


        private void CreateContent()
        {
            Children = new List<SkiaControl>
            {
                // Main scroll
                new AppScroll
                {
                    HorizontalOptions = LayoutOptions.Fill,
                    VerticalOptions = LayoutOptions.Fill,
                    ZIndex = -1,
                    Content = CreateContentLayout()
                }.Assign(out MainScroll)
            };
        }

        private SkiaLayout CreateContentLayout()
        {
            return new DebugThisStack//ScreenVerticalStack
            {
                UseCache = SkiaCacheType.ImageComposite,

                Padding = new Thickness(0, 24, 0, 0),
                HorizontalOptions = LayoutOptions.Fill,
                MinimumHeightRequest = -1,
                Spacing = 0,
                Type = LayoutType.Column,

                Children = new List<SkiaControl>
                {
                    // Navbar padding for fullscreen version
                    new StatusBarPlaceholder(),

                    new LabelScreenTitle
                    {
                        //Margin = new Thickness(16, 16, 64, 8),
                        Margin = new(24, 0, 24, 32),
                        Text = ResStrings.Profile,
                    },

                    // Options list
                    CreateOptionsLayout(),

                    // Version
                    new SkiaLabel
                    {
                        Margin = new Thickness(24),
                        FontFamily = "FontText",
                        FontSize = 12,
                        HorizontalOptions = LayoutOptions.Center,
                        LineBreakMode = LineBreakMode.TailTruncation,
                        Text = Model.Presentation.BuildDesc,
                        TextColor = AppColors.IconSecondary
                    },

                    // Bottom tabs padding
                    new SkiaControl
                    {
                        WidthRequest = 1
                    }.Adapt((control) =>
                    {
                        control.SetBinding(SkiaControl.HeightRequestProperty, "Presentation.BottomTabsHeightRequest");
                    })
                }
            }.Assign(out StackContainer);
        }

        public class DebugThisStack : ScreenVerticalStack
        {
            public override bool NeedMeasure
            {
                get
                {
                    return base.NeedMeasure;
                }
                set
                {
                    if (value)
                    {
                        var debug = true;
                    }
                    base.NeedMeasure = value;
                }
            }
        }

        private SkiaLayout CreateOptionsLayout()
        {
            return new SkiaLayout
            {
                    HorizontalOptions = LayoutOptions.Fill,
                    RecyclingTemplate = RecyclingTemplate.Disabled,
                    Spacing = 0,
                    Split = 1,
                    Tag = "StackSettings",
                    Type = LayoutType.Column,
                    IsClippedToBounds = true,
                    UseCache = SkiaCacheType.ImageComposite,
                    ItemTemplate = CreateItemTemplate()
                }.Assign(out StackOptions)
                .Adapt((layout) =>
                {
                    layout.SetBinding(SkiaLayout.ItemsSourceProperty, "SettingsFields");
                });
        }

        private DataTemplate CreateItemTemplate()
        {
            return new DataTemplate(() =>
            {
                SkiaShape cellShape = null;
                SkiaShape notificationsShape = null;
                SkiaLabel notificationsLabel = null;

                return new CellSettings
                {
                    HeightRequest = 56,
                    HorizontalOptions = LayoutOptions.Fill,
                    //ExpandDirtyRegion = 8,
                    UseCache = SkiaCacheType.Operations,
                    Children = new List<SkiaControl>
                    {
                        // Background shape with shadow
                        new SkiaShape
                        {
                            Margin = new Thickness(16, 3),
                            BackgroundColor = AppColors.Background,
                            CornerRadius = 12,
                            HorizontalOptions = LayoutOptions.Fill,
                            StrokeColor = AppColors.ControlMinor,
                            StrokeWidth = 1,
                            VerticalOptions = LayoutOptions.Fill,
                            ZIndex = -1,
                            //Shadows = new List<SkiaShadow>
                            //{
                            //    new SkiaShadow
                            //    {
                            //        Blur = 1,
                            //        Opacity = 1.0,
                            //        X = 1,
                            //        Y = 1,
                            //        Color = AppColors.ControlMinor
                            //    }
                            //}
                        }.Assign(out cellShape),

                        // Content layout
                        new SkiaLayout
                        {
                            Padding = new Thickness(32, 0, 0, 0),
                            HorizontalOptions = LayoutOptions.Fill,
                            Spacing = 0,
                            Type = LayoutType.Row,
                            VerticalOptions = LayoutOptions.Fill,
                            Children = new List<SkiaControl>
                            {
                                // Icon
                                new SkiaSvg
                                {
                                    Margin = new Thickness(0, 0, 68, 0),
                                    HeightRequest = 16,
                                    LockRatio = 1,
                                    Opacity = 0.4,
                                    Tag = "SvgIcon",
                                    TintColor = AppColors.Text,
                                    VerticalOptions = LayoutOptions.Center
                                }.Adapt((svg) =>
                                {
                                    svg.SetBinding(SkiaSvg.ZoomProperty, nameof(ActionOption.ZoomIcon));
                                }),

                                // Title
                                new SkiaLabel
                                { 
                                    Margin = new Thickness(0, 0, 80, 0),
                                    FontFamily = "FontTextSemiBold",
                                    FontSize = 17,
                                    HorizontalOptions = LayoutOptions.Fill,
                                    LineBreakMode = LineBreakMode.TailTruncation,
                                    MaxLines = 1,
                                    Tag = "LabelTitle",
                                    TextColor = AppColors.Text,
                                    VerticalOptions = LayoutOptions.Center
                                },

                                //Notifications
                                new SkiaShape()
                                {
                                    Type = ShapeType.Circle,
                                    Margin = new Thickness(0, 0, 56, 0),
                                    VerticalOptions = LayoutOptions.Center,
                                    HorizontalOptions = LayoutOptions.End, //todo fix Start !!!
                                    BackgroundColor = AppColors.Icon,
                                    WidthRequest = 24,
                                    LockRatio = 1,
                                    Children =
                                    {
                                        new SkiaLabel()
                                        {
                                            FontSize = 11,
                                            TextColor = Colors.White,
                                            VerticalOptions = LayoutOptions.Fill,
                                            HorizontalOptions = LayoutOptions.Fill,
                                            VerticalTextAlignment = TextAlignment.Center,
                                            HorizontalTextAlignment = DrawTextAlignment.Center
                                        }.Assign(out notificationsLabel)
                                    }
                                }.Assign(out notificationsShape),

                                // Right arrow
                                new SkiaSvg
                                {
                                    Margin = new Thickness(0, 0, 32, 0),
                                    HeightRequest = 14,
                                    HorizontalOptions = LayoutOptions.End,
                                    LockRatio = 1,
                                    SvgString = App.Current.Resources.Get<string>("SvgExpandRight"),
                                    VerticalOptions = LayoutOptions.Center
                                }
                            }
                        }
                    }
                }.Adapt((cell) =>
                {
                    // Add ripple effect
                    AddGestures.SetAnimationTapped(cell, SkiaTouchAnimation.Ripple);
                    AddGestures.SetTouchEffectColor(cell, AppColors.Touch);

                    cell.ClippedEffectsWith = cellShape;
                    notificationsShape.Observe(notificationsLabel, (shape, prop) =>
                    {
                        if (prop.IsEither(nameof(SkiaControl.BindingContext), nameof(SkiaLabel.Text)))
                        {
                            if (string.IsNullOrEmpty(notificationsLabel.Text) || notificationsLabel.Text == "0")
                            {
                                shape.IsVisible = false;
                            }
                            else
                            {
                                shape.IsVisible = true;
                            }
                        }
                    });

                    cell.SetBinding(AddGestures.CommandTappedProperty, nameof(ActionOption.Command));
                    cell.SetBinding(SkiaControl.IsVisibleProperty, nameof(ActionOption.IsVisible));
                    notificationsLabel.SetBinding(SkiaLabel.TextProperty,  "Notifications.Value");

                });
            });
        }

    }
}
<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Class RadioButtons | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Class RadioButtons | DrawnUi Documentation ">
    
    <meta name="description" content="Manages radio button groups, ensuring only one button is selected per group. Supports grouping by parent control or by string name.">
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="DrawnUi.Controls.RadioButtons">



  <h1 id="DrawnUi_Controls_RadioButtons" data-uid="DrawnUi.Controls.RadioButtons" class="text-break">Class RadioButtons</h1>
  <div class="markdown level0 summary"><p>Manages radio button groups, ensuring only one button is selected per group.
Supports grouping by parent control or by string name.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
    <div class="level1"><span class="xref">RadioButtons</span></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Controls.html">Controls</a></h6>
  <h6><strong>Assembly</strong>: DrawnUi.Maui.dll</h6>
  <h5 id="DrawnUi_Controls_RadioButtons_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class RadioButtons</code></pre>
  </div>
  <h3 id="constructors">Constructors
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_RadioButtons__ctor.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.RadioButtons.%23ctor%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L131">View Source</a>
  </span>
  <a id="DrawnUi_Controls_RadioButtons__ctor_" data-uid="DrawnUi.Controls.RadioButtons.#ctor*"></a>
  <h4 id="DrawnUi_Controls_RadioButtons__ctor" data-uid="DrawnUi.Controls.RadioButtons.#ctor">RadioButtons()</h4>
  <div class="markdown level1 summary"><p>Initializes a new instance of the RadioButtons class.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public RadioButtons()</code></pre>
  </div>
  <h3 id="properties">Properties
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_RadioButtons_All.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.RadioButtons.All%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L14">View Source</a>
  </span>
  <a id="DrawnUi_Controls_RadioButtons_All_" data-uid="DrawnUi.Controls.RadioButtons.All*"></a>
  <h4 id="DrawnUi_Controls_RadioButtons_All" data-uid="DrawnUi.Controls.RadioButtons.All">All</h4>
  <div class="markdown level1 summary"><p>Gets the singleton instance of the RadioButtons manager.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static RadioButtons All { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Controls.RadioButtons.html">RadioButtons</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_RadioButtons_GroupsByName.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.RadioButtons.GroupsByName%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L125">View Source</a>
  </span>
  <a id="DrawnUi_Controls_RadioButtons_GroupsByName_" data-uid="DrawnUi.Controls.RadioButtons.GroupsByName*"></a>
  <h4 id="DrawnUi_Controls_RadioButtons_GroupsByName" data-uid="DrawnUi.Controls.RadioButtons.GroupsByName">GroupsByName</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected Dictionary&lt;string, List&lt;ISkiaRadioButton&gt;&gt; GroupsByName { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Controls.ISkiaRadioButton.html">ISkiaRadioButton</a>&gt;&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_RadioButtons_GroupsByParent.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.RadioButtons.GroupsByParent%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L126">View Source</a>
  </span>
  <a id="DrawnUi_Controls_RadioButtons_GroupsByParent_" data-uid="DrawnUi.Controls.RadioButtons.GroupsByParent*"></a>
  <h4 id="DrawnUi_Controls_RadioButtons_GroupsByParent" data-uid="DrawnUi.Controls.RadioButtons.GroupsByParent">GroupsByParent</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected Dictionary&lt;SkiaControl, List&lt;ISkiaRadioButton&gt;&gt; GroupsByParent { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary</a>&lt;<a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="DrawnUi.Controls.ISkiaRadioButton.html">ISkiaRadioButton</a>&gt;&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_RadioButtons_AddToGroup_DrawnUi_Controls_ISkiaRadioButton_DrawnUi_Draw_SkiaControl_.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.RadioButtons.AddToGroup(DrawnUi.Controls.ISkiaRadioButton%2CDrawnUi.Draw.SkiaControl)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L157">View Source</a>
  </span>
  <a id="DrawnUi_Controls_RadioButtons_AddToGroup_" data-uid="DrawnUi.Controls.RadioButtons.AddToGroup*"></a>
  <h4 id="DrawnUi_Controls_RadioButtons_AddToGroup_DrawnUi_Controls_ISkiaRadioButton_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Controls.RadioButtons.AddToGroup(DrawnUi.Controls.ISkiaRadioButton,DrawnUi.Draw.SkiaControl)">AddToGroup(ISkiaRadioButton, SkiaControl)</h4>
  <div class="markdown level1 summary"><p>Adds a radio button control to a parent-based group. Ensures at least one button in the group is selected.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AddToGroup(ISkiaRadioButton control, SkiaControl parent)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Controls.ISkiaRadioButton.html">ISkiaRadioButton</a></td>
        <td><span class="parametername">control</span></td>
        <td><p>The radio button control to add to the group.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></td>
        <td><span class="parametername">parent</span></td>
        <td><p>The parent control that defines the group.</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_RadioButtons_AddToGroup_DrawnUi_Controls_ISkiaRadioButton_System_String_.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.RadioButtons.AddToGroup(DrawnUi.Controls.ISkiaRadioButton%2CSystem.String)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L142">View Source</a>
  </span>
  <a id="DrawnUi_Controls_RadioButtons_AddToGroup_" data-uid="DrawnUi.Controls.RadioButtons.AddToGroup*"></a>
  <h4 id="DrawnUi_Controls_RadioButtons_AddToGroup_DrawnUi_Controls_ISkiaRadioButton_System_String_" data-uid="DrawnUi.Controls.RadioButtons.AddToGroup(DrawnUi.Controls.ISkiaRadioButton,System.String)">AddToGroup(ISkiaRadioButton, string)</h4>
  <div class="markdown level1 summary"><p>Adds a radio button control to a named group. Ensures at least one button in the group is selected.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AddToGroup(ISkiaRadioButton control, string groupName)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Controls.ISkiaRadioButton.html">ISkiaRadioButton</a></td>
        <td><span class="parametername">control</span></td>
        <td><p>The radio button control to add to the group.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td><span class="parametername">groupName</span></td>
        <td><p>The name of the group to add the control to.</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_RadioButtons_GetSelected_DrawnUi_Draw_SkiaControl_.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.RadioButtons.GetSelected(DrawnUi.Draw.SkiaControl)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L35">View Source</a>
  </span>
  <a id="DrawnUi_Controls_RadioButtons_GetSelected_" data-uid="DrawnUi.Controls.RadioButtons.GetSelected*"></a>
  <h4 id="DrawnUi_Controls_RadioButtons_GetSelected_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Controls.RadioButtons.GetSelected(DrawnUi.Draw.SkiaControl)">GetSelected(SkiaControl)</h4>
  <div class="markdown level1 summary"><p>Gets the currently selected radio button in the group associated with the specified parent control.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl GetSelected(SkiaControl parent)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></td>
        <td><span class="parametername">parent</span></td>
        <td><p>The parent control that defines the radio button group.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></td>
        <td><p>The selected SkiaControl, or null if no button is selected or group doesn't exist.</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_RadioButtons_GetSelected_System_String_.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.RadioButtons.GetSelected(System.String)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L51">View Source</a>
  </span>
  <a id="DrawnUi_Controls_RadioButtons_GetSelected_" data-uid="DrawnUi.Controls.RadioButtons.GetSelected*"></a>
  <h4 id="DrawnUi_Controls_RadioButtons_GetSelected_System_String_" data-uid="DrawnUi.Controls.RadioButtons.GetSelected(System.String)">GetSelected(string)</h4>
  <div class="markdown level1 summary"><p>Gets the currently selected radio button in the group with the specified name.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl GetSelected(string groupName)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td><span class="parametername">groupName</span></td>
        <td><p>The name of the radio button group.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></td>
        <td><p>The selected SkiaControl, or null if no button is selected or group doesn't exist.</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_RadioButtons_GetSelectedIndex_DrawnUi_Draw_SkiaControl_.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.RadioButtons.GetSelectedIndex(DrawnUi.Draw.SkiaControl)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L67">View Source</a>
  </span>
  <a id="DrawnUi_Controls_RadioButtons_GetSelectedIndex_" data-uid="DrawnUi.Controls.RadioButtons.GetSelectedIndex*"></a>
  <h4 id="DrawnUi_Controls_RadioButtons_GetSelectedIndex_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Controls.RadioButtons.GetSelectedIndex(DrawnUi.Draw.SkiaControl)">GetSelectedIndex(SkiaControl)</h4>
  <div class="markdown level1 summary"><p>Gets the index of the currently selected radio button in the group associated with the specified parent control.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int GetSelectedIndex(SkiaControl parent)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></td>
        <td><span class="parametername">parent</span></td>
        <td><p>The parent control that defines the radio button group.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><p>The zero-based index of the selected button, or -1 if no button is selected or group doesn't exist.</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_RadioButtons_GetSelectedIndex_System_String_.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.RadioButtons.GetSelectedIndex(System.String)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L82">View Source</a>
  </span>
  <a id="DrawnUi_Controls_RadioButtons_GetSelectedIndex_" data-uid="DrawnUi.Controls.RadioButtons.GetSelectedIndex*"></a>
  <h4 id="DrawnUi_Controls_RadioButtons_GetSelectedIndex_System_String_" data-uid="DrawnUi.Controls.RadioButtons.GetSelectedIndex(System.String)">GetSelectedIndex(string)</h4>
  <div class="markdown level1 summary"><p>Gets the index of the currently selected radio button in the group with the specified name.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int GetSelectedIndex(string groupName)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td><span class="parametername">groupName</span></td>
        <td><p>The name of the radio button group.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><p>The zero-based index of the selected button, or -1 if no button is selected or group doesn't exist.</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_RadioButtons_RemoveFromGroup_DrawnUi_Draw_SkiaControl_DrawnUi_Controls_ISkiaRadioButton_.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.RadioButtons.RemoveFromGroup(DrawnUi.Draw.SkiaControl%2CDrawnUi.Controls.ISkiaRadioButton)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L186">View Source</a>
  </span>
  <a id="DrawnUi_Controls_RadioButtons_RemoveFromGroup_" data-uid="DrawnUi.Controls.RadioButtons.RemoveFromGroup*"></a>
  <h4 id="DrawnUi_Controls_RadioButtons_RemoveFromGroup_DrawnUi_Draw_SkiaControl_DrawnUi_Controls_ISkiaRadioButton_" data-uid="DrawnUi.Controls.RadioButtons.RemoveFromGroup(DrawnUi.Draw.SkiaControl,DrawnUi.Controls.ISkiaRadioButton)">RemoveFromGroup(SkiaControl, ISkiaRadioButton)</h4>
  <div class="markdown level1 summary"><p>Removes a radio button control from a parent-based group. Ensures at least one button remains selected in the group.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RemoveFromGroup(SkiaControl parent, ISkiaRadioButton control)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></td>
        <td><span class="parametername">parent</span></td>
        <td><p>The parent control that defines the group to remove the control from.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="DrawnUi.Controls.ISkiaRadioButton.html">ISkiaRadioButton</a></td>
        <td><span class="parametername">control</span></td>
        <td><p>The radio button control to remove.</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_RadioButtons_RemoveFromGroup_System_String_DrawnUi_Controls_ISkiaRadioButton_.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.RadioButtons.RemoveFromGroup(System.String%2CDrawnUi.Controls.ISkiaRadioButton)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L172">View Source</a>
  </span>
  <a id="DrawnUi_Controls_RadioButtons_RemoveFromGroup_" data-uid="DrawnUi.Controls.RadioButtons.RemoveFromGroup*"></a>
  <h4 id="DrawnUi_Controls_RadioButtons_RemoveFromGroup_System_String_DrawnUi_Controls_ISkiaRadioButton_" data-uid="DrawnUi.Controls.RadioButtons.RemoveFromGroup(System.String,DrawnUi.Controls.ISkiaRadioButton)">RemoveFromGroup(string, ISkiaRadioButton)</h4>
  <div class="markdown level1 summary"><p>Removes a radio button control from a named group. Ensures at least one button remains selected in the group.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RemoveFromGroup(string groupName, ISkiaRadioButton control)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td><span class="parametername">groupName</span></td>
        <td><p>The name of the group to remove the control from.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="DrawnUi.Controls.ISkiaRadioButton.html">ISkiaRadioButton</a></td>
        <td><span class="parametername">control</span></td>
        <td><p>The radio button control to remove.</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_RadioButtons_RemoveFromGroups_DrawnUi_Controls_ISkiaRadioButton_.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.RadioButtons.RemoveFromGroups(DrawnUi.Controls.ISkiaRadioButton)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L199">View Source</a>
  </span>
  <a id="DrawnUi_Controls_RadioButtons_RemoveFromGroups_" data-uid="DrawnUi.Controls.RadioButtons.RemoveFromGroups*"></a>
  <h4 id="DrawnUi_Controls_RadioButtons_RemoveFromGroups_DrawnUi_Controls_ISkiaRadioButton_" data-uid="DrawnUi.Controls.RadioButtons.RemoveFromGroups(DrawnUi.Controls.ISkiaRadioButton)">RemoveFromGroups(ISkiaRadioButton)</h4>
  <div class="markdown level1 summary"><p>Removes a radio button control from all groups it belongs to. Ensures at least one button remains selected in affected groups.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void RemoveFromGroups(ISkiaRadioButton control)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Controls.ISkiaRadioButton.html">ISkiaRadioButton</a></td>
        <td><span class="parametername">control</span></td>
        <td><p>The radio button control to remove from all groups.</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_RadioButtons_ReportValueChange_DrawnUi_Controls_ISkiaRadioButton_System_Boolean_.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.RadioButtons.ReportValueChange(DrawnUi.Controls.ISkiaRadioButton%2CSystem.Boolean)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L246">View Source</a>
  </span>
  <a id="DrawnUi_Controls_RadioButtons_ReportValueChange_" data-uid="DrawnUi.Controls.RadioButtons.ReportValueChange*"></a>
  <h4 id="DrawnUi_Controls_RadioButtons_ReportValueChange_DrawnUi_Controls_ISkiaRadioButton_System_Boolean_" data-uid="DrawnUi.Controls.RadioButtons.ReportValueChange(DrawnUi.Controls.ISkiaRadioButton,System.Boolean)">ReportValueChange(ISkiaRadioButton, bool)</h4>
  <div class="markdown level1 summary"><p>Called by radio button controls to report value changes. Manages mutual exclusion within groups and fires the Changed event.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ReportValueChange(ISkiaRadioButton control, bool newValue)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Controls.ISkiaRadioButton.html">ISkiaRadioButton</a></td>
        <td><span class="parametername">control</span></td>
        <td><p>The radio button control reporting the change.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></td>
        <td><span class="parametername">newValue</span></td>
        <td><p>The new value of the control (true for selected, false for unselected).</p>
</td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_RadioButtons_Select_DrawnUi_Draw_SkiaControl_System_Int32_.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.RadioButtons.Select(DrawnUi.Draw.SkiaControl%2CSystem.Int32)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L111">View Source</a>
  </span>
  <a id="DrawnUi_Controls_RadioButtons_Select_" data-uid="DrawnUi.Controls.RadioButtons.Select*"></a>
  <h4 id="DrawnUi_Controls_RadioButtons_Select_DrawnUi_Draw_SkiaControl_System_Int32_" data-uid="DrawnUi.Controls.RadioButtons.Select(DrawnUi.Draw.SkiaControl,System.Int32)">Select(SkiaControl, int)</h4>
  <div class="markdown level1 summary"><p>Selects the radio button at the specified index in the group associated with the container control.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Select(SkiaControl container, int index)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></td>
        <td><span class="parametername">container</span></td>
        <td><p>The parent control that defines the radio button group.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td><span class="parametername">index</span></td>
        <td><p>The zero-based index of the button to select.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h3 id="events">Events
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_RadioButtons_Changed.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.RadioButtons.Changed%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L28">View Source</a>
  </span>
  <h4 id="DrawnUi_Controls_RadioButtons_Changed" data-uid="DrawnUi.Controls.RadioButtons.Changed">Changed</h4>
  <div class="markdown level1 summary"><p>Occurs when a radio button selection changes in any group.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public event EventHandler Changed</code></pre>
  </div>
  <h5 class="eventType">Event Type</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.eventhandler">EventHandler</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Controls_RadioButtons.md&amp;value=---%0Auid%3A%20DrawnUi.Controls.RadioButtons%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A" class="contribution-link">Edit this page</a>
                  </li>
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Controls/RadioButtons/RadioButtons.cs/#L7" class="contribution-link">View Source</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>

﻿
namespace AppoMobi.Mobile.Views;

public class CardChatUnreadMessage : SkiaLayout
{
    string GetIcon(bool hasNewMessages)
    {
        if (hasNewMessages)
        {
            return App.Current.Resources.Get<string>("SvgDuoNewMessages");
        }
        else
        {
            return App.Current.Resources.Get<string>("SvgDuoNoMessages");
        }
    }

    string GetText(bool hasNewMessages)
    {
        if (hasNewMessages)
        {
            return ResStrings.ChatNewMessages;
        }
        else
        {
            return ResStrings.ChatNoMessages;
        }
    }

    public void Update(ChatGroup chat)
    {
        bool hasNewMessages = chat != null 
            && chat.UnreadCount > 0 && chat.LastMessage != null;

        var children = new List<SkiaControl>()
        {
            new SkiaSvg()
            {
                HorizontalOptions = LayoutOptions.Center,
                UseCache = SkiaCacheType.Operations,
                WidthRequest = 100,
                LockRatio = 1,
                FontAwesomePrimaryColor = AppColors.Text,
                FontAwesomeSecondaryColor = AppColors.PrimaryLight,
                SvgString = GetIcon(hasNewMessages)
            },
        };

        if (hasNewMessages)
        {
            children.Add(
                new SkiaLabel(GetText(hasNewMessages))
                {
                    TextColor = AppColors.Text,
                    FontFamily = AppFonts.SemiBold,
                    HorizontalOptions = LayoutOptions.Center
                }
            );

            children.Add(
                new SkiaShape()
                {
                    IsVisible = hasNewMessages,
                    HorizontalOptions = LayoutOptions.Center,
                    BackgroundColor = AppColors.BackgroundMinor,
                    CornerRadius = 16,
                    Children =
                    {
                        new SkiaLabel(chat.LastMessage.Text)
                        {
                            Margin = 10,
                            MaxLines = 1,
                            LineBreakMode = LineBreakMode.TailTruncation,
                            FontFamily = AppFonts.SemiBold,
                            TextColor = AppColors.Text
                        }
                    }
                }
            );
        }

        Children = children;
    }


    public CardChatUnreadMessage(ChatGroup chat)
    {
        UseCache = SkiaCacheType.Image;
        Type = LayoutType.Column;
        Spacing = 24;

        Update(chat);
    }
}
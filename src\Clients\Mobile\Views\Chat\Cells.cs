﻿using AppoMobi.Maui.Gestures;
using System.ComponentModel;
using System.Windows.Input;
using AppoMobi.Common.Enums.Project;
using AppoMobi.Framework.Maui.Touch;

namespace AppoMobi.Mobile.Views;

public class FastCellGroupChatMessage : FastCellChatMessage
{
    protected static double AvatarSize = 40.0;

    protected override void OnTapped(object sender, TouchActionEventArgs args)
    {
        var x = args.Location.X;
        var y = args.Location.Y;

        bool insideAvatar = x >= AvatarFrame.Destination.Left / RenderingScale &&
                            x <= AvatarFrame.Destination.Right / RenderingScale;

        if (insideAvatar)
        {
            if (!Model.Outgoing && Model.Player != null)
            {
                Device.StartTimer(TimeSpan.FromMilliseconds(50), () =>
                {
                    //invoke action
                    CommandAvatarTapped?.Execute(Model.Player);

                    return false;
                });
            }

            return;
        }

        base.OnTapped(sender, args);
    }

    protected override void OnLongPressing(object sender, TouchActionEventArgs args)
    {
        var x = args.Location.X;
        var y = args.Location.Y;

        bool insideAvatar = x >= AvatarFrame.Destination.Left / RenderingScale &&
                            x <= AvatarFrame.Destination.Right / RenderingScale;

        if (insideAvatar)
        {
            if (!Model.Outgoing && Model.Player != null)
            {
                Device.StartTimer(TimeSpan.FromMilliseconds(50), () =>
                {
                    //invoke action
                    CommandAvatarLongPressing?.Execute(Model.Player);

                    return false;
                });
            }

            return;
        }

        base.OnLongPressing(sender, args);
    }

    //-------------------------------------------------------------
    // CommandAvatarTapped
    //-------------------------------------------------------------
    private const string nameCommandAvatarTapped = "CommandAvatarTapped";

    public static readonly BindableProperty CommandAvatarTappedProperty = BindableProperty.Create(
        nameCommandAvatarTapped, typeof(ICommand), typeof(FastCellGroupChatMessage),
        null);

    public ICommand CommandAvatarTapped
    {
        get { return (ICommand)GetValue(CommandAvatarTappedProperty); }
        set { SetValue(CommandAvatarTappedProperty, value); }
    }

    //-------------------------------------------------------------
    // CommandAvatarLongPressing
    //-------------------------------------------------------------
    private const string nameCommandAvatarLongPressing = "CommandAvatarLongPressing";

    public static readonly BindableProperty CommandAvatarLongPressingProperty = BindableProperty.Create(
        nameCommandAvatarLongPressing, typeof(ICommand), typeof(FastCellGroupChatMessage),
        null);

    public ICommand CommandAvatarLongPressing
    {
        get { return (ICommand)GetValue(CommandAvatarLongPressingProperty); }
        set { SetValue(CommandAvatarLongPressingProperty, value); }
    }

    private SkiaShape AvatarFrame;

    protected override void CreateControls()
    {
        ShowName = true;

        // AVATAR
        AvatarFrame = new SkiaShape()
        {
            Tag = "AvatarFrame",
            Margin = new Thickness(0, 0, 4, 0),
            Type = ShapeType.Circle,
            BackgroundColor = ColorIncoming,
            WidthRequest = AvatarSize,
            HeightRequest = AvatarSize,
            //StrokeColor = Colors.Transparent,
            //StrokeWidth = 0,
            HorizontalOptions = LayoutOptions.Start,
            VerticalOptions = LayoutOptions.Start,
            FillGradient = new()
            {
                Colors = new List<Color>()
                {
                    AppColors.PrimaryLight,
                    AppColors.Primary,
                }
            },
        }.AssignParent(MainHorizontalStack);

        ImageAvatar = new SkiaImage()
        {
            Tag = "avatar",
            Aspect = TransformAspect.AspectFill,
            //TintColor = ColorIncoming,
            //Margin = new Thickness(1),
            HorizontalOptions = LayoutOptions.Fill,
            VerticalOptions = LayoutOptions.Fill,
            IsClippedToBounds = true,
            Clipping = (path, dest) =>
            {
                //avatar circle
                path.AddCircle(dest.Left + dest.Width / 2, dest.Top + dest.Height / 2,
                    Math.Min(dest.Width, dest.Height) / 2);
            }
        }.AssignParent(AvatarFrame);

        //AvatarDefault = CreateStandartEmptyAvatar(ImageAvatar, RenderingScale);
        //AvatarDefault.HeightRequest = 21;
        //AvatarDefault.WidthRequest = 21;

        AvatarDefault = new SkiaLabel()
        {
            FontFamily = AppFonts.Bold,
            Text = $"A",
            //TranslationY = -2,
            FontSize = 24,
            HorizontalOptions = LayoutOptions.Center,
            VerticalOptions = LayoutOptions.Center,
            TextColor = Color.FromHex("#33ffffff")
        }.AssignParent(AvatarFrame);

        ImageAvatar.Error += OnAvatarError;

        // ONLINE INDICATOR
        //OnlineIndicator = new SkiaShape
        //{
        //    Tag = "online",
        //    Type = ShapeType.Circle,
        //    WidthRequest = 8,
        //    HeightRequest = 8,
        //    TintColor = App.Current.Resources.ColorPaperSecondary,

        //    StrokeColor = Colors.White,
        //    StrokeWidth = 1,

        //    ShadowColor = Color.FromHex("#32000000"),

        //    ShadowY = 4,
        //    ShadowX = 0,
        //    ShadowBlur = 4,

        //    HorizontalOptions = LayoutOptions.Center,
        //    VerticalOptions = LayoutOptions.Center,
        //};
        //AvatarFrame
        //    .AddSubView(new SkiaShape
        //    {
        //        Tag = "debug",
        //        Margin = new Thickness(0, 0, 0, 0),
        //        Type = ShapeType.Circle,
        //        //     TintColor = StaticResources.DropShadow,
        //        WidthRequest = 12,
        //        HeightRequest = 12,
        //        HorizontalOptions = LayoutOptions.Start,
        //        VerticalOptions = LayoutOptions.Start
        //    })
        //    .AddSubView(OnlineIndicator);

        base.CreateControls();
    }

    public FastCellGroupChatMessage() : base()
    {
        HorizontalOptions = LayoutOptions.Fill;
        BackgroundColor = Colors.Yellow;
    }

    public override void OnDisposing()
    {
        base.OnDisposing();

        ImageAvatar.Error -= OnAvatarError;
    }

    public SkiaImage ImageAvatar { get; set; }

    //public SkiaShape OnlineIndicator { get; set; }
    public SkiaLabel AvatarDefault { get; set; }

    protected override void UpdateContainerForOutgoing(ChatMessage item)
    {
        AvatarFrame.IsVisible = false;

        base.UpdateContainerForOutgoing(item);
    }

    protected override void UpdateContainerForIncoming(ChatMessage item)
    {
        UpdateAvatar(item.Player);

        AvatarFrame.IsVisible = true;

        //AvatarFrame.IsGhost = !item.IsFirst;

        base.UpdateContainerForIncoming(item);
    }

    //protected override void UpdateContent(ChatMessage item)
    //{
    //    base.UpdateContent(item);

    //    UpdateAvatar(item.Player);
    //}

    private void OnAvatarError(object sender, EventArgs e)
    {
        AvatarDefault.IsVisible = true;
        Update();
    }

    public void UpdateAvatar(Player item)
    {
        if (item == null || item.ImageMain == null)
        {
            AvatarDefault.IsVisible = true;
            return;
        }

        var check = item.Initialized;

        if (!string.IsNullOrEmpty(item.DisplayLetter))
        {
            if (item.AvatarFormsColor == Colors.Transparent)
                item.AvatarFormsColor = Colors.Gainsboro;

            AvatarFrame.FillGradient = new()
            {
                Colors = new List<Color>()
                {
                    item.AvatarFormsColor,
                    item.AvatarFormsColor.MakeLighter(10),
                }
            };

            AvatarDefault.Text = item.DisplayLetter;
            LabelName.TextColor = item.AvatarFormsColor.WithLuminosity(0.33f);
        }

        AvatarDefault.IsVisible = false;

        var source = item.ImageMain.Normal;
        if (string.IsNullOrEmpty(source))
        {
            ImageAvatar.Source = null;
            ImageAvatar.PreviewBase64 = null;
            AvatarDefault.IsVisible = true;
        }
        else
        {
            ImageAvatar.PreviewBase64 = item.Preview;
            ImageAvatar.Source = source;
            AvatarDefault.IsVisible = false;
        }
    }
}

public class FastCellChatMessage : OldWidgetListCell
{
    const double BubblesSpacing = 10.0;
    protected static Color ColorSystemTitle = AppColors.Primary;
    protected static Color ColorIncoming = Colors.White;
    protected static Color ColorOutcoming = Colors.Gainsboro;
    protected static Color ColorSeparatorText = Colors.Gainsboro;
    protected static Color ColorText = AppColors.Text;
    protected static Color ColorTextTime = AppColors.Primary;
    protected static Color ColorCheck = AppColors.PrimaryLight;

    protected ChatMessage Model
    {
        get { return BindingContext as ChatMessage; }
    }

    //-------------------------------------------------------------
    // CommandAttachedMessageTapped
    //-------------------------------------------------------------
    private const string nameCommandAttachedMessageTapped = "CommandAttachedMessageTapped";

    public static readonly BindableProperty CommandAttachedMessageTappedProperty = BindableProperty.Create(
        nameCommandAttachedMessageTapped, typeof(ICommand),
        typeof(FastCellChatMessage),
        null);

    public ICommand CommandAttachedMessageTapped
    {
        get { return (ICommand)GetValue(CommandAttachedMessageTappedProperty); }
        set { SetValue(CommandAttachedMessageTappedProperty, value); }
    }

    //-------------------------------------------------------------
    // CommandEmptySpaceTapped
    //-------------------------------------------------------------
    private const string nameCommandEmptySpaceTapped = "CommandEmptySpaceTapped";

    public static readonly BindableProperty CommandEmptySpaceTappedProperty = BindableProperty.Create(
        nameCommandEmptySpaceTapped, typeof(ICommand),
        typeof(FastCellChatMessage),
        null);

    public ICommand CommandEmptySpaceTapped
    {
        get { return (ICommand)GetValue(CommandEmptySpaceTappedProperty); }
        set { SetValue(CommandEmptySpaceTappedProperty, value); }
    }

    protected override void OnTapped(object sender, TouchActionEventArgs args)
    {
        var x = args.Location.X;
        var y = args.Location.Y;

        bool insideMessage = x >= MainFrame.Destination.Left / RenderingScale &&
                             x <= MainFrame.Destination.Right / RenderingScale;
        bool insideReply = AttachedMessageStack.IsVisible && insideMessage &&
                           y >= AttachedMessageStack.Destination.Top / RenderingScale &&
                           y <= AttachedMessageStack.Destination.Bottom / RenderingScale;

        if (insideReply)
        {
            CommandAttachedMessageTapped.Execute(Model.AttachedMessage);
            //return;
        }

        if (!insideMessage)
        {
            App.Instance.Messager.All(AppMessages.Chat, "Clicked"); //todo move

            CommandEmptySpaceTapped.Execute(Model);
            // return;
        }

        base.OnTapped(sender, args);
    }

    protected virtual void CreateControls()
    {
        FrameNewDate = new SkiaShape()
        {
            Tag = "FrameNewDate",
            Type = ShapeType.Rectangle,
            CornerRadius = 8,
            HeightRequest = 24,
            WidthRequest = 100,
            HorizontalOptions = LayoutOptions.Center,
            Margin = new Thickness(8, 8, 8, 8),
        }.AssignParent(this);

        LabelFirstDate = new SkiaLabel()
        {
            TranslationY = -4,
            Margin = new Thickness(10, 0, 10, 0),
            LineBreakMode = LineBreakMode.NoWrap,
            MaxLines = 1,
            FontFamily = AppFonts.Bold,
            Text = $"Time",
            FontSize = 10,
            HorizontalOptions = LayoutOptions.Center,
            VerticalOptions = LayoutOptions.Center,
            TextColor = ColorSeparatorText,
        }.AssignParent(FrameNewDate).Adapt((me) => { me.Measured += OnFirstTimeMeasured; });


        //StackBubble = new SkiaLayout(Stack)
        //{
        //    Margin = new Thickness(8, 0, 8, 0),
        //    Spacing = 0,
        //    VerticalOptions = LayoutOptions.Fill,
        //    HorizontalOptions = LayoutOptions.End
        //};

        BubbleArrowIncoming = new SkiaSvg()
        {
            Tag = "arrowL",
            TranslationY = 6,
            Aspect = TransformAspect.AspectFit,
            TintColor = ColorIncoming,
            SvgString = App.Current.Resources.Get<string>("SvgChatFromLeft"),
            VerticalOptions = LayoutOptions.Start,
            HorizontalOptions = LayoutOptions.Start,
            HeightRequest = 7,
            WidthRequest = 7
        }.AssignParent(MainHorizontalStack);

        #region BUBBLE

        MainFrame = new SkiaShape()
        {
            Tag = "MainFrame",
            Margin = new Thickness(0, 0, 0, BubblesSpacing),
            CornerRadius = 8,
            BackgroundColor = ColorOutcoming,
            //           StrokeColor = StaticResources.ColorPaperSecondary,
            HorizontalOptions = LayoutOptions.Fill,
            VerticalOptions = LayoutOptions.Fill,
        }.AssignParent(MainHorizontalStack);

        AttachedMessageStack = new SkiaLayout()
        {
            BackgroundColor = Color.FromHex("#11000000"),
            Tag = "AttStack",
            IsVisible = false,
            IsClippedToBounds = true,
            Spacing = 0,
            VerticalOptions = LayoutOptions.Start,
            HeightRequest = 58,
            HorizontalOptions = LayoutOptions.Start
        }.AssignParent(MainFrame);

        AttachedMessageStack.AddSubView(new SkiaShape()
        {
            Margin = new Thickness(8, 8, 0, 8),
            CornerRadius = 0,
            BackgroundColor = ColorSystemTitle,
            HorizontalOptions = LayoutOptions.Start,
            WidthRequest = 2,
            VerticalOptions = LayoutOptions.Fill,
        });

        MessageStack = new SkiaLayout()
        {
            BackgroundColor = Colors.Pink,

            Tag = "MessageStack",
            IsClippedToBounds = true,
            Type = LayoutType.Column,
            Spacing = 0,
            VerticalOptions = LayoutOptions.Fill,
            HorizontalOptions = LayoutOptions.Start
        }.AssignParent(MainFrame);

        Banner = new SkiaImage()
        {
            IsVisible = false,
            Tag = "Banner",
            Aspect = TransformAspect.AspectCover,
            BackgroundColor = Colors.White,
            //Margin = new Thickness(0, 0, 0, 0),
            HorizontalOptions = LayoutOptions.Fill,
            VerticalOptions = LayoutOptions.Start,
            HeightRequest = 200,
            IsClippedToBounds = true,
            //ClipCustom = (path, dest) =>
            //{
            //    //avatar circle
            //    path.AddCircle(dest.Left + dest.Width / 2, dest.Top + dest.Height / 2,
            //        dest.Width / 2);
            //}
        }.AssignParent(MessageStack);

        Banner.Error += OnImageError;

        IconAttachment = new SkiaSvg()
        {
            Tag = "Attachment",
            Margin = new Thickness(8, 0, 0, 0),
            Aspect = TransformAspect.AspectFit,
            SvgString = App.Current.Resources.Get<string>("SvgAttachment"),
            VerticalOptions = LayoutOptions.Center,
            HorizontalOptions = LayoutOptions.Start,
            TintColor = AppColors.AccentLight,
            HeightRequest = 16,
            WidthRequest = 16
        }.AssignParent(MessageStack);


        LabelNameAttached = new SkiaLabel()
        {
            Tag = "AName",
            LineBreakMode = LineBreakMode.TailTruncation,
            MaxLines = 1,
            FontFamily = AppFonts.Normal,
            Text = $"APlayer",
            FontSize = 15,
            HorizontalOptions = LayoutOptions.Start,
            Margin = new Thickness(16, 8, 8, 0),
            VerticalOptions = LayoutOptions.Start,
            TextColor = ColorSystemTitle,
        }.AssignParent(AttachedMessageStack);

        LabelMessageAttached = new SkiaLabel()
        {
            Tag = "AttMessage",
            LineBreakMode = LineBreakMode.TailTruncation,
            MaxLines = 1,
            FontFamily = AppFonts.Normal,
            Text = $"...",
            FontSize = 15,
            HorizontalOptions = LayoutOptions.Fill,
            Margin = new Thickness(16, 28, 8, 0),
            VerticalOptions = LayoutOptions.Start,
            TextColor = ColorText,
        }.AssignParent(AttachedMessageStack);

        LabelName = new SkiaLabel()
        {
            Tag = "Name",
            LineBreakMode = LineBreakMode.TailTruncation,
            MaxLines = 1,
            FontFamily = AppFonts.Normal,
            Text = $"Player",
            FontSize = 15,
            HorizontalOptions = LayoutOptions.Start,
            Margin = new Thickness(8, 8, 8, 0),
            VerticalOptions = LayoutOptions.Start,
            TextColor = ColorSystemTitle,
        }.AssignParent(MessageStack);

        LabelMessage = new SkiaLabel()
            {
                Tag = "Message",
                //TintColor = Colors.FloralWhite,
                LineBreakMode = LineBreakMode.WordWrap,
                MaxLines = -1,
                FontFamily = AppFonts.Normal,
                Text = $"Message",
                FontSize = 15,
                HorizontalOptions = LayoutOptions.Fill,
                Margin = new Thickness(8, 8, 8, 11),
                VerticalOptions = LayoutOptions.Start,
                TextColor = ColorText,
            }.AssignParent(MessageStack)
            .AssignParent(FrameNewDate).Adapt((me) => { me.Measured += OnTextSizeChanged; });


        LabelTime = new SkiaLabel()
        {
            Tag = "LabelTime",
            LineBreakMode = LineBreakMode.NoWrap,
            MaxLines = 1,
            FontFamily = AppFonts.Normal,
            Text = $"Time",
            FontSize = 9,
            HorizontalOptions = LayoutOptions.End,
            Margin = new Thickness(0, 0, 2, 2),
            VerticalOptions = LayoutOptions.End,
            TextColor = ColorTextTime,
        }.AssignParent(MainFrame);

        IconWasSent = new SkiaSvg()
        {
            Tag = "WasDelivered",
            //TranslationY = 5,
            Aspect = TransformAspect.AspectFit,
            Margin = new Thickness(0, 0, 7, 3),
            TintColor = ColorCheck,
            SvgString = App.Current.Resources.Get<string>("SvgCheck"),
            VerticalOptions = LayoutOptions.End,
            HorizontalOptions = LayoutOptions.End,
            HeightRequest = 11,
            WidthRequest = 11
        }.AssignParent(MainFrame);

        IconWasDelivered = new SkiaSvg()
        {
            Tag = "WasSeen",
            //TranslationY = 5,
            Aspect = TransformAspect.AspectFit,
            Margin = new Thickness(0, 0, 3, 3),
            TintColor = ColorCheck,
            SvgString = App.Current.Resources.Get<string>("SvgCheck"),
            VerticalOptions = LayoutOptions.End,
            HorizontalOptions = LayoutOptions.End,
            HeightRequest = 11,
            WidthRequest = 11
        }.AssignParent(MainFrame);

        #endregion

        BubbleArrowOutcoming = new SkiaSvg()
        {
            Tag = "arrowR",
            TranslationY = 6,
            TintColor = ColorOutcoming,
            Aspect = TransformAspect.AspectFit,
            SvgString = App.Current.Resources.Get<string>("SvgChatFromRight"),
            VerticalOptions = LayoutOptions.Start,
            HorizontalOptions = LayoutOptions.Start,
            HeightRequest = 7,
            WidthRequest = 7
        }.AssignParent(MainHorizontalStack);
    }

    public FastCellChatMessage()
    {
        //we gonna set heightrequest manually after measuring text

        MainHorizontalStack = new SkiaLayout()
        {
            Tag = "MainHorizontalStack",
            Type = LayoutType.Row,
            Padding = new Thickness(8, 0, 8, 0),
            AlignContentHorizontal = LayoutOptions.End, //todo
            Spacing = 0,
            VerticalOptions = LayoutOptions.Fill,
            HorizontalOptions = LayoutOptions.End
        }.AssignParent(this);

        LabelDebug = new SkiaLabel()
        {
            IsVisible = false,
            BackgroundColor = Color.FromHex("#66000000"),
            Margin = new Thickness(10),
            Padding = new Thickness(8),
            LineBreakMode = LineBreakMode.NoWrap,
            MaxLines = 1,
            FontFamily = AppFonts.Bold,
            Text = $"...",
            FontSize = 10,
            HorizontalOptions = LayoutOptions.Start,
            VerticalOptions = LayoutOptions.End,
            TextColor = Colors.White,
        }.AssignParent(this);

        CreateControls();


        // GESTURES
        AttachGestures();
    }

    public SkiaShape FrameNewDate { get; set; }
    public SkiaLabel LabelFirstDate { get; set; }
    public SkiaLabel LabelDebug { get; set; }
    public SkiaLayout MainHorizontalStack { get; set; }
    public SkiaLayout MessageStack { get; set; }

    //public SkiaLayout StackBubble { get; set; }
    public SkiaImage Banner { get; set; }
    public SkiaLabel LabelMessage { get; set; }
    public SkiaLabel LabelName { get; set; }
    public SkiaLayout AttachedMessageStack { get; set; }

    //public SkiaShape FrameAttached { get; set; }
    public SkiaLabel LabelNameAttached { get; set; }
    public SkiaLabel LabelMessageAttached { get; set; }
    public SkiaLabel LabelTime { get; set; }
    public SkiaSvg IconAttachment { get; set; }
    public SkiaSvg BubbleArrowIncoming { get; set; }
    public SkiaSvg BubbleArrowOutcoming { get; set; }
    public SkiaSvg IconWasSent { get; set; }
    public SkiaSvg IconWasDelivered { get; set; }

    public override void OnDisposing()
    {
        Banner.Error -= OnImageError;

        DetachGestures();

        base.OnDisposing();
    }

    private void OnImageError(object sender, EventArgs e)
    {
        Update();
    }

    protected override void OnContextPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == "Id") //Message was sent and received id from server
        {
            SetContentFull(BindingContext as ChatMessage);
            return;
        }

        if (e.PropertyName == "Read"
            || e.PropertyName == "Sent"
            || e.PropertyName == "Delivered")
        {
            UpdateStatus(BindingContext as ChatMessage);
            Update();
        }
        else if (e.PropertyName == "Notify")
        {
            var item = BindingContext as ChatMessage;
            if (item != null)
            {
                IsNew = item.Notify;
            }
            else
            {
                IsNew = false;
            }

            UpdateContainer(BindingContext as ChatMessage);
            Update();
        }

        base.OnContextPropertyChanged(sender, e);
    }

    private void OnFirstTimeMeasured(object? sender, ScaledSize scaledSize)
    {
        var label = sender as SkiaLabel;

        FrameNewDate.WidthRequest = label.MeasuredSize.Units.Width + label.Margin.Left + label.Margin.Right;
    }

    //this is more smooth when invoked here comparing to actions inside OnBindingContextChanged
    void OnTextSizeChanged(object? sender, ScaledSize scaledSize)
    {
        var label = sender as SkiaLabel;

        var requestContainerWidth = label.MeasuredSize.Units.Width + label.Margin.Left + label.Margin.Right + 8;

        var maybeNameWider = LabelName.MeasuredSize.Units.Width + LabelName.Margin.Left + LabelName.Margin.Right + 8;
        if (maybeNameWider > requestContainerWidth)
            requestContainerWidth = maybeNameWider;

        //var maybeNameAttachedWider = LabelNameAttached.TextSizePoints.Width + LabelNameAttached.Margin.Left + LabelNameAttached.Margin.Right + 8;
        //if (maybeNameAttachedWider > requestContainerWidth)
        //    requestContainerWidth = maybeNameAttachedWider;

        //var maybeTextAttachedWider = LabelMessageAttached.TextSizePoints.Width + LabelMessageAttached.Margin.Left + LabelMessageAttached.Margin.Right + 8;
        //if (maybeTextAttachedWider > requestContainerWidth)
        //    requestContainerWidth = maybeTextAttachedWider;

        if (Template == ChatMetaType.File)
        {
            requestContainerWidth += 16 + 8 + 4;
        }

        var minHeight = 44.0 + 4;
        var minWidth = 0;


        var addHeight = 0.0;
        if (ShowDate)
        {
            addHeight = 24 + 16;
        }

        if (LabelName.IsVisible)
        {
            addHeight += 20;
        }

        if (AttachedMessageStack.IsVisible)
        {
            addHeight += AttachedMessageStack.HeightRequest;
            minWidth = 200;
        }

        if (Template == ChatMetaType.Image || Template == ChatMetaType.Video)
        {
            minWidth = 200;
            minHeight = Banner.HeightRequest + 10 + addHeight + 14;
        }

        if (requestContainerWidth < minWidth)
            requestContainerWidth = minWidth;

        if (MainFrame.Destination.Width != requestContainerWidth)
        {
            MainFrame.WidthRequest = requestContainerWidth;
        }

        double setHeight;
        var textHeight = 0.0;

        if (label.IsVisible)
        {
            textHeight = label.MeasuredSize.Units.Height + label.Margin.Top + label.Margin.Bottom;
        }
        else
        {
        }

        //set forms view dynamic height...
        if (Template == ChatMetaType.Image || Template == ChatMetaType.Video || Template == ChatMetaType.Article)
        {
            setHeight = addHeight + textHeight + Banner.HeightRequest + 10;
        }
        else
        {
            setHeight = addHeight + textHeight + 10;
        }

        if (setHeight > minHeight)
            HeightRequest = setHeight;
        else
            HeightRequest = minHeight;
    }

    protected override void Draw(DrawingContext context)
    {

        if (IsSelected)
        {
            Opacity = 0.7;
        }
        else
        {
            Opacity = 1.0;
        }

        base.Draw(context);
    }

    public bool ShowDate { get; set; }

    protected virtual void UpdateContainerForIncoming(ChatMessage item)
    {
        if (item == null)
            return;

        LabelTime.Margin = new Thickness(0, 0, 4, 2);

        //LabelTime.TextColor = App.Current.Resources.ColorPrimaryLight;
        MainFrame.BackgroundColor = ColorIncoming;

        MainHorizontalStack.HorizontalOptions = LayoutOptions.Start;
        BubbleArrowOutcoming.IsVisible = false;
        BubbleArrowIncoming.IsVisible = true;

        BubbleArrowIncoming.IsGhost = !item.IsFirst;
    }

    protected virtual void UpdateContainerForOutgoing(ChatMessage item)
    {
        if (item == null)
            return;

        LabelTime.Margin = new Thickness(0, 0, 20, 2);

        //LabelTime.TextColor = ColorTextTime;
        MainFrame.BackgroundColor = ColorOutcoming;

        MainHorizontalStack.HorizontalOptions = LayoutOptions.End;
        BubbleArrowIncoming.IsVisible = false;

        BubbleArrowOutcoming.IsVisible = true;
        BubbleArrowOutcoming.IsGhost = !item.IsFirst;
    }

    protected virtual void UpdateContainer(ChatMessage item)
    {
        if (item == null)
            return;

        var lol = 1;

        IconWasSent.IsVisible = false;
        IconWasDelivered.IsVisible = false;

        if (ShowDate)
        {
            FrameNewDate.IsVisible = true;
            MainHorizontalStack.Padding = new Thickness(0, 24 + 16, 0, 0);
        }
        else
        {
            FrameNewDate.IsVisible = false;
            MainHorizontalStack.Padding = new Thickness(0, 0, 0, 0);
        }

        if (item.Outgoing)
        {
            UpdateContainerForOutgoing(item);
        }
        else
        {
            UpdateContainerForIncoming(item);
        }

        if (Template == ChatMetaType.File)
        {
            IconAttachment.IsVisible = true;
            LabelMessage.TextColor = AppColors.AccentLight;
        }
        else
        {
            IconAttachment.IsVisible = false;
            LabelMessage.TextColor = AppColors.Text;
        }

        if (Template == ChatMetaType.Image || Template == ChatMetaType.Video)
        {
            Banner.HeightRequest = 200;
            Banner.IsVisible = true;
        }
        else if (Template == ChatMetaType.Article)
        {
            Banner.HeightRequest = 80;
            Banner.IsVisible = true;
        }
        else
        {
            Banner.IsVisible = false;
        }
    }

    protected bool ShowName { get; set; }

    protected virtual void UpdateStatus(ChatMessage item)
    {
        if (item == null)
            return;

        if (item.Outgoing)
        {
            if (item.Read)
            {
                IconWasSent.TintColor = Colors.DeepSkyBlue;
                IconWasDelivered.TintColor = Colors.DeepSkyBlue;
                IconWasSent.IsVisible = true;
                IconWasDelivered.IsVisible = true;
            }
            else
            {
                IconWasSent.TintColor = ColorCheck;
                IconWasDelivered.TintColor = ColorCheck;
                IconWasSent.IsVisible = item.Sent;
                IconWasDelivered.IsVisible = item.Delivered;
            }
        }
    }

    protected virtual void UpdateContent(ChatMessage item)
    {
        if (item == null)
            return;

        if (ShowDate)
            LabelFirstDate.Text = item.WhenDesc;
        else
            LabelFirstDate.Text = "";

        //if (Template == ChatMetaType.Image || Template == ChatMetaType.Video)
        //{
        //    LabelMessage.IsVisible = false;
        //}
        //else
        //{
        //    LabelMessage.IsVisible = true;
        //}

        var text = item.Text;
        if (item.AttachmentType == 5)
        {
            if (item.AttachedMessage != null)
            {
                //       text = $"Attached: from {item.AttachedMessage.PlayerName}, {item.AttachedMessage.Text} --- {item.Text}";
                LabelNameAttached.Text = item.AttachedMessage.PlayerName;
                LabelMessageAttached.Text = item.AttachedMessage.Text;
            }
            else
                text = $"Meta: {item.Meta}";

            AttachedMessageStack.IsVisible = true;
            MessageStack.Margin = MessageStack.Margin.WithTop(AttachedMessageStack.HeightRequest);
        }
        else
        {
            MessageStack.Margin = MessageStack.Margin.WithTop(0);
            AttachedMessageStack.IsVisible = false;
        }

        if (item.Outgoing)
        {
            LabelName.IsVisible = false;

            LabelMessage.Text = text + "           ";
        }
        else
        {
            LabelName.IsVisible = ShowName && item.IsFirst;
            LabelName.Text = item.PlayerName;

            LabelMessage.Text = text + "      ";
        }

        //LabelMessage.Measure(ContainerWidth * RenderingScale - 150 * RenderingScale, Height * RenderingScale); //reduce by side padding 

        LabelName.Measure(200 * RenderingScale, Height * RenderingScale); //reduce by side padding 

        LabelMessage.Measure(200 * RenderingScale, Height * RenderingScale); //reduce by side padding 

        LabelTime.Text = item.DisplayTime;

        if (Template == ChatMetaType.Image || Template == ChatMetaType.Video)
        {
            Banner.PreviewBase64 = item.Preview;
            Banner.Source = item.ImageMain.Large;
        }
        else if (Template == ChatMetaType.Article && item.Metadata != null)
        {
            Banner.Source = item.Metadata.Image;
        }
        else
        {
            Banner.Source = null;
            Banner.PreviewBase64 = null;
        }
    }

    public ChatMetaType Template { get; protected set; } = ChatMetaType.Default;

    protected virtual void SetContentFull(ChatMessage item)
    {
        IsNew = item.Notify;
        Template = item.PresentAs;
        if (item.Metadata != null && item.Metadata.Domain == "request")
        {
            Template = ChatMetaType.System;
            //request key is in the meta Url
        }

        ShowDate = item.IsFirstDate;
        CanBeTapped = Template != ChatMetaType.Default || Template == ChatMetaType.System;


        //if (_oldContext != null)
        //{
        //    if (_oldContext.Outgoing != item.Outgoing || _oldContext.PresentAs != item.PresentAs)
        //        UpdateBubble(item);
        //}
        //else
        //{
        //    UpdateBubble(item);
        //}
        UpdateContainer(item);
        UpdateContent(item);
        UpdateStatus(item);
        Update();
    }

    protected override void OnBindingContextChanged()
    {
        var item = this.BindingContext as ChatMessage;
        if (item != null && OldContext != item)
        {
            SetContentFull(item);
        }

        base.OnBindingContextChanged();
    }

    //we are using this to be able to measure needed size BEFORE
    //we are drawn, to avoid 2 draws: wrong size, right size

    //-------------------------------------------------------------
    // ContainerWidth
    //-------------------------------------------------------------
    private const string nameContainerWidth = "ContainerWidth";

    public static readonly BindableProperty ContainerWidthProperty =
        BindableProperty.Create(nameContainerWidth, typeof(double), typeof(FastCellChatMessage),
            100.0); //, BindingMode.TwoWay

    public double ContainerWidth
    {
        get { return (double)GetValue(ContainerWidthProperty); }
        set { SetValue(ContainerWidthProperty, value); }
    }
}

public class OldWidgetListCell : SkiaControl, ICanBeTapped
{
    public static double PresetCornerRadious { get; } = 6.0;

    protected static Color
        FrameBorderColor =
            Color.FromHex(
                "#eeeeee"); //App.Current.Resources.Get<Color>("ColorTextSecondary");//Color.FromHex("336E7388");

    protected static Color FrameBackgroundColor = AppColors.Background;// App.Current.Resources.Get<Color>("ColorPaper");
    protected static Color FrameBackgroundColorNotify = AppColors.AccentLight;// App.Current.Resources.Get<Color>("ColorListFrameNotify");

    public override void OnDisposing()
    {
        if (OldContext != null)
        {
            OldContext.PropertyChanged -= OnContextPropertyChanged;
            OldContext = null;
        }

        base.OnDisposing();
    }

    protected override void OnBindingContextChanged()
    {
        if (OldContext != null)
            OldContext.PropertyChanged -= OnContextPropertyChanged;

        var bindable = BindingContext as INotifyPropertyChanged;
        if (bindable != null)
        {
            bindable.PropertyChanged += OnContextPropertyChanged;
            OldContext = bindable;
        }
    }

    protected virtual void OnContextPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
    }

    protected INotifyPropertyChanged OldContext;

    public static SkiaImage CreateStandartAvatar(SkiaControl parent, double scale)
    {
        return new SkiaImage()
        {
            Tag = "avatar",
            Aspect = TransformAspect.AspectFill,
            //  TintColor = App.Current.Resources.ColorPrimaryLight,

            //Margin = new Thickness(2),
            HorizontalOptions = LayoutOptions.Fill,
            VerticalOptions = LayoutOptions.Fill,
            IsClippedToBounds = true,
            Clipping = (path, dest) =>
            {
                //avatar circle
                path.AddCircle(dest.Left + dest.Width / 2, dest.Top + dest.Height / 2,
                    Math.Min(dest.Width, dest.Height) / 2);
            }
        }.AssignParent(parent);
    }

    public static SkiaSvg CreateStandartEmptyAvatar(SkiaControl parent, double scale)
    {
        return new SkiaSvg()
        {
            Tag = "EmptyAvatar",
            IsVisible = false,
            TranslationY = -2,
            IsClippedToBounds = true,
            Aspect = TransformAspect.AspectFit,
            SvgString = App.Current.Resources.Get<string>("SvgAvatarEmpty"),
            VerticalOptions = LayoutOptions.Center,
            HorizontalOptions = LayoutOptions.Center,
            HeightRequest = 32,
            WidthRequest = 32
        }.AssignParent(parent);
    }

    public static bool DisableShadows => true; //App.Settings.DisableShadows;

    //-------------------------------------------------------------
    // CommandTapped
    //-------------------------------------------------------------
    private const string nameCommandTapped = "CommandTapped";

    public static readonly BindableProperty CommandTappedProperty = BindableProperty.Create(nameCommandTapped,
        typeof(ICommand), typeof(OldWidgetListCell),
        null);

    public ICommand CommandTapped
    {
        get { return (ICommand)GetValue(CommandTappedProperty); }
        set { SetValue(CommandTappedProperty, value); }
    }

    //-------------------------------------------------------------
    // CommandLongPressing
    //-------------------------------------------------------------
    private const string nameCommandLongPressing = "CommandLongPressing";

    public static readonly BindableProperty CommandLongPressingProperty = BindableProperty.Create(
        nameCommandLongPressing, typeof(ICommand), typeof(OldWidgetListCell),
        null);

    public ICommand CommandLongPressing
    {
        get { return (ICommand)GetValue(CommandLongPressingProperty); }
        set { SetValue(CommandLongPressingProperty, value); }
    }

    public SkiaShape MainFrame { get; set; }

    public bool HasGestures
    {
        get { return _touchHandler != null; }
    }

    protected void DetachGestures()
    {
        if (!HasGestures)
            return;

        _touchHandler.Dispose();
    }

    protected void AttachGestures()
    {
        if (HasGestures)
            return;

        _touchHandler = new TouchEffect
        {
            Capture = true
        };
        _touchHandler.LongPressing += OnLongPressing;
        _touchHandler.Tapped += OnTapped;
        _touchHandler.Down += OnDown;
        _touchHandler.Up += OnUp;
        _touchHandler.TouchAction += OnTouch;
        this.Effects.Add(_touchHandler);
    }

    public bool IsSelected { get; set; }
    public bool CanBeSelected { get; set; } = true;
    public bool CanBeTapped { get; set; } = true;
    public bool IsNew { get; set; }

    protected virtual void OnLongPressing(object sender, TouchActionEventArgs args)
    {
        Debug.WriteLine($"[TOUCH] LongPressing!");

        var x = args.Location.X;
        var y = args.Location.Y;

        bool insideMessage = x >= MainFrame.Destination.Left / RenderingScale &&
                             x <= MainFrame.Destination.Right / RenderingScale;

        if (insideMessage && CommandLongPressing != null)
        {
            if (CanBeSelected)
            {
                IsSelected = true;
                Update();
            }

            Device.StartTimer(TimeSpan.FromMilliseconds(2500), () =>
            {
                if (CanBeSelected)
                {
                    IsSelected = false;
                    Update();
                }

                return false;
            });
            CommandLongPressing.Execute(BindingContext);
        }
    }

    private bool lockTap;
    private TouchEffect _touchHandler;

    protected virtual void OnTapped(object sender, TouchActionEventArgs args)
    {
        if (!CanBeTapped)
            return;

        if (lockTap && TouchEffect.LockTimeTimeMsDefault > 0)
            return;


        lockTap = true;
        if (CanBeSelected)
        {
            IsSelected = true;
            Update();
        }

        //invoke action
        Task.Run(async () =>
        {
            try
            {
                MainThread.BeginInvokeOnMainThread(() => { CommandTapped?.Execute(BindingContext); });
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            finally
            {
                if (CanBeSelected)
                    Device.StartTimer(TimeSpan.FromMilliseconds(2500), () =>
                    {
                        IsSelected = false;
                        Update();
                        return false;
                    });

                if (TouchEffect.LockTimeTimeMsDefault > 0)
                {
                    Device.StartTimer(TimeSpan.FromMilliseconds(TouchEffect.LockTimeTimeMsDefault), () =>
                    {
                        lockTap = false;
                        return false;
                    });
                }
                else
                {
                    lockTap = false;
                }
            }
        }).ConfigureAwait(false);
    }

    protected virtual void OnUp(object sender, TouchActionEventArgs args)
    {
        Debug.WriteLine($"[TOUCH] UP");

        //MainFrame.StrokeWidth = 1 / RenderingScale; // 1 precise pixel
        //MainFrame.StrokeColor = StaticResources.DropShadow;
        //Update();
    }

    protected virtual void OnDown(object sender, TouchActionEventArgs args)
    {
        Debug.WriteLine($"[TOUCH] DOWN");
    }

    protected virtual void OnTouch(object sender, TouchActionEventArgs args)
    {
        Debug.WriteLine($"[TOUCH] {args.Type} {JsonConvert.SerializeObject(args)}");
    }
}
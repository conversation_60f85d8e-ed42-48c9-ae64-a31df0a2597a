﻿using System.Threading;

namespace AppoMobi.Mobile.Views;

public class ScreenTest : AppScreen
{
    public ScreenTest()
    {
        var test = CreateButton(PrebuiltControlStyle.Cupertino);
        test.UseCache = SkiaCacheType.Image;
        test.ExpandDirtyRegion = 8;
        test.VisualEffects.Add(new DropShadowEffect()
        {
            Blur = 5,
            X = 1,
            Y = 1,
            Color = Colors.Black.WithAlpha(0.5f)
        });

        VerticalOptions = LayoutOptions.Fill;
        HorizontalOptions = LayoutOptions.Fill;
        BackgroundColor = Colors.DimGray;

        Children = new List<SkiaControl>()
        {
            new AppScroll()
            {
                VerticalOptions = LayoutOptions.Fill,
                HorizontalOptions = LayoutOptions.Fill,
                Content = new SkiaLayout()
                {
                    UseCache = SkiaCacheType.ImageComposite,
                    Padding = new Thickness(0, 16),
                    BackgroundColor = Colors.WhiteSmoke,
                    Type = LayoutType.Column,
                    Spacing = 8,
                    HorizontalOptions = LayoutOptions.Fill,
                    Children = new List<SkiaControl>()
                    {

                        new StatusBarPlaceholder(),
                        //test,

                        //new SkiaShape
                        //{
                        //    Tag = "ViewCheckOn",
                        //    Type = ShapeType.Path,
                        //    PathData = "M6 12 L10 16 L18 8",
                        //    StrokeColor = Colors.Red,
                        //    StrokeWidth = 2,
                        //    StrokeCap = SKStrokeCap.Round,
                        //    HorizontalOptions = LayoutOptions.Center,
                        //    Margin = 3,
                        //    WidthRequest = 24, 
                        //    HeightRequest = 24,
                        //},

 
                        new SkiaControl()
                        {
                            LockRatio=1,
                            WidthRequest=19,
                            BackgroundColor = Colors.Red,
                            HorizontalOptions = LayoutOptions.Center
                        },
                        new SkiaControl()
                        {
                            BackgroundColor = Colors.Red,
                            LockRatio=1,
                            WidthRequest=17,
                            HorizontalOptions = LayoutOptions.Center
                        },
                        CreateCheckBoxes(),
                        CreateSwitches(),
                        CreateButtons(),
                        CreateAppButtons(),
                        new BottomTabsPlaceholder()
               

                    }
                }
            }
        };
    }

    private SkiaLayout CreateSwitches()
    {
        return new SkiaLayout()
        {
            UseCache = SkiaCacheType.ImageComposite,

            Type = LayoutType.Column,
            Spacing = 8,
            HorizontalOptions = LayoutOptions.Fill,
            Children = new List<SkiaControl>()
            {
                CreateSwitch(PrebuiltControlStyle.Unset),
                CreateSwitch(PrebuiltControlStyle.Material),
                CreateSwitch(PrebuiltControlStyle.Cupertino),
                CreateSwitch(PrebuiltControlStyle.Windows),
            }
        };
    }

    private SkiaLayout CreateAppButtons()
    {
        return new SkiaLayout()
        {
            UseCache = SkiaCacheType.ImageComposite,

            Padding = new Thickness(0, 16),
            //BackgroundColor = Colors.Blue,
            Type = LayoutType.Column,
            Spacing = 8,
            HorizontalOptions = LayoutOptions.Fill,
            Children = new List<SkiaControl>()
            {
                CreateAppButton(BtnStyle.Disabled),
                CreateAppButton(BtnStyle.Default),
                CreateAppButton(BtnStyle.Secondary),
                CreateAppButton(BtnStyle.Accent),
                CreateAppButton(BtnStyle.Info),
                CreateAppButton(BtnStyle.Danger),
                CreateAppButton(BtnStyle.Success),
                CreateAppButton(BtnStyle.Dark),
                CreateAppButton(BtnStyle.Transparent),
                CreateAppButton(BtnStyle.Flat),
                CreateAppButton(BtnStyle.Tool),
                CreateButtonSmall(BtnStyle.Default, "Small"),
            }
        };
    }

    private SkiaSwitch CreateSwitch(PrebuiltControlStyle style = PrebuiltControlStyle.Unset)
    {
        return new SkiaSwitch()
        {
            HorizontalOptions = LayoutOptions.Center,
            ControlStyle = style
        };
    }

    private ButtonMedium CreateAppButton(BtnStyle look = BtnStyle.Default)
    {
        return new ButtonMedium()
        {
            UseCache = SkiaCacheType.Image,
            HorizontalOptions = LayoutOptions.Center,
            WidthRequest = 250,
            HeightRequest = 44,
            Text = $"{look}",
            Look = look
        };
    }

    private ButtonMedium CreateButtonSmall(BtnStyle look = BtnStyle.Default, string caption=null)
    {
        var text = caption == null ? $"{look}" : caption;
        return new ButtonSmall()
        {
            UseCache = SkiaCacheType.Image,
            HorizontalOptions = LayoutOptions.Center,
            WidthRequest = 150,
            //HeightRequest = 41,
            Text = text,
            Look = look
        };
    }

    private SkiaLayout CreateButtons()
    {
        return new SkiaLayout()
        {
            UseCache = SkiaCacheType.ImageComposite,
            ExpandDirtyRegion = 1,
            Type = LayoutType.Column,
            Spacing = 8,
            HorizontalOptions = LayoutOptions.Fill,
            Children = new List<SkiaControl>()
            {
                CreateButton(PrebuiltControlStyle.Unset),
                CreateButton(PrebuiltControlStyle.Material),
                CreateButton(PrebuiltControlStyle.Cupertino),
                CreateButton(PrebuiltControlStyle.Windows),
            }
        };
    }

    private SkiaLayout CreateCheckBoxes()
    {
        return new SkiaLayout()
        {
            UseCache = SkiaCacheType.ImageComposite,

            Type = LayoutType.Column,
            Spacing = 8,
            HorizontalOptions = LayoutOptions.Fill,
            Children = new List<SkiaControl>()
            {
                CreateCheckBox(PrebuiltControlStyle.Unset),
                CreateCheckBox(PrebuiltControlStyle.Material),
                CreateCheckBox(PrebuiltControlStyle.Cupertino),
                CreateCheckBox(PrebuiltControlStyle.Windows),
            }
        };
    }

    private SkiaCheckbox CreateCheckBox(PrebuiltControlStyle style = PrebuiltControlStyle.Unset)
    {
        return new ()
        {
            HorizontalOptions = LayoutOptions.Center,
            ControlStyle = style
        };
    }

    private SkiaButton CreateButton(PrebuiltControlStyle style = PrebuiltControlStyle.Unset)
    {
        return new SkiaButton()
        {
            HorizontalOptions = LayoutOptions.Center,
            ControlStyle = style,
            Text = $"{style}"
        };
    }

    public override void OnWillDisposeWithChildren()
    {
        base.OnWillDisposeWithChildren();

        Debug.WriteLine("OnWillDisposeWithChildren all good");
    }
}
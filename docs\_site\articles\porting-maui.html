<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Porting Native to Drawn with DrawnUi | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Porting Native to Drawn with DrawnUi | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="../toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">
        <div class="article row grid">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="porting-native-to-drawn-with-drawnui" sourcefile="articles/porting-maui.md" sourcestartlinenumber="1">Porting Native to Drawn with DrawnUi</h1>

<p sourcefile="articles/porting-maui.md" sourcestartlinenumber="3">There can come a time when You feel that some complex parts of the app are not rendering the way You wish, or you cannot implement some UI with out-of-the box native controls. At the same time You want to stay with MAUI and not rewrite the app in something else.<br>
We can then replace chunks of our UI with drawn controls. Or for the whole app.</p>
<p sourcefile="articles/porting-maui.md" sourcestartlinenumber="6">Why even bother?</p>
<ul sourcefile="articles/porting-maui.md" sourcestartlinenumber="8">
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="8"><p sourcefile="articles/porting-maui.md" sourcestartlinenumber="8"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="8">You want complex layouts not to affect your app performance</strong><br>
<em sourcefile="articles/porting-maui.md" sourcestartlinenumber="9">In some scenarios native layouts can be slower than drawn ones. Like 5 horses vs a car with a 5 horse power engine: for example, app has to handle 5 natives views instead of just 1 - the Canvas. Rasterized caching makes shadows and other heavy-duty elements never affect your performance.</em></p>
</li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="11"><p sourcefile="articles/porting-maui.md" sourcestartlinenumber="11"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="11">Your designer gave you something to implement that pre-built controls can't handle</strong><br>
<em sourcefile="articles/porting-maui.md" sourcestartlinenumber="12">DrawnUi is designed with freedom in mind, to be able to draw just about anything you can imagine. With direct access to canvas you can achieve exactly your unique result.</em></p>
</li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="14"><p sourcefile="articles/porting-maui.md" sourcestartlinenumber="14"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="14">You want consistency across platforms</strong><br>
<em sourcefile="articles/porting-maui.md" sourcestartlinenumber="15">On all platforms the rendering is done with same logic, make you certain that font, controls and layouts will render the same way.</em></p>
</li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="17"><p sourcefile="articles/porting-maui.md" sourcestartlinenumber="17"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="17">You want to be in control</strong><br>
<em sourcefile="articles/porting-maui.md" sourcestartlinenumber="18">DrawnUi is a lightweight open-source project that can be directly referenced and customized up to your app needs. When you meet a bug you can can hotfix it in the engine source code, and if you miss some property/control You can easily add them.</em></p>
</li>
</ul>
<p sourcefile="articles/porting-maui.md" sourcestartlinenumber="20">This guide will help you port your existing native controls to DrawnUi.</p>
<h2 id="prerequisites" sourcefile="articles/porting-maui.md" sourcestartlinenumber="22">Prerequisites</h2>
<p sourcefile="articles/porting-maui.md" sourcestartlinenumber="24">First please follow the Getting <a href="getting-started.html" sourcefile="articles/porting-maui.md" sourcestartlinenumber="24">Started guide</a> to setup your project for DrawnUi.</p>
<h2 id="the-theory" sourcefile="articles/porting-maui.md" sourcestartlinenumber="26">The theory</h2>
<p sourcefile="articles/porting-maui.md" sourcestartlinenumber="28">To replace native controls with DrawnUi ones would take several steps:</p>
<ol sourcefile="articles/porting-maui.md" sourcestartlinenumber="30">
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="30"><p sourcefile="articles/porting-maui.md" sourcestartlinenumber="30">Put used images inside <code sourcefile="articles/porting-maui.md" sourcestartlinenumber="30">Resources/Raw</code> folder.</p>
</li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="32"><p sourcefile="articles/porting-maui.md" sourcestartlinenumber="32">Create copies of your existing views</p>
</li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="34"><p sourcefile="articles/porting-maui.md" sourcestartlinenumber="34">Replace native views names with DrawnUi ones.</p>
</li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="36"><p sourcefile="articles/porting-maui.md" sourcestartlinenumber="36">Fix properties/event handlers mismatch</p>
</li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="38"><p sourcefile="articles/porting-maui.md" sourcestartlinenumber="38">Optimize: add caching etc</p>
</li>
</ol>
<h2 id="native-vs-drawn-names-table" sourcefile="articles/porting-maui.md" sourcestartlinenumber="40">Native vs Drawn names table</h2>
<p sourcefile="articles/porting-maui.md" sourcestartlinenumber="42">There are some direct alternatives to native controls You can use. At the same time now that you can &quot;draw&quot; you controls You can create your own controls from scratch.<br>
You can also just place MAUI controls over the canvas if You need to stick with native, use <code sourcefile="articles/porting-maui.md" sourcestartlinenumber="43">SkiaMauiElement</code> as wrapper for them.</p>
<table sourcefile="articles/porting-maui.md" sourcestartlinenumber="45">
<thead>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="45">
<th sourcefile="articles/porting-maui.md" sourcestartlinenumber="45">Native MAUI Control</th>
<th sourcefile="articles/porting-maui.md" sourcestartlinenumber="45">DrawnUi Equivalent</th>
<th sourcefile="articles/porting-maui.md" sourcestartlinenumber="45">Notes</th>
</tr>
</thead>
<tbody>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="47">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="47"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="47">Layout Controls</strong></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="48">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="48"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="48">Frame</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="48"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="48">SkiaFrame</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="48">Alias for SkiaShape with Rectangle type</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="49">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="49"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="49">VerticalStackLayout</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="49"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="49">SkiaStack</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="49">Alias for SkiaLayout type Column with horizontal Fill</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="50">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="50"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="50">HorizontalStackLayout</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="50"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="50">SkiaRow</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="50">Alias for SkiaLayout type Row</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="51">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="51"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="51">AbsoluteLayout</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="51">❌ Do not use</td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="51">See &quot;Grid (single cell)&quot; instead</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="52">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="52"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="52">Grid</code> (single row/col)</td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="52"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="52">SkiaLayer</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="52">Layering controls one over another with alignements</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="53">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="53"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="53">Grid</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="53"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="53">SkiaGrid</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="53">Grid supporting children alignements</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="54">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="54"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="54">StackLayout</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="54"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="54">SkiaLayout</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="54">Use Type=&quot;Column&quot; or Type=&quot;Row&quot;</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="55">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="55"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="55">FlexLayout</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="55"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="55">SkiaLayout</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="55">Use Type=&quot;Wrap&quot;</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="56">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="56"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="56">ScrollView</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="56"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="56">SkiaScroll</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="56">Scrolling container with virtualization</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="57">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="57"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="57">Text Controls</strong></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="58">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="58"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="58">Label</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="58"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="58">SkiaLabel</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="58">Renders unicode, spans support</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="59">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="59"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="59">Label</code> (with markdown)</td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="59"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="59">SkiaMarkdownLabel</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="59">For complex formatting, emojis, different languages, auto-finds fonts</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="60">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="60"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="60">Input Controls</strong></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="61">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="61"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="61">Entry</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="61"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="61">SkiaMauiEntry</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="61">Native entry wrapped for DrawnUi</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="62">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="62"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="62">Editor</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="62"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="62">SkiaMauiEditor</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="62">Native editor wrapped for DrawnUi</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="63">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="63"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="63">Button Controls</strong></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="64">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="64"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="64">Button</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="64"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="64">SkiaButton</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="64">Platform-specific styling via ControlStyle</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="65">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="65"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="65">Toggle Controls</strong></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="66">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="66"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="66">Switch</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="66"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="66">SkiaSwitch</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="66">Platform-styled toggle switch</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="67">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="67"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="67">CheckBox</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="67"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="67">SkiaCheckbox</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="67">Platform-styled checkbox</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="68">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="68"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="68">RadioButton</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="68"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="68">SkiaRadioButton</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="68">Subclassed from SkiaToggle</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="69">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="69"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="69">Image Controls</strong></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="70">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="70"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="70">Image</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="70"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="70">SkiaImage</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="70">High-performance image rendering</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="71">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="71"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="71">Media Controls</strong></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="72">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="72"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="72">Image</code> (media)</td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="72"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="72">SkiaMediaImage</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="72">Subclassed SkiaImage for media</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="73">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="73"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="73">Graphics Controls</strong></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="74">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="74">N/A</td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="74"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="74">SkiaSvg</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="74">SVG rendering support</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="75">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="75">N/A</td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="75"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="75">SkiaGif</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="75">Animated GIF support</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="76">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="76">N/A</td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="76"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="76">SkiaLottie</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="76">Lottie animation support</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="77">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="77"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="77">Shapes Controls</strong></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="78">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="78"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="78">Frame</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="78"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="78">SkiaShape</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="78">Container with border</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="79">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="79"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="79">Border</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="79"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="79">SkiaShape</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="79">Border decoration</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="80">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="80"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="80">Ellipse</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="80"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="80">SkiaShape</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="80">Ellipse shape</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="81">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="81"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="81">Line</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="81"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="81">SkiaShape</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="81">Line shape</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="82">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="82"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="82">Path</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="82"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="82">SkiaShape</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="82">Vector path shape</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="83">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="83"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="83">Polygon</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="83"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="83">SkiaShape</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="83">Polygon shape</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="84">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="84"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="84">Polyline</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="84"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="84">SkiaShape</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="84">Polyline shape</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="85">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="85"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="85">Rectangle</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="85"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="85">SkiaShape</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="85">Rectangle shape</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="86">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="86"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="86">RoundRectangle</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="86"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="86">SkiaShape</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="86">Rounded rectangle</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="87">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="87"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="87">Navigation Controls</strong></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="88">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="88"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="88">Shell</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="88"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="88">SkiaShell</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="88">Navigation framework</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="89">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="89"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="89">TabbedPage</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="89"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="89">SkiaViewSwitcher</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="89">View switching functionality</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="90">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="90"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="90">Scroll recycled cells</strong></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="91">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="91"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="91">CollectionView</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="91"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="91">SkiaScroll</code>+<code sourcefile="articles/porting-maui.md" sourcestartlinenumber="91">SkiaLayout</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="91">Virtualized item collection</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="92">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="92"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="92">ListView</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="92"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="92">SkiaScroll</code>+<code sourcefile="articles/porting-maui.md" sourcestartlinenumber="92">SkiaLayout</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="92">Simple item list</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="93">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="93"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="93">Specialized Controls</strong></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="94">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="94">N/A</td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="94"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="94">SkiaDecoratedGrid</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="94">Grid with shape drawing between cells</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="95">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="95"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="95">CarouselView</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="95"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="95">SkiaCarousel</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="95">Swipeable carousel with snap points</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="96">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="96"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="96">SwipeView</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="96"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="96">SkiaDrawer</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="96">Swipe actions on items</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="97">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="97"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="97">RefreshView</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="97"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="97">LottieRefreshIndicator</code>/anything</td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="97">Pull-to-refresh functionality</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="98">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="98"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="98">ActivityIndicator</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="98"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="98">LottieRefreshIndicator</code>/anything</td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="98">Loading/busy indicator</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="99">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="99"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="99">Map</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="99"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="99">SkiaMapsUi</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="99">Map control, SkiaMapsUi addon</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="100">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="100">N/A</td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="100"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="100">SkiaDrawer</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="100">Swipe-in/out panel</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="101">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="101">N/A</td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="101"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="101">SkiaCamera</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="101">Mlti-platform camera, SkiaCamera addon</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="102">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="102"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="102">Use native (wrap over canvas)</strong></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="1"></td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="103">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="103"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="103">WebView</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="103"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="103">SkiaMauiElement</code>+<code sourcefile="articles/porting-maui.md" sourcestartlinenumber="103">WebView</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="103">wrap native over the canvas</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="104">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="104"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="104">MediaElement</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="104"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="104">SkiaMauiElement</code>+<code sourcefile="articles/porting-maui.md" sourcestartlinenumber="104">MediaElement</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="104">Video/audio playback</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="105">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="105"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="105">Picker</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="105"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="105">SkiaMauiElement</code>+<code sourcefile="articles/porting-maui.md" sourcestartlinenumber="105">Picker</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="105">Dropdown selection, create custom</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="106">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="106"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="106">DatePicker</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="106"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="106">SkiaMauiElement</code>+<code sourcefile="articles/porting-maui.md" sourcestartlinenumber="106">DatePicker</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="106">Date selection control, create custom</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="107">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="107"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="107">TimePicker</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="107"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="107">SkiaMauiElement</code>+<code sourcefile="articles/porting-maui.md" sourcestartlinenumber="107">TimePicker</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="107">Time selection control, create custom</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="108">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="108"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="108">Slider</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="108"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="108">SkiaMauiElement</code>+<code sourcefile="articles/porting-maui.md" sourcestartlinenumber="108">Slider</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="108">Range input control, create custom</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="109">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="109"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="109">Stepper</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="109"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="109">SkiaMauiElement</code>+<code sourcefile="articles/porting-maui.md" sourcestartlinenumber="109">Slider</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="109">Increment/decrement numeric input, create custom</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="110">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="110"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="110">ProgressBar</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="110"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="110">SkiaMauiElement</code>+<code sourcefile="articles/porting-maui.md" sourcestartlinenumber="110">Slider</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="110">Progress indication, create custom</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="111">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="111"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="111">TableView</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="111"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="111">SkiaMauiElement</code>+<code sourcefile="articles/porting-maui.md" sourcestartlinenumber="111">TableView</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="111">Grouped table display, create custom</td>
</tr>
<tr sourcefile="articles/porting-maui.md" sourcestartlinenumber="112">
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="112"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="112">SearchBar</code></td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="112">❌ Do not use, create custom</td>
<td sourcefile="articles/porting-maui.md" sourcestartlinenumber="112">Search input with built-in styling</td>
</tr>
</tbody>
</table>
<h2 id="the-practice" sourcefile="articles/porting-maui.md" sourcestartlinenumber="114">The practice</h2>
<p sourcefile="articles/porting-maui.md" sourcestartlinenumber="116">Let's take a look at a simple example of porting a native MAUI page to DrawnUi.</p>
<h3 id="before-native-maui" sourcefile="articles/porting-maui.md" sourcestartlinenumber="118">Before: Native MAUI</h3>
<p sourcefile="articles/porting-maui.md" sourcestartlinenumber="120">Here's a typical MAUI page with native controls:</p>
<pre><code class="lang-xml" sourcefile="articles/porting-maui.md" sourcestartlinenumber="122">&lt;ContentPage xmlns=&quot;http://schemas.microsoft.com/dotnet/2021/maui&quot;
             xmlns:x=&quot;http://schemas.microsoft.com/winfx/2009/xaml&quot;
             x:Class=&quot;MyApp.MainPage&quot;&gt;

    &lt;ScrollView&gt;
        &lt;VerticalStackLayout Spacing=&quot;25&quot; Padding=&quot;30,0&quot;&gt;

            &lt;Frame BackgroundColor=&quot;LightBlue&quot;
                   Padding=&quot;20&quot;
                   CornerRadius=&quot;10&quot;&gt;
                &lt;Label Text=&quot;Welcome to MAUI!&quot;
                       FontSize=&quot;18&quot;
                       HorizontalOptions=&quot;Center&quot; /&gt;
            &lt;/Frame&gt;

            &lt;HorizontalStackLayout Spacing=&quot;10&quot;&gt;
                &lt;Image Source=&quot;icon.png&quot;
                       WidthRequest=&quot;50&quot;
                       HeightRequest=&quot;50&quot; /&gt;
                &lt;Label Text=&quot;Hello World&quot;
                       FontSize=&quot;16&quot;
                       VerticalOptions=&quot;Center&quot; /&gt;
            &lt;/HorizontalStackLayout&gt;

            &lt;Button Text=&quot;Click me&quot;
                    BackgroundColor=&quot;Blue&quot;
                    TextColor=&quot;White&quot;
                    CornerRadius=&quot;8&quot;
                    Clicked=&quot;OnButtonClicked&quot; /&gt;

        &lt;/VerticalStackLayout&gt;
    &lt;/ScrollView&gt;

&lt;/ContentPage&gt;
</code></pre>
<h3 id="after-drawnui" sourcefile="articles/porting-maui.md" sourcestartlinenumber="159">After: DrawnUi</h3>
<p sourcefile="articles/porting-maui.md" sourcestartlinenumber="161">Here's the same page converted to DrawnUi:</p>
<pre><code class="lang-xml" sourcefile="articles/porting-maui.md" sourcestartlinenumber="163">&lt;draw:DrawnUiBasePage xmlns=&quot;http://schemas.microsoft.com/dotnet/2021/maui&quot;
                      xmlns:x=&quot;http://schemas.microsoft.com/winfx/2009/xaml&quot;
                      xmlns:draw=&quot;http://schemas.appomobi.com/drawnUi/2023/draw&quot;
                      x:Class=&quot;MyApp.MainPage&quot;&gt;

    &lt;draw:Canvas RenderingMode=&quot;Accelerated&quot;
                 Gestures=&quot;Enabled&quot;
                 HorizontalOptions=&quot;Fill&quot;
                 VerticalOptions=&quot;Fill&quot;&gt;

        &lt;draw:SkiaScroll&gt;
            &lt;draw:SkiaStack Spacing=&quot;25&quot; Padding=&quot;30,0&quot;&gt;

                &lt;draw:SkiaFrame BackgroundColor=&quot;LightBlue&quot;
                                Padding=&quot;20&quot;
                                CornerRadius=&quot;10&quot;&gt;
                    &lt;draw:SkiaLabel Text=&quot;Welcome to DrawnUi!&quot;
                                    FontSize=&quot;18&quot;
                                    HorizontalOptions=&quot;Center&quot; /&gt;
                &lt;/draw:SkiaFrame&gt;

                &lt;draw:SkiaRow Spacing=&quot;10&quot;&gt;
                    &lt;draw:SkiaImage Source=&quot;icon.png&quot;
                                    WidthRequest=&quot;50&quot;
                                    HeightRequest=&quot;50&quot; /&gt;
                    &lt;draw:SkiaLabel Text=&quot;Hello World&quot;
                                    FontSize=&quot;16&quot;
                                    VerticalOptions=&quot;Center&quot; /&gt;
                &lt;/draw:SkiaRow&gt;

                &lt;draw:SkiaButton Text=&quot;Click me&quot;
                                 BackgroundColor=&quot;Blue&quot;
                                 TextColor=&quot;White&quot;
                                 CornerRadius=&quot;8&quot;
                                 WidthRequest=&quot;120&quot;
                                 HeightRequest=&quot;44&quot;
                                 Clicked=&quot;OnButtonClicked&quot; /&gt;

            &lt;/draw:SkiaStack&gt;
        &lt;/draw:SkiaScroll&gt;

    &lt;/draw:Canvas&gt;

&lt;/draw:DrawnUiBasePage&gt;
</code></pre>
<h3 id="key-changes-made" sourcefile="articles/porting-maui.md" sourcestartlinenumber="210">Key Changes Made</h3>
<ol sourcefile="articles/porting-maui.md" sourcestartlinenumber="212">
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="212"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="212">Root Container</strong>: Changed from <code sourcefile="articles/porting-maui.md" sourcestartlinenumber="212">ContentPage</code> to <code sourcefile="articles/porting-maui.md" sourcestartlinenumber="212">draw:DrawnUiBasePage</code> just for keyboard support. You don't need it leave <code sourcefile="articles/porting-maui.md" sourcestartlinenumber="212">ContentPage</code> as it is.</li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="213"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="213">Canvas</strong>: Added <code sourcefile="articles/porting-maui.md" sourcestartlinenumber="213">draw:Canvas</code> as the root drawing surface</li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="214"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="214">Layout Controls</strong>:
<ul sourcefile="articles/porting-maui.md" sourcestartlinenumber="215">
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="215"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="215">ScrollView</code> → <code sourcefile="articles/porting-maui.md" sourcestartlinenumber="215">draw:SkiaScroll</code></li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="216"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="216">VerticalStackLayout</code> → <code sourcefile="articles/porting-maui.md" sourcestartlinenumber="216">draw:SkiaStack</code></li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="217"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="217">HorizontalStackLayout</code> → <code sourcefile="articles/porting-maui.md" sourcestartlinenumber="217">draw:SkiaRow</code></li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="218"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="218">Frame</code> → <code sourcefile="articles/porting-maui.md" sourcestartlinenumber="218">draw:SkiaFrame</code></li>
</ul>
</li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="219"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="219">Content Controls</strong>:
<ul sourcefile="articles/porting-maui.md" sourcestartlinenumber="220">
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="220"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="220">Label</code> → <code sourcefile="articles/porting-maui.md" sourcestartlinenumber="220">draw:SkiaLabel</code></li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="221"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="221">Image</code> → <code sourcefile="articles/porting-maui.md" sourcestartlinenumber="221">draw:SkiaImage</code></li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="222"><code sourcefile="articles/porting-maui.md" sourcestartlinenumber="222">Button</code> → <code sourcefile="articles/porting-maui.md" sourcestartlinenumber="222">draw:SkiaButton</code></li>
</ul>
</li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="223"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="223">Button Sizing</strong>: Added explicit <code sourcefile="articles/porting-maui.md" sourcestartlinenumber="223">WidthRequest</code> and <code sourcefile="articles/porting-maui.md" sourcestartlinenumber="223">HeightRequest</code> to button (DrawnUi buttons need explicit sizing)</li>
</ol>
<h3 id="code-behind-changes" sourcefile="articles/porting-maui.md" sourcestartlinenumber="225">Code-Behind Changes</h3>
<p sourcefile="articles/porting-maui.md" sourcestartlinenumber="227">The code-behind remains mostly the same, but the event signature is slightly different:</p>
<pre><code class="lang-csharp" sourcefile="articles/porting-maui.md" sourcestartlinenumber="229">// Before (MAUI)
private void OnButtonClicked(object sender, EventArgs e)
{
    // Handle click
}

// After (DrawnUi)
private void OnButtonClicked(SkiaButton button, SkiaGesturesParameters args)
{
    // Handle click - note the different parameters
}
</code></pre>
<h3 id="optimize-add-caching" sourcefile="articles/porting-maui.md" sourcestartlinenumber="243">Optimize: add caching</h3>
<p sourcefile="articles/porting-maui.md" sourcestartlinenumber="245">Imagine your page redrawing.. What could stay same if you redraw one element?</p>
<pre><code class="lang-xml" sourcefile="articles/porting-maui.md" sourcestartlinenumber="247">        &lt;draw:SkiaScroll&gt;
            &lt;!--this is a small stack, just cache it in whole --&gt;
            &lt;!--&quot;composite&quot; will redraw only changed areas, for instance the clicked button,
            leaving other area raster unchaged --&gt;
            &lt;draw:SkiaStack Spacing=&quot;25&quot; Padding=&quot;30,0&quot; UseCache=&quot;ImageComposite&quot;&gt;

            &lt;!-- unchaged code --&gt;

            &lt;/draw:SkiaStack&gt;

            &lt;/draw:SkiaStack&gt;
        &lt;/draw:SkiaScroll&gt;
</code></pre>
<h3 id="performance-benefits" sourcefile="articles/porting-maui.md" sourcestartlinenumber="262">Performance Benefits</h3>
<p sourcefile="articles/porting-maui.md" sourcestartlinenumber="264">After conversion, you'll get:</p>
<ul sourcefile="articles/porting-maui.md" sourcestartlinenumber="265">
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="265"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="265">Better Performance</strong>: Single canvas instead of multiple native views</li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="266"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="266">Consistent Rendering</strong>: Same appearance across all platforms</li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="267"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="267">Advanced Caching</strong>: Built-in rasterization and caching capabilities</li>
<li sourcefile="articles/porting-maui.md" sourcestartlinenumber="268"><strong sourcefile="articles/porting-maui.md" sourcestartlinenumber="268">Custom Drawing</strong>: Ability to add custom graphics and effects</li>
</ul>
<h3 id="example-with-dynamic-content" sourcefile="articles/porting-maui.md" sourcestartlinenumber="270">Example with dynamic content</h3>
<p sourcefile="articles/porting-maui.md" sourcestartlinenumber="272">TODO point is with dynamic we need other type of caching, groupping controls into cached layers</p>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/porting-maui.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>

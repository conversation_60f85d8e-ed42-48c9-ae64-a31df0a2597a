{"items": [{"name": "Articles", "href": "index.html", "topicHref": "index.html", "items": [{"name": "Getting Started", "items": [{"name": "Installation and Setup", "href": "articles/getting-started.html", "topicHref": "articles/getting-started.html"}, {"name": "Porting Native to Drawn", "href": "articles/porting-maui.html", "topicHref": "articles/porting-maui.html"}]}, {"name": "Your First DrawnUi App", "href": "articles/first-app.html", "topicHref": "articles/first-app.html"}, {"name": "Fluent C# Extensions", "href": "articles/fluent-extensions.html", "topicHref": "articles/fluent-extensions.html"}, {"name": "Controls", "href": "articles/controls/index.html", "topicHref": "articles/controls/index.html"}, {"name": "Advanced", "href": "articles/advanced/index.html", "topicHref": "articles/advanced/index.html", "items": [{"name": "Platform-Specific Styling", "href": "articles/advanced/platform-styling.html", "topicHref": "articles/advanced/platform-styling.html"}, {"name": "Layout System Architecture", "href": "articles/advanced/layout-system.html", "topicHref": "articles/advanced/layout-system.html"}, {"name": "Gradients", "href": "articles/advanced/gradients.html", "topicHref": "articles/advanced/gradients.html"}, {"name": "Game UI & Interactive Games", "href": "articles/advanced/game-ui.html", "topicHref": "articles/advanced/game-ui.html"}, {"name": "SkiaScroll & Virtualization", "href": "articles/advanced/skiascroll.html", "topicHref": "articles/advanced/skiascroll.html"}, {"name": "Gestures & Touch Input", "href": "articles/advanced/gestures.html", "topicHref": "articles/advanced/gestures.html"}]}]}, {"name": "FAQ", "href": "faq.html", "topicHref": "faq.html"}, {"name": "Demo", "href": "demo.html", "topicHref": "demo.html"}, {"name": "API Documentation", "href": "api/index.html", "tocHref": "api/toc.html", "topicHref": "api/index.html", "homepage": "api/index.html"}]}
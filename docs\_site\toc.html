
<div id="sidetoggle">
  <div>
    <div class="sidefilter">
      <form class="toc-filter">
        <span class="glyphicon glyphicon-filter filter-icon"></span>
        <span class="glyphicon glyphicon-remove clear-icon" id="toc_filter_clear"></span>
        <input type="text" id="toc_filter_input" placeholder="Filter by title" onkeypress="if(event.keyCode==13) {return false;}">
      </form>
    </div>
    <div class="sidetoc">
      <div class="toc" id="toc">

          <ul class="nav level1">
                <li>
                    <span class="expand-stub"></span>
                    <a href="index.html" name="" title="Articles">Articles</a>

                    <ul class="nav level2">
                          <li>
                              <span class="expand-stub"></span>
                              <a href="index.html" name="" title="Getting Started">Getting Started</a>

                              <ul class="nav level3">
                                    <li>
                                        <a href="articles/getting-started.html" name="" title="Installation and Setup">Installation and Setup</a>
                                    </li>
                                    <li>
                                        <a href="articles/porting-maui.html" name="" title="Porting Native to Drawn">Porting Native to Drawn</a>
                                    </li>
                              </ul>
                          </li>
                          <li>
                              <a href="articles/first-app.html" name="" title="Your First DrawnUi App">Your First DrawnUi App</a>
                          </li>
                          <li>
                              <a href="articles/fluent-extensions.html" name="" title="Fluent C# Extensions">Fluent C# Extensions</a>
                          </li>
                          <li>
                              <a href="articles/controls/index.html" name="" title="Controls">Controls</a>
                          </li>
                          <li>
                              <span class="expand-stub"></span>
                              <a href="articles/advanced/index.html" name="" title="Advanced">Advanced</a>

                              <ul class="nav level3">
                                    <li>
                                        <a href="articles/advanced/platform-styling.html" name="" title="Platform-Specific Styling">Platform-Specific Styling</a>
                                    </li>
                                    <li>
                                        <a href="articles/advanced/layout-system.html" name="" title="Layout System Architecture">Layout System Architecture</a>
                                    </li>
                                    <li>
                                        <a href="articles/advanced/gradients.html" name="" title="Gradients">Gradients</a>
                                    </li>
                                    <li>
                                        <a href="articles/advanced/game-ui.html" name="" title="Game UI &amp; Interactive Games">Game UI &amp; Interactive Games</a>
                                    </li>
                                    <li>
                                        <a href="articles/advanced/skiascroll.html" name="" title="SkiaScroll &amp; Virtualization">SkiaScroll &amp; Virtualization</a>
                                    </li>
                                    <li>
                                        <a href="articles/advanced/gestures.html" name="" title="Gestures &amp; Touch Input">Gestures &amp; Touch Input</a>
                                    </li>
                              </ul>
                          </li>
                    </ul>
                </li>
                <li>
                    <a href="faq.html" name="" title="FAQ">FAQ</a>
                </li>
                <li>
                    <a href="demo.html" name="" title="Demo">Demo</a>
                </li>
                <li>
                    <a href="api/index.html" name="api/toc.html" title="API Documentation">API Documentation</a>
                </li>
          </ul>
      </div>
    </div>
  </div>
</div>

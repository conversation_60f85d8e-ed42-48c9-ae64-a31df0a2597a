<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Namespace DrawnUi.Controls | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Namespace DrawnUi.Controls | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="DrawnUi.Controls">

  <h1 id="DrawnUi_Controls" data-uid="DrawnUi.Controls" class="text-break">Namespace DrawnUi.Controls</h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="markdown level0 remarks"></div>
    <h3 id="classes">
Classes
</h3>
      <h4><a class="xref" href="DrawnUi.Controls.AnimatedFramesRenderer.html">AnimatedFramesRenderer</a></h4>
      <section><p>Base class for playing frames. Subclass to play spritesheets, gifs, custom animations etc.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Controls.ContentWithBackdrop.html">ContentWithBackdrop</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.GifAnimation.html">GifAnimation</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.GridLayout.html">GridLayout</a></h4>
      <section><p>Helper class for SkiaLayout Type = LayoutType.Grid</p>
</section>
      <h4><a class="xref" href="DrawnUi.Controls.MauiEditor.html">MauiEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.MauiEditorHandler.html">MauiEditorHandler</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.MauiEntry.html">MauiEntry</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.MauiEntryHandler.html">MauiEntryHandler</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.RadioButtons.html">RadioButtons</a></h4>
      <section><p>Manages radio button groups, ensuring only one button is selected per group.
Supports grouping by parent control or by string name.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Controls.ScrollPickerLabelContainer.html">ScrollPickerLabelContainer</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.ScrollPickerWheel.html">ScrollPickerWheel</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaCarousel.html">SkiaCarousel</a></h4>
      <section><p>A specialized scroll control designed for creating swipeable carousels with automatic snapping to items.
Supports data binding through ItemsSource and ItemTemplate, peek effects with SidesOffset, and smooth transitions.
Ideal for image galleries, tab interfaces, and any swipeable content display.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaDecoratedGrid.html">SkiaDecoratedGrid</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaDrawer.html">SkiaDrawer</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaDrawnCell.html">SkiaDrawnCell</a></h4>
      <section><p>Base ISkiaCell implementation</p>
</section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaDynamicDrawnCell.html">SkiaDynamicDrawnCell</a></h4>
      <section><p>This cell can watch binding context property changing</p>
</section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaGif.html">SkiaGif</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaLottie.html">SkiaLottie</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.html">SkiaLottie.ColorEqualityComparer</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaMauiEditor.html">SkiaMauiEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaMauiEntry.html">SkiaMauiEntry</a></h4>
      <section><p>Used to draw maui element over a skia canvas.
Positions elelement using drawnUi layout and sometimes just renders element bitmap snapshot instead of displaying the real element, for example, when scrolling/animating.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaMediaImage.html">SkiaMediaImage</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaRadioButton.html">SkiaRadioButton</a></h4>
      <section><p>Switch-like control, can include any content inside. It's aither you use default content (todo templates?..)
or can include any content inside, and properties will by applied by convention to a SkiaShape with Tag <code>Frame</code>, SkiaShape with Tag <code>Thumb</code>. At the same time you can override ApplyProperties() and apply them to your content yourself.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaShell.html">SkiaShell</a></h4>
      <section><p>A Canvas with Navigation capabilities</p>
</section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaShell.ModalWrapper.html">SkiaShell.ModalWrapper</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaShell.NavigationLayer-1.html">SkiaShell.NavigationLayer&lt;T&gt;</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaShell.PageInStack.html">SkiaShell.PageInStack</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaShell.ParsedRoute.html">SkiaShell.ParsedRoute</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaShell.PopupWrapper.html">SkiaShell.PopupWrapper</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaShell.ShellCurrentRoute.html">SkiaShell.ShellCurrentRoute</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaShell.ShellStackChild.html">SkiaShell.ShellStackChild</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaShell.TypeRouteFactory.html">SkiaShell.TypeRouteFactory</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaShellNavigatedArgs.html">SkiaShellNavigatedArgs</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaShellNavigatingArgs.html">SkiaShellNavigatingArgs</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaSpinner.html">SkiaSpinner</a></h4>
      <section><p>A wheel-of-names spinner control that displays items in a circular arrangement
and allows spinning to select an item through gesture interaction.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaSprite.html">SkiaSprite</a></h4>
      <section><p>Renders animated sprite sheets by subclassing AnimatedFramesRenderer</p>
</section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaTabsSelector.html">SkiaTabsSelector</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaTabsSelector.TabEntry.html">SkiaTabsSelector.TabEntry</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaViewSwitcher.html">SkiaViewSwitcher</a></h4>
      <section><p>Display and hide views, eventually animating them</p>
</section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.html">SkiaViewSwitcher.NavigationStackEntry</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaWheelPicker.html">SkiaWheelPicker</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaWheelPickerCell.html">SkiaWheelPickerCell</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaWheelScroll.html">SkiaWheelScroll</a></h4>
      <section><p>A specialized scroll view that displays items in a 3D wheel-like arrangement</p>
</section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaWheelShape.html">SkiaWheelShape</a></h4>
      <section><p>Custom SkiaShape that positions children in a circular arrangement around the wheel circumference.
Handles rotation and positioning calculations for the spinner wheel.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaWheelStack.html">SkiaWheelStack</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.WheelCellInfo.html">WheelCellInfo</a></h4>
      <section></section>
    <h3 id="interfaces">
Interfaces
</h3>
      <h4><a class="xref" href="DrawnUi.Controls.ISkiaRadioButton.html">ISkiaRadioButton</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.ISmartNative.html">ISmartNative</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.IWheelPickerCell.html">IWheelPickerCell</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.SkiaShell.IHandleGoBack.html">SkiaShell.IHandleGoBack</a></h4>
      <section></section>
    <h3 id="enums">
Enums
</h3>
      <h4><a class="xref" href="DrawnUi.Controls.DrawerDirection.html">DrawerDirection</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Controls.NavigationSource.html">NavigationSource</a></h4>
      <section></section>


</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>

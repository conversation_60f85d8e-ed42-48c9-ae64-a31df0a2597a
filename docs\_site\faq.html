<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Frequently Asked Questions | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Frequently Asked Questions | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="images/favicon.ico">
      <link rel="stylesheet" href="styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="styles/docfx.css">
      <link rel="stylesheet" href="styles/main.css">
      <meta property="docfx:navrel" content="toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="index.html">
                <img id="logo" class="svg" src="images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">
        <div class="article row grid">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="frequently-asked-questions">Frequently Asked Questions</h1>

<p><strong>Is it DrawnUI or DrawnUi?</strong>
Both are totally fine.</p>
<p><strong>How do I create my custom button?</strong>
While you can use SkiaButton and set a custom content to it, you can also use a click handler Tapped with ANY control you like.</p>
<p><strong>I have an existing MAUI app, how can DrawnUi be beneficial to me?</strong>
You can definitely speed your app by replacing slow UI parts with faster drawn sections, replacing a horde of native controls with just one canvas. Check out the <a href="articles/porting-maui.html">Porting MAUI</a> guide.</p>
<p><strong>Knowing that properties are in points, how do I create a line or stroke of exactly 1 pixel?</strong>
When working with SkiaShape use a negative value (ex: -1) to pass pixels instead of points to compatible properties like StrokeWidth and similar.</p>
<p><strong>How do I bind SkiaImage source not to a file/url but to an existing bitmap?</strong>
Use <code>ImageBitmap</code> property for that, type is <code>LoadedImageSource</code>.</p>
<p><strong>Can DrawnUi use MAUI's default Images folder?</strong>
Unfortunately no. DrawnUi can read from Raw folder and from native storage if the app has written there, but not from the Images folder. It's &quot;hardcoded-designed for MAUI views&quot; and not accessible to DrawnUi controls.</p>
<p><strong>How do I prevent touch input from passing through overlapping controls?</strong>
Use the <code>BlockGesturesBelow=&quot;True&quot;</code> property on the top control. Note that <code>InputTransparent</code> makes the control itself avoid gestures, but doesn't block gestures from reaching controls below it in the Z-axis.</p>
<p><strong>Does DrawnUi work with .NET 9?</strong>
Yes, DrawnUi works with .NET 9. However, remember that SkiaLabel, SkiaLayout etc. are virtual drawn controls that must be placed inside a <code>Canvas</code> control: <code>&lt;draw:Canvas&gt;your skia controls&lt;/draw:Canvas&gt;</code>. Only Canvas has handlers for normal and hardware accelerated views.</p>
<p><strong>How do I enable mouse wheel scrolling in SkiaScroll?</strong>
Mouse wheel scrolling is not built-in by default, but you can easily implement it by subclassing SkiaScroll and overriding the <code>ProcessGestures</code> method to handle <code>TouchActionResult.Wheel</code> events. See the <a href="https://github.com/taublast/DrawnUi/discussions/162">GitHub discussions</a> for a complete implementation example.</p>
<hr>
<h2 id="didnt-find-your-answer">Didn't find your answer?</h2>
<p>If you didn't find the answer to your question here, please feel free to ask in our <a href="https://github.com/taublast/DrawnUi/discussions">GitHub Discussions</a>. The community and maintainers are active there and happy to help with any questions about DrawnUi implementation, troubleshooting, or best practices.</p>
<p>You can also:</p>
<ul>
<li>Browse existing discussions to see if someone else had a similar question</li>
<li>Report bugs or request features in <a href="https://github.com/taublast/DrawnUi/issues">GitHub Issues</a></li>
<li>Check out the <a href="articles/">complete documentation</a> for detailed guides and examples</li>
</ul>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/faq.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="styles/docfx.js"></script>
    <script type="text/javascript" src="styles/main.js"></script>
  </body>
</html>

### YamlMime:ManagedReference
items:
- uid: DrawnUi.Controls.SkiaShell.ParsedRoute
  commentId: T:DrawnUi.Controls.SkiaShell.ParsedRoute
  id: SkiaShell.ParsedRoute
  parent: DrawnUi.Controls
  children:
  - DrawnUi.Controls.SkiaShell.ParsedRoute.Arguments
  - DrawnUi.Controls.SkiaShell.ParsedRoute.Original
  - DrawnUi.Controls.SkiaShell.ParsedRoute.Parts
  langs:
  - csharp
  - vb
  name: SkiaShell.ParsedRoute
  nameWithType: SkiaShell.ParsedRoute
  fullName: DrawnUi.Controls.SkiaShell.ParsedRoute
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ParsedRoute
    path: ../src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs
    startLine: 2291
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: 'public record SkiaShell.ParsedRoute : IEquatable<SkiaShell.ParsedRoute>'
    content.vb: Public Class SkiaShell.ParsedRoute Implements IEquatable(Of SkiaShell.ParsedRoute)
  inheritance:
  - System.Object
  implements:
  - System.IEquatable{DrawnUi.Controls.SkiaShell.ParsedRoute}
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Controls.SkiaShell.ParsedRoute.Original
  commentId: P:DrawnUi.Controls.SkiaShell.ParsedRoute.Original
  id: Original
  parent: DrawnUi.Controls.SkiaShell.ParsedRoute
  langs:
  - csharp
  - vb
  name: Original
  nameWithType: SkiaShell.ParsedRoute.Original
  fullName: DrawnUi.Controls.SkiaShell.ParsedRoute.Original
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Original
    path: ../src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs
    startLine: 2293
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public string Original { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Original As String
  overload: DrawnUi.Controls.SkiaShell.ParsedRoute.Original*
- uid: DrawnUi.Controls.SkiaShell.ParsedRoute.Parts
  commentId: P:DrawnUi.Controls.SkiaShell.ParsedRoute.Parts
  id: Parts
  parent: DrawnUi.Controls.SkiaShell.ParsedRoute
  langs:
  - csharp
  - vb
  name: Parts
  nameWithType: SkiaShell.ParsedRoute.Parts
  fullName: DrawnUi.Controls.SkiaShell.ParsedRoute.Parts
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Parts
    path: ../src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs
    startLine: 2294
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public string[] Parts { get; set; }
    parameters: []
    return:
      type: System.String[]
    content.vb: Public Property Parts As String()
  overload: DrawnUi.Controls.SkiaShell.ParsedRoute.Parts*
- uid: DrawnUi.Controls.SkiaShell.ParsedRoute.Arguments
  commentId: P:DrawnUi.Controls.SkiaShell.ParsedRoute.Arguments
  id: Arguments
  parent: DrawnUi.Controls.SkiaShell.ParsedRoute
  langs:
  - csharp
  - vb
  name: Arguments
  nameWithType: SkiaShell.ParsedRoute.Arguments
  fullName: DrawnUi.Controls.SkiaShell.ParsedRoute.Arguments
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Arguments
    path: ../src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs
    startLine: 2295
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public IDictionary<string, object> Arguments { get; set; }
    parameters: []
    return:
      type: System.Collections.Generic.IDictionary{System.String,System.Object}
    content.vb: Public Property Arguments As IDictionary(Of String, Object)
  overload: DrawnUi.Controls.SkiaShell.ParsedRoute.Arguments*
references:
- uid: DrawnUi.Controls
  commentId: N:DrawnUi.Controls
  href: DrawnUi.html
  name: DrawnUi.Controls
  nameWithType: DrawnUi.Controls
  fullName: DrawnUi.Controls
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.IEquatable{DrawnUi.Controls.SkiaShell.ParsedRoute}
  commentId: T:System.IEquatable{DrawnUi.Controls.SkiaShell.ParsedRoute}
  parent: System
  definition: System.IEquatable`1
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  name: IEquatable<SkiaShell.ParsedRoute>
  nameWithType: IEquatable<SkiaShell.ParsedRoute>
  fullName: System.IEquatable<DrawnUi.Controls.SkiaShell.ParsedRoute>
  nameWithType.vb: IEquatable(Of SkiaShell.ParsedRoute)
  fullName.vb: System.IEquatable(Of DrawnUi.Controls.SkiaShell.ParsedRoute)
  name.vb: IEquatable(Of SkiaShell.ParsedRoute)
  spec.csharp:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: <
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.ParsedRoute
    name: ParsedRoute
    href: DrawnUi.Controls.SkiaShell.ParsedRoute.html
  - name: '>'
  spec.vb:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.ParsedRoute
    name: ParsedRoute
    href: DrawnUi.Controls.SkiaShell.ParsedRoute.html
  - name: )
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.IEquatable`1
  commentId: T:System.IEquatable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  name: IEquatable<T>
  nameWithType: IEquatable<T>
  fullName: System.IEquatable<T>
  nameWithType.vb: IEquatable(Of T)
  fullName.vb: System.IEquatable(Of T)
  name.vb: IEquatable(Of T)
  spec.csharp:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Controls.SkiaShell.ParsedRoute.Original*
  commentId: Overload:DrawnUi.Controls.SkiaShell.ParsedRoute.Original
  href: DrawnUi.Controls.SkiaShell.ParsedRoute.html#DrawnUi_Controls_SkiaShell_ParsedRoute_Original
  name: Original
  nameWithType: SkiaShell.ParsedRoute.Original
  fullName: DrawnUi.Controls.SkiaShell.ParsedRoute.Original
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: DrawnUi.Controls.SkiaShell.ParsedRoute.Parts*
  commentId: Overload:DrawnUi.Controls.SkiaShell.ParsedRoute.Parts
  href: DrawnUi.Controls.SkiaShell.ParsedRoute.html#DrawnUi_Controls_SkiaShell_ParsedRoute_Parts
  name: Parts
  nameWithType: SkiaShell.ParsedRoute.Parts
  fullName: DrawnUi.Controls.SkiaShell.ParsedRoute.Parts
- uid: System.String[]
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string[]
  nameWithType: string[]
  fullName: string[]
  nameWithType.vb: String()
  fullName.vb: String()
  name.vb: String()
  spec.csharp:
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: '['
  - name: ']'
  spec.vb:
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: (
  - name: )
- uid: DrawnUi.Controls.SkiaShell.ParsedRoute.Arguments*
  commentId: Overload:DrawnUi.Controls.SkiaShell.ParsedRoute.Arguments
  href: DrawnUi.Controls.SkiaShell.ParsedRoute.html#DrawnUi_Controls_SkiaShell_ParsedRoute_Arguments
  name: Arguments
  nameWithType: SkiaShell.ParsedRoute.Arguments
  fullName: DrawnUi.Controls.SkiaShell.ParsedRoute.Arguments
- uid: System.Collections.Generic.IDictionary{System.String,System.Object}
  commentId: T:System.Collections.Generic.IDictionary{System.String,System.Object}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IDictionary`2
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.idictionary-2
  name: IDictionary<string, object>
  nameWithType: IDictionary<string, object>
  fullName: System.Collections.Generic.IDictionary<string, object>
  nameWithType.vb: IDictionary(Of String, Object)
  fullName.vb: System.Collections.Generic.IDictionary(Of String, Object)
  name.vb: IDictionary(Of String, Object)
  spec.csharp:
  - uid: System.Collections.Generic.IDictionary`2
    name: IDictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.idictionary-2
  - name: <
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IDictionary`2
    name: IDictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.idictionary-2
  - name: (
  - name: Of
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Collections.Generic.IDictionary`2
  commentId: T:System.Collections.Generic.IDictionary`2
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.idictionary-2
  name: IDictionary<TKey, TValue>
  nameWithType: IDictionary<TKey, TValue>
  fullName: System.Collections.Generic.IDictionary<TKey, TValue>
  nameWithType.vb: IDictionary(Of TKey, TValue)
  fullName.vb: System.Collections.Generic.IDictionary(Of TKey, TValue)
  name.vb: IDictionary(Of TKey, TValue)
  spec.csharp:
  - uid: System.Collections.Generic.IDictionary`2
    name: IDictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.idictionary-2
  - name: <
  - name: TKey
  - name: ','
  - name: " "
  - name: TValue
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IDictionary`2
    name: IDictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.idictionary-2
  - name: (
  - name: Of
  - name: " "
  - name: TKey
  - name: ','
  - name: " "
  - name: TValue
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic

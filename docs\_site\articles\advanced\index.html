<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Advanced Topics | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Advanced Topics | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="advanced-topics">Advanced Topics</h1>

<p>This section covers advanced features and concepts for DrawnUi development.</p>
<h2 id="architecture-and-performance">Architecture and Performance</h2>
<ul>
<li><a href="layout-system.html">Layout System Architecture</a> - Deep dive into how the layout system works</li>
<li><a href="platform-styling.html">Platform-Specific Styling</a> - Creating platform-specific UI styles</li>
</ul>
<h2 id="visual-features">Visual Features</h2>
<ul>
<li><a href="gradients.html">Gradients</a> - Creating and using gradient effects</li>
<li><a href="skiascroll.html">SkiaScroll &amp; Virtualization</a> - Advanced scrolling and performance optimization</li>
</ul>
<h2 id="interaction">Interaction</h2>
<ul>
<li><a href="gestures.html">Gestures &amp; Touch Input</a> - Handling complex touch interactions</li>
</ul>
<h2 id="specialized-use-cases">Specialized Use Cases</h2>
<ul>
<li><a href="game-ui.html">Game UI &amp; Interactive Games</a> - Building game interfaces and interactive experiences</li>
</ul>
<h2 id="best-practices">Best Practices</h2>
<p>When working with advanced DrawnUi features:</p>
<ol>
<li><strong>Performance</strong>: Use appropriate caching strategies for your use case</li>
<li><strong>Platform Differences</strong>: Test platform-specific features on all target platforms</li>
<li><strong>Memory Management</strong>: Be mindful of resource usage, especially with images and animations</li>
<li><strong>Touch Handling</strong>: Understand the gesture system for complex interactions</li>
</ol>
<h2 id="getting-help">Getting Help</h2>
<p>For advanced topics not covered here, check:</p>
<ul>
<li>The source code examples in the Sandbox project</li>
<li>Community discussions and issues on GitHub</li>
<li>The API documentation for detailed method signatures</li>
</ul>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/advanced/index.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>

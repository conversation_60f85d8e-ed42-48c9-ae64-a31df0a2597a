﻿using AppoMobi.Specials.Localization;
using DrawnUi.Controls;

namespace AppoMobi.Mobile.Views;

public class TabRequests : AppScreen
{
    private readonly MainPageViewModel Model;

    public TabRequests(MainPageViewModel vm)
    {
        Model = vm;
        BindingContext = Model;

        Children = new List<SkiaControl>
        {
            new SkiaLottie()
                {
                    TranslationX = 4,
                    TranslationY = App.Instance.Presentation.StatusBarHeightRequest - 4,
                    AutoPlay = true,
                    HorizontalOptions = LayoutOptions.End,
                    LockRatio = 1,
                    Opacity = 0.99,
                    Repeat = -1,
                    ColorTint = AppColors.PrimaryLight,
                    Source = "Lottie/shield.json",
                    SpeedRatio = 0.5,
                    UseCache = SkiaCacheType.Operations,
                    WidthRequest = 100,
                }.Assign(out AnimationButton)
                .Observe(App.Instance.Presentation, (me, prop) =>
                {
                    if (prop.IsEither(nameof(BindingContext), nameof(NavigationViewModel.StatusBarHeightRequest)))
                    {
                        me.TranslationY = App.Instance.Presentation.StatusBarHeightRequest - 4;
                    }
                }),

            CreateContentLayout()
        };
    }

    public override void OnAppearing()
    {
        base.OnAppearing();

        Trace.WriteLine("OnAppearing");
    }

    public override void OnDisappearing()
    {
        base.OnDisappearing();

        Trace.WriteLine("OnDisappearing");
    }

    private SkiaLottie AnimationButton;

    protected override void OnLayoutReady()
    {
        base.OnLayoutReady();

        var canUpdate = BindingContext as IUpdateUIState;
        canUpdate?.UpdateState(true);
    }

    private SkiaLayout CreateContentLayout()
    {
        SkiaCarousel Carousel = null;

        var tabsCorners = 16.0;
        var tabsHeight = 36.0;

        return new ScreenVerticalStack
        {
            Padding = new Thickness(0, 16, 0, 16),
            HorizontalOptions = LayoutOptions.Fill,
            VerticalOptions = LayoutOptions.Fill,
            Spacing = 0,
            Type = LayoutType.Column,
            Tag = "Debug",
            Children = new List<SkiaControl>
            {
                // Navbar padding for fullscreen version
                new StatusBarPlaceholder(),

                // Title with tool button
                new SkiaLayout()
                {
                    UseCache = SkiaCacheType.Image,
                    //HeightRequest = 44,
                    Margin = new(24, 8, 24, 24),
                    HorizontalOptions = LayoutOptions.Fill,
                    Children = new List<SkiaControl>()
                    {
                        new LabelScreenTitle
                        {
                            Text = ResStrings.Deliveries,
                        },

                        App.Instance.Presentation.Shell.Elements.CreateRoundButton()
                            .OnTapped((me) =>
                            {
                                Model.CommandAddRequest.Execute(null);
                                //Super.StatusBarHeight += 5;
                                //Super.InsetsChanged?.Invoke(this, null);
                            })
                            .EndX()
                    }
                },

                //tabs header
                new SkiaShape()
                {
                    Margin = new Thickness(16, 8, 16, 12),
                    HorizontalOptions = LayoutOptions.Fill,
                    CornerRadius = tabsCorners,
                    HeightRequest = tabsHeight,
                    UseCache = SkiaCacheType.Operations,
                    Children =
                    {
                        new DrawnTabsHeader()
                            {
                                VerticalOptions = LayoutOptions.Fill,
                                TouchEffectColor = AppColors.Touch,
                                Children = new List<SkiaControl>()
                                {
                                    // inactive layer
                                    new SkiaLayout()
                                    {
                                        BackgroundColor = AppColors.ControlMinor,
                                        UseCache = SkiaCacheType.Image,
                                        Type = LayoutType.Grid,
                                        DefaultRowDefinition = new RowDefinition(GridLength.Star),
                                        ColumnSpacing = 0,
                                        VerticalOptions = LayoutOptions.Fill,
                                        HorizontalOptions = LayoutOptions.Fill,
                                        Children = new List<SkiaControl>()
                                        {
                                            // MASK: this shape's clip will be used to draw active layer
                                            new SkiaShape()
                                            {
                                                CornerRadius = tabsCorners,
                                                HorizontalOptions = LayoutOptions.Fill,
                                                VerticalOptions = LayoutOptions.Fill,
                                                Tag = "Clip", // by convention
                                            },
                                            // tab 0 unselected
                                            new SkiaLabel()
                                            {
                                                Text = ResStrings.FilterActive,
                                                FontFamily = "FontText",
                                                TextColor = AppColors.TextSecondary,
                                                HorizontalOptions = LayoutOptions.Center,
                                                VerticalOptions = LayoutOptions.Center
                                            }.WithColumn(0),
                                            // 1 unselected
                                            new SkiaLabel()
                                            {
                                                Text = ResStrings.FilterArchive,
                                                FontFamily = "FontText",
                                                TextColor = AppColors.TextSecondary,
                                                HorizontalOptions = LayoutOptions.Center,
                                                VerticalOptions = LayoutOptions.Center
                                            }.WithColumn(1),
                                        }
                                    }.WithColumnDefinitions("*,*"),

                                    // active layer
                                    new SkiaLayout()
                                    {
                                        Tag = "Active", // by convention
                                        BackgroundColor = AppColors.ControlPrimary,
                                        UseCache = SkiaCacheType.Image,
                                        Type = LayoutType.Grid,
                                        DefaultRowDefinition = new RowDefinition(GridLength.Star),
                                        ColumnSpacing = 0,
                                        VerticalOptions = LayoutOptions.Fill,
                                        HorizontalOptions = LayoutOptions.Fill,
                                        Children = new List<SkiaControl>()
                                        {
                                            // tab 0 selected
                                            new SkiaLabel()
                                            {
                                                FontFamily = "FontTextSemiBold",
                                                Text = ResStrings.FilterActive,
                                                TextColor = AppColors.TextInvert,
                                                HorizontalOptions = LayoutOptions.Center,
                                                VerticalOptions = LayoutOptions.Center
                                            }.WithColumn(0),
                                            // 1 selected
                                            new SkiaLabel()
                                            {
                                                FontFamily = "FontTextSemiBold",
                                                Text = ResStrings.FilterArchive,
                                                TextColor = AppColors.TextInvert,
                                                HorizontalOptions = LayoutOptions.Center,
                                                VerticalOptions = LayoutOptions.Center
                                            }.WithColumn(1),
                                        }
                                    }.WithColumnDefinitions("*,*")
                                }
                            }
                            .Initialize((me) =>
                            {
                                me.BindProperty(DrawnTabsHeader.SelectedIndexProperty, Carousel,
                                    nameof(SkiaCarousel.SelectedIndex), BindingMode.TwoWay);
                                me.BindProperty(DrawnTabsHeader.TabsCountProperty, Carousel,
                                    nameof(SkiaCarousel.ChildrenCount));
                                me.BindProperty(DrawnTabsHeader.ScrollAmountProperty, Carousel,
                                    nameof(SkiaCarousel.ScrollProgress));
                            }),
                    }
                },

                //vertical tabs content
                new SkiaCarousel()
                    {
                        AutoVelocityMultiplyPts = 10,
                        HorizontalOptions = LayoutOptions.Fill,
                        VerticalOptions = LayoutOptions.Fill,
                        Children = new List<SkiaControl>()
                        {
                            //tab 0 - ACTIVE
                            new SkiaLayout()
                            {
                                HorizontalOptions = LayoutOptions.Fill,
                                VerticalOptions = LayoutOptions.Fill,
                                Children = new List<SkiaControl>()
                                {
                                    new CellsScroll()
                                        {
                                            Tag = "Active",
                                            RefreshCommand = Model.CommandRefresh,
                                            RefreshEnabled = true,

                                            //scroll content tab 0
                                            Content = new CellsStack()
                                                {
                                                    //EmptyView
                                                    EmptyView = new SkiaLayer()
                                                    {
                                                        Children = new List<SkiaControl>()
                                                        {
                                                            new SkiaStack()
                                                                {
                                                                    IsVisible = false,
                                                                    UseCache = SkiaCacheType.Image,
                                                                    Spacing = 24,
                                                                    Margin = new(24, 32),
                                                                    Children = new List<SkiaControl>()
                                                                    {
                                                                        new SkiaLabel(ResStrings.OrdersHint)
                                                                        {
                                                                            HorizontalOptions = LayoutOptions.Center,
                                                                            HorizontalTextAlignment =
                                                                                DrawTextAlignment.Center,
                                                                            Margin = new Thickness(0, 0, 0, 8)
                                                                        },

                                                                        new SkiaSvg()
                                                                        {
                                                                            HorizontalOptions = LayoutOptions.Center,
                                                                            UseCache = SkiaCacheType.Operations,
                                                                            WidthRequest = 120,
                                                                            LockRatio = 1,
                                                                            FontAwesomePrimaryColor = AppColors.Text,
                                                                            FontAwesomeSecondaryColor =
                                                                                AppColors.PrimaryLight,
                                                                            SvgString = App.Current.Resources
                                                                                .Get<string>(
                                                                                    "SvgDuoNewOrder")
                                                                        },

                                                                        new SkiaLabel(ResStrings.HintFirstOrder)
                                                                        {
                                                                            TextColor = AppColors.Text,
                                                                            FontFamily = AppFonts.SemiBold,
                                                                            HorizontalOptions = LayoutOptions.Fill,
                                                                            HorizontalTextAlignment =
                                                                                DrawTextAlignment.Center,
                                                                        }
                                                                    }
                                                                }
                                                                .OnTapped((me) =>
                                                                {
                                                                    Model.CommandAddRequest.Execute(null);
                                                                })
                                                                .ObserveBindingContext<SkiaLayout, MainPageViewModel>((
                                                                    me, vm,
                                                                    prop) =>
                                                                {
                                                                    bool attached = prop == nameof(BindingContext);
                                                                    if (attached || prop == nameof(vm.IsBusy))
                                                                    {
                                                                        var show = !vm.IsBusy
                                                                            && vm.HasLoaded && vm.HasData &&
                                                                            Model.RequestsDataService.LoadedItems !=
                                                                            null &&
                                                                            Model.RequestsDataService.LoadedItems
                                                                                .Count == 0;
                                                                        if (show)
                                                                        {
                                                                            var stop = 1;
                                                                        }

                                                                        me.IsVisible = show;
                                                                    }
                                                                })
                                                        }
                                                    },

                                                    ItemTemplate = new DataTemplate(() =>
                                                    {
                                                        var cell = new CellServiceRequest();
                                                        cell.AnimationTapped = SkiaTouchAnimation.Ripple;
                                                        cell.TouchEffectColor = AppColors.PrimaryLight;
                                                        cell.Tapped += (sender, args) =>
                                                        {
                                                            Model.CommandRequestDetails
                                                                .Execute(cell.BindingContext);
                                                        };
                                                        return cell;
                                                    })
                                                }
                                                //Set ItemsSource
                                                .ObserveBindingContext<SkiaLayout, MainPageViewModel>((me, vm, prop) =>
                                                {
                                                    bool attached = prop == nameof(BindingContext);

                                                    if (attached || prop == nameof(vm.HasData))
                                                    {
                                                        if (vm.HasData)
                                                            me.ItemsSource = vm.RequestsDataService.Items;
                                                    }

                                                    if (attached || prop == nameof(vm.HasError))
                                                    {
                                                        if (vm.HasError)
                                                            me.ItemsSource = null;
                                                    }
                                                }),
                                        }
                                        //Attach refreshing state
                                        .ObserveBindingContext<SkiaScroll, MainPageViewModel>((me, vm, prop) =>
                                        {
                                            bool attached = prop == nameof(BindingContext);

                                            if (attached || prop == nameof(vm.IsRefreshing))
                                            {
                                                me.IsRefreshing = vm.IsRefreshing;
                                            }
                                        }),

                                    // ERROR VIEW 0
                                    new ConnectionErrorView()
                                }
                            },


                            //tab 1 - ARCHIVE

                            new CellsScroll()
                                {
                                    Tag = "Archive",
                                    RefreshCommand = Model.CommandRefreshArchive,
                                    RefreshEnabled = true,

                                    //scroll content tab 0
                                    Content = new CellsStack()
                                        {
                                            ItemTemplate = new DataTemplate(() =>
                                            {
                                                var cell = new CellServiceRequest();
                                                cell.Tapped += (sender, args) =>
                                                {
                                                    Model.CommandRequestDetails.Execute(cell.BindingContext);
                                                };
                                                return cell;
                                            })
                                        }
                                        //Set ItemsSource
                                        .ObserveBindingContext<SkiaLayout, MainPageViewModel>((me, vm, prop) =>
                                        {
                                            bool attached = prop == nameof(BindingContext);

                                            if (attached || prop == nameof(vm.HasLoadedArchive))
                                            {
                                                me.ItemsSource = vm.DataServiceArchive.Items;
                                            }

                                            if (attached || prop == nameof(vm.HasErrorArchive))
                                            {
                                                if (vm.HasError)
                                                    me.ItemsSource = null;
                                            }
                                        }),
                                }
                                //Attach refreshing state
                                .ObserveBindingContext<SkiaScroll, MainPageViewModel>((me, vm, prop) =>
                                {
                                    bool attached = prop == nameof(BindingContext);

                                    if (attached || prop == nameof(vm.IsRefreshingArchive))
                                    {
                                        //me.IsRefreshing = vm.IsRefreshingArchive;
                                    }
                                }),
                        }
                    }
                    .ObserveSelf((me, prop) =>
                    {
                        if (prop.IsEither(nameof(BindingContext), nameof(me.SelectedIndex)))
                        {
                            Model.RequestFilterIndex = me.SelectedIndex;
                        }
                    })
                    .Assign(out Carousel),
            }
        };
    }
}
﻿using AppoMobi.Mobile.Views;
using DrawnUi.Controls;

namespace AppoMobi.Mobile;

public class ScreenLogin : AppScreen
{
    protected readonly LoginSmsViewModel Model;

    public ScreenLogin(LoginSmsViewModel vm)
    {
        Model = vm;

        BindingContext = vm;

        //Model.GoToEnterCode();
        Model.GoToStart();

        VerticalOptions = LayoutOptions.Fill;
        HorizontalOptions = LayoutOptions.Fill;

        CreateContent();

        //waiting popup
        SkiaShell.PopupsBackgroundBlur = 0;
        SkiaShell.PopupBackgroundColor = Color.Parse("#22000000");

        //var popup = App.Instance.Presentation.Shell.Elements.CreateWaitingPopup();

        //Tasks.StartDelayed(TimeSpan.FromMilliseconds(2500), () =>
        //{
        //    _ = App.Instance.Presentation.Shell.OpenPopupAsync(popup, 
        //        true, 
        //        true, false);
        //});
    }

    private void CreateContent()
    {
        Children = new List<SkiaControl>()
        {
            new SkiaLayout()
            {
                Type = LayoutType.Column,
                VerticalOptions = LayoutOptions.Fill,
                HorizontalOptions = LayoutOptions.Fill,
                Children = new List<SkiaControl>()
                {
                    new LabelScreenTitle()
                    {
                        Margin = new(32, 64, 32, 24),
                    }.Adapt((label) => { label.SetBinding(SkiaLabel.TextProperty, "Title"); }),

                    // FIRST STEP (0) - Enter Phone
                    CreateEnterPhoneStep(),

                    // SECOND STEP (1) - Enter SMS Code
                    CreateEnterCodeStep(),

                    new KeyboardPlaceholder()
                }
            }
        };
    }

    // STEP 1 - ENTER PHONE
    private SkiaLayout CreateEnterPhoneStep()
    {
        return new ScreenVerticalStack()
            {
                Tag = "ContentEnterPhone",
                VerticalOptions = LayoutOptions.Fill,
                Children = new List<SkiaControl>()
                {
                    new SkiaLabel()
                    {
                        Margin = new Microsoft.Maui.Thickness(0, 0, 0, -16),
                        FontSize = 13,
                        Text = ResStrings.WhyNeedMobileNumber,
                        TextColor = AppColors.Text
                    },

                    CreatePhoneInputGrid(),

                    // Placeholder to push content to the bottom
                    //new SkiaLayout()
                    //{
                    //    VerticalOptions = LayoutOptions.Fill
                    //},

                    // new SkiaButton()
                    // {
                    //     BackgroundColor = Colors.Red,
                    //     Text = "Debug",
                    //     WidthRequest = 150,
                    //     HeightRequest = 48,
                    // }.Adapt((btn) => { btn.SetBinding(SkiaButton.CommandTappedProperty, "CommandNextStep"); }),

                    // new SkiaLabel()
                    // {
                    //     TextColor = Colors.Red
                    // }                    .Adapt((label) =>
                    // {
                    //     label.SetBinding(SkiaLabel.TextProperty, "DebugString");
                    // }),

                    // Privacy & Agreement
                    CreatePrivacySection(),
                }
            }
            .ObserveBindingContext<SkiaLayout, LoginSmsViewModel>((me, vm, prop) =>
            {
                if (prop == nameof(vm.ProcessStep) || prop == nameof(BindingContext))
                {
                    me.IsVisible = vm.ProcessStep == 0;
                }
            })
            .Adapt((layout) =>
            {
                layout.LayoutIsReady += (s, a) =>
                {
                    if (Model.IsStep0On)
                    {
                        Tasks.StartDelayed(TimeSpan.FromMilliseconds(100), () => { PanelInputTel.Focus(); });
                    }
                };

                layout.VisibilityChanged += (s, v) =>
                {
                    if (v)
                    {
                        Tasks.StartDelayed(TimeSpan.FromMilliseconds(500), () => { PanelInputTel.Focus(); });
                    }
                };
            });
    }

    // STEP 2 - ENTER CODE
    private SkiaLayout CreateCountryCodeSection()
    {
        return new SkiaLayout()
            {
                Margin = new Microsoft.Maui.Thickness(12, 0, 0, 0),
                HorizontalOptions = LayoutOptions.Start,
                VerticalOptions = LayoutOptions.Fill,
                Children = new List<SkiaControl>()
                {
                    new SkiaLabel()
                        {
                            Tag = "LabelCode",
                            //AutoSize = AutoSizeType.FitHorizontal,
                            //CharWidth = 9,
                            FontFamily = "FontTextBold",
                            FontSize = 15,
                            MaxLines = 1,
                            VerticalOptions = LayoutOptions.Center
                        }.Assign(out LabelCode)
                        .Adapt((label) => { label.SetBinding(SkiaLabel.TextProperty, "Country.TelCode"); }),

                    // Bottom line
                    new SkiaShape()
                    {
                        BackgroundColor = AppColors.Line,
                        HeightRequest = 1,
                        HorizontalOptions = LayoutOptions.Fill,
                        VerticalOptions = LayoutOptions.End
                    }.Assign(out BoxViewCodeDivider)
                }
            }
            .SetGrid(1, 0);
    }

    private SkiaLayout CreatePrivacySection()
    {
        return new SkiaLayout()
        {
            Type = LayoutType.Column,
            Margin = new(0, 16, 0, 40),
            VerticalOptions = LayoutOptions.End,
            HorizontalOptions = LayoutOptions.Fill,
            Children = new List<SkiaControl>()
            {
                // Agree checkbox with text
                new SkiaLayout()
                {
                    Type = LayoutType.Grid,
                    ColumnSpacing = 8,
                    Tag = "ControlAgree",
                    Margin = new Microsoft.Maui.Thickness(0, 0, 0, 16),
                    ColumnDefinitions = new ColumnDefinitionCollection
                    {
                        new ColumnDefinition { Width = GridLength.Auto },
                        new ColumnDefinition { Width = GridLength.Star }
                    },
                    Children = new List<SkiaControl>()
                    {
                        new AppCheckbox()
                            {
                            }.Adapt((check) =>
                            {
                                check.SetBinding(SkiaToggle.IsToggledProperty, "Agree", BindingMode.TwoWay);
                                check.SetBinding(SkiaToggle.CommandTappedProperty, "CommandCheckAgree");
                            })
                            .SetGrid(0, 0),

                        new SkiaMarkdownLabel()
                        {
                            FontSize = 11,
                            UnderlineLink = true,
                            UseCache = SkiaCacheType.Image,
                            ClipEffects = true,
                            LinkColor = AppColors.Text,
                            UnderlineWidth = -1, // 1px
                            Text = ResStrings.LegalAgreeCheck,
                            FontFamily = "FontText",
                            TextColor = AppColors.TextMinor,
                            VerticalOptions = LayoutOptions.Start
                        }.SetGrid(1, 0).Adapt((me) => { me.LinkTapped += (sender, e) => { Model.ProcessTapLink(e); }; })
                    }
                }.Assign(out ControlAgree),

                /*
                // Debug server selection (only visible in debug mode)
                new SkiaLayout()
                {
                    Type = LayoutType.Grid,
                    Tag = "LabelToken",
                    ColumnDefinitions = new ColumnDefinitionCollection
                    {
                        new ColumnDefinition { Width = GridLength.Auto },
                        new ColumnDefinition { Width = GridLength.Star }
                    },
                    Children = new List<SkiaControl>()
                    {
                        new ButtonSmall()
                            {
                                Margin = new Microsoft.Maui.Thickness(32),
                                HorizontalOptions = LayoutOptions.Center,
                                Text = "Выбор сервера",
                                TextColor = AppColors.Link,
                                Look = BtnStyle.Transparent
                            }.Adapt((button) =>
                            {
#if DEBUG
                                button.SetBinding(IsVisibleProperty, "IsDebug");
                                button.SetBinding(ButtonSmall.CommandTappedProperty, "CommandSelectServer");
#else
                                // Compiled bindings
                                button.SetBinding(IsVisibleProperty,
                                    static (LoginSmsViewModel vm)  => vm.IsDebug);
                                button.SetBinding(ButtonSmall.CommandTappedProperty,
                                    static (LoginSmsViewModel vm)  => vm.CommandSelectServer);
#endif
                            })
                            .SetGrid(0, 1)
                    }
                }.Adapt((grid) =>
                {
#if DEBUG
                    grid.SetBinding(IsVisibleProperty, "IsDebug");
#else
                        // Compiled bindings
                        grid.SetBinding(IsVisibleProperty,
                            static (LoginSmsViewModel vm)  => vm.IsDebug);
#endif
                })

                */
            }
        };
    }

    private SkiaLayout CreatePhoneInputGrid()
    {
        var grid = new SkiaLayout()
        {
            Tag = "InputGrid",
            Type = LayoutType.Grid,
            HeightRequest = 49,
            ColumnDefinitions = new ColumnDefinitionCollection
            {
                new ColumnDefinition { Width = GridLength.Auto },
                new ColumnDefinition { Width = GridLength.Auto },
                new ColumnDefinition { Width = new GridLength(8) },
                new ColumnDefinition { Width = GridLength.Star }
            },
            RowDefinitions = new RowDefinitionCollection
            {
                new RowDefinition { Height = GridLength.Auto }
            },
            ColumnSpacing = 0,
            HorizontalOptions = LayoutOptions.Fill,
            VerticalOptions = LayoutOptions.Start,
            Children = new List<SkiaControl>()
            {
                // Country Selector
                CreateCountrySelector(),

                // Country Code
                CreateCountryCodeSection(),

                // Divider
                new SkiaShape()
                    {
                        BackgroundColor = AppColors.Line,
                        HeightRequest = 1,
                        HorizontalOptions = LayoutOptions.Fill,
                        VerticalOptions = LayoutOptions.End
                    }.Assign(out BoxViewDivider)
                    .SetGrid(2, 0),

                // Highlight box for focus
                //new SkiaShape()
                //    {
                //        BackgroundColor = Colors.Fuchsia,// AppColors.Focus,
                //        HorizontalOptions = LayoutOptions.Start,
                //        VerticalOptions = LayoutOptions.Fill,
                //        ScaleX = 1.1f
                //    }.Assign(out BoxViewHighlight)
                //    .SetGrid(3, 0),

                // Phone Input Panel
                new PinMaskedPanel()
                    {
                        HorizontalOptions = LayoutOptions.Start,
                        VerticalOptions = LayoutOptions.Fill,
                        Spacing = 4
                    }.Assign(out PanelInputTel)
                    .Adapt((panel) =>
                    {
                        panel.SetBinding(PinPanel.ChangeToResetProperty, "ChangeToUpdateStep0");
                        panel.SetBinding(PinPanel.FinishedCommandProperty, "CommandSubmitPhone");
                        panel.SetBinding(PinPanel.InputMaskProperty, "InputMask");
                        panel.SetBinding(PinPanel.LimitProperty, "TelLimit");
                        panel.SetBinding(PinPanel.LimitReachedProperty, "TelFull", BindingMode.TwoWay);
                        panel.SetBinding(PinPanel.ToggleKeyboardProperty, "ToggleKeyboard1");
                    })
                    .SetGrid(3, 0),
            }
        };

        grid.Observe(Model, (me, prop) =>
        {
            if (prop == nameof(LoginSmsViewModel.Country))
            {
                me.Invalidate(); //space for country code may be different
            }
        });

        // Set up bindings for highlight and BoxViewDivider state changes
        SetupInputFocusBindings();

        return grid;
    }

    private SkiaLayout CreateCountrySelector()
    {
        return new SkiaLayout()
            {
                Tag = "GridDropdown",
                VerticalOptions = LayoutOptions.Fill,
                Children = new List<SkiaControl>()
                {
                    new SkiaLayout()
                    {
                        Tag = "RowSelect",
                        Type = LayoutType.Row,
                        VerticalOptions = LayoutOptions.Center,
                        Spacing = 0,
                        Children = new List<SkiaControl>()
                        {
                            new SkiaSvg()
                                {
                                    StyleId = "Flag",
                                    Margin = new Microsoft.Maui.Thickness(0, 0, 4, 0),
                                    HeightRequest = 24,
                                    VerticalOptions = LayoutOptions.Center,
                                    WidthRequest = 34,
                                    Aspect = TransformAspect.Fill
                                }
                                .ObservePropertyOn(
                                    Model,
                                    () => Model.Country,
                                    nameof(Model.Country),
                                    nameof(Model.Country.SkiaFlag),
                                    (me) => { me.SvgString = Model.Country.SkiaFlag; }
                                ),

                            //   .Adapt((svg) => { svg.SetBinding(SkiaSvg.SvgStringProperty, "Country.SkiaFlag"); }),

                            new SkiaSvg()
                            {
                                Margin = new Microsoft.Maui.Thickness(1, 1, 0, 0),
                                HorizontalOptions = LayoutOptions.Start,
                                TintColor = AppColors.IconSecondary,
                                VerticalOptions = LayoutOptions.Fill,
                                WidthRequest = 10,
                                SvgString = App.Current.Resources.Get<string>("SvgEdit")
                            }.Assign(out CountryDropdownIcon)
                        }
                    },

                    // Bottom line
                    new SkiaShape()
                    {
                        BackgroundColor = AppColors.Line,
                        HeightRequest = 1,
                        HorizontalOptions = LayoutOptions.Fill,
                        VerticalOptions = LayoutOptions.End
                    },

                    // SkiaHotspot for country selection
                    new SkiaHotspot()
                    {
                        Margin = new Microsoft.Maui.Thickness(-6),
                        TransformView = GridDropdown,
                        AnimationTapped = SkiaTouchAnimation.Ripple
                    }.Adapt((hotspot) =>
                    {
                        hotspot.SetBinding(SkiaHotspot.CommandTappedProperty, "CommandSelectCountry");

                        // Setup triggers for the dropdown icon based on touch state
                        hotspot.PropertyChanged += (s, e) =>
                        {
                            if (e.PropertyName == nameof(SkiaHotspot.TouchDown))
                            {
                                if (hotspot.TouchDown)
                                    CountryDropdownIcon.TintColor = AppColors.Icon;
                                else
                                    CountryDropdownIcon.TintColor = AppColors.IconSecondary;
                            }
                        };
                    })
                }
            }.Assign(out GridDropdown)
            .SetGrid(0, 0);
    }

    private SkiaLayout CreateEnterCodeStep()
    {
        PinPanel InputPin = null;

        return new ScreenVerticalStack()
            {
                VerticalOptions = LayoutOptions.Fill,
                Tag = "ContentEnterCode",
                Children = new List<SkiaControl>()
                {
                    new SkiaLabel()
                    {
                        TextColor = AppColors.Text,
                        FontSize = 13,
                        HorizontalOptions = LayoutOptions.Fill,
                        Text = ResStrings.CheckYourSms
                    },

                    new SkiaLabel()
                    {
                        TextColor = AppColors.Text,
                        Margin = new Microsoft.Maui.Thickness(0, -10, 0, 1),
                        FontFamily = "FontTextBold",
                        FontSize = 15,
                        HorizontalOptions = LayoutOptions.Fill,
                    }.Adapt((label) => { label.SetBinding(SkiaLabel.TextProperty, "DisplayTel"); }),

                    new SkiaLabel()
                    {
                        Margin = new Microsoft.Maui.Thickness(0, -10, 0, 0),
                        FontSize = 13,
                        HorizontalOptions = LayoutOptions.Fill,
                        TextColor = AppColors.Text,
                        Text = ResStrings.EnterItBelow
                    },

                    new PinPanel()
                    {
                        Tag = "PinPanelCode",
                        Margin = new Microsoft.Maui.Thickness(0, -8, 0, 0),
                        HorizontalOptions = LayoutOptions.Start,
                        VerticalOptions = LayoutOptions.Start,
                        HeightRequest = 49,
                        Spacing = 8
                    }.Adapt((layout) =>
                    {
                        layout.SetBinding(PinPanel.ChangeToResetProperty, "ChangeToUpdateStep1");
                        layout.SetBinding(PinPanel.FinishedCommandProperty, "CommandSubmitCode");
                        //layout.SetBinding(PinPanel.ToggleKeyboardProperty, "ToggleKeyboard2");

                        layout.LayoutIsReady += (s, a) =>
                        {
                            if (Model.IsStep1On)
                            {
                                Tasks.StartDelayed(TimeSpan.FromMilliseconds(100), () => { layout.Focus(); });
                            }
                        };

                        layout.VisibilityChanged += (s, v) =>
                        {
                            if (v)
                            {
                                Tasks.StartDelayed(TimeSpan.FromMilliseconds(500), () => { layout.Focus(); });
                            }
                        };
                    }).Assign(out InputPin),

                    CreateResendSmsSection(),

                    CreateNavigationBackButton()
                }
            }
            .ObserveBindingContext<SkiaLayout, LoginSmsViewModel>((me, vm, prop) =>
            {
                if (prop == nameof(vm.ProcessStep) || prop == nameof(BindingContext))
                {
                    me.IsVisible = vm.ProcessStep == 1;
                }
            })
            .Adapt((layout) =>
            {
                layout.VisibilityChanged += (s, v) =>
                {
                    if (v)
                    {
                        Tasks.StartDelayed(TimeSpan.FromMilliseconds(100), () => { InputPin.Focus(); });
                    }
                };
            });
    }

    private SkiaLayout CreateResendSmsSection()
    {
        return new SkiaLayout()
        {
            HeightRequest = 50,
            HorizontalOptions = LayoutOptions.Fill,
            Children = new List<SkiaControl>()
            {
                new SkiaLabel()
                {
                    Tag = "LabelResend",
                    FontFamily = "FontText",
                    FallbackCharacter = '?',
                    FontSize = 12,
                    TextColor = AppColors.TextSecondary,
                    Spans =
                    {
                        new TextSpan
                        {
                            Text = ResStrings.HaveNoCodeQuestion,
                            TextColor = AppColors.TextSecondary
                        },
                        new TextSpan
                        {
                            Text = ResStrings.LinkSendAgain,
                            TextColor = AppColors.Text,
                            UnderlineWidth = -1,
                            Underline = true
                        }
                    }
                }.Assign(out LabelResend),

                new SkiaHotspot()
                {
                    TransformView = LabelResend,
                    AnimationTapped = SkiaTouchAnimation.Ripple
                }.Adapt((hotspot) => { hotspot.SetBinding(SkiaHotspot.CommandTappedProperty, "CommandResendSMS"); })
            }
        }.Adapt((grid) => { grid.SetBinding(IsVisibleProperty, "CanResend"); });
    }

    private SkiaLayout CreateNavigationBackButton()
    {
        return new SkiaLayout()
        {
            VerticalOptions = LayoutOptions.End,
            Margin = new(0, 0, 0, 40),
            UseCache = SkiaCacheType.Operations,
            HeightRequest = 48,
            WidthRequest = 50,
            Children = new List<SkiaControl>()
            {
                new SkiaLabel()
                {
                },

                new SkiaSvg()
                {
                    Tag = "IconClose",
                    SvgString = App.Current.Resources.Get<string>("SvgGoBack"),
                    TintColor = AppColors.IconSecondary,
                    //UseGradient = false,
                    //StartColor = AppColors.AccentLight.MultiplyAlpha(1.0f),
                    //EndColor = AppColors.AccentPrimary.MultiplyAlpha(1.0f),
                    //StartXRatio = 0,
                    //StartYRatio = 0.5f,
                    //EndXRatio = 1,
                    //EndYRatio = 0.5f,
                    //VerticalOptions = LayoutOptions.Center,
                    WidthRequest = 20,
                    LockRatio = 1
                }.Assign(out IconClose),

                new SkiaHotspot()
                {
                    Margin = new Microsoft.Maui.Thickness(0),
                    HeightRequest = 50,
                    HorizontalOptions = LayoutOptions.Fill,
                    VerticalOptions = LayoutOptions.Fill,
                    WidthRequest = 100,
                    AnimationTapped = SkiaTouchAnimation.Ripple,
                    TransformView = IconClose
                }.Adapt((hotspot) =>
                {
#if DEBUG
                    hotspot.SetBinding(SkiaHotspot.CommandTappedProperty, "CommandPrevStep");
#else
                        // Compiled bindings
                        hotspot.SetBinding(SkiaHotspot.CommandTappedProperty, 
                            static (LoginSmsViewModel vm)  => vm.CommandPrevStep);
#endif
                })
            }
        };
    }

    private void SetupInputFocusBindings()
    {
        //PanelInputTel.PropertyChanged += (s, e) =>
        //{
        //    if (e.PropertyName == nameof(PinMaskedPanel.IsFocused))
        //    {
        //        if (PanelInputTel.IsFocused)
        //        {
        //            BoxViewHighlight.BackgroundColor = Colors.Red;// AppColors.BackgroundAccentOpacity8;
        //            BoxViewDivider.BackgroundColor = AppColors.AccentLight;
        //            BoxViewCodeDivider.BackgroundColor = AppColors.AccentLight;
        //        }
        //        else
        //        {
        //            BoxViewHighlight.BackgroundColor = Colors.Transparent;
        //            BoxViewDivider.BackgroundColor = AppColors.TextSecondary;
        //            BoxViewCodeDivider.BackgroundColor = AppColors.TextSecondary;
        //        }
        //    }
        //};
    }

    // Control references for bindings and interactions
    private SkiaLayout GridDropdown;
    private SkiaSvg CountryDropdownIcon;
    private SkiaLabel LabelCode;
    private PinMaskedPanel PanelInputTel;
    private SkiaShape BoxViewHighlight;
    private SkiaShape BoxViewDivider;
    private SkiaShape BoxViewCodeDivider;
    private SkiaLayout ControlAgree;
    private SkiaLabel LabelResend;
    private SkiaSvg IconClose;

    //protected override void OnNavigating(ShellNavigatingEventArgs e)
    //{
    //    if (e.Target.Location.ToString().Contains("//login"))
    //        Model.GoToStart(); //we are inside stack and we are called again

    //    base.OnNavigating(e);
    //}
}
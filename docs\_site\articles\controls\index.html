<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Controls Overview | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Controls Overview | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="controls-overview">Controls Overview</h1>

<p>DrawnUi positions itself as an engine providing a toolset to create and use custom drawn controls. Out-of-the-box it provides you with base controls that can be used as lego-bricks to composite custom controls, and proposes some useful pre-made custom controls.</p>
<p>The main spirit is to have all controls subclassable and customizable at the maximum possible extent.</p>
<p>DrawnUi provides a comprehensive set of UI controls rendered with SkiaSharp for optimal performance. All controls support platform-specific styling and extensive customization options.</p>
<h2 id="base-drawn-controls">Base Drawn Controls</h2>
<p>These are the fundamental building blocks for creating any custom control:</p>
<ul>
<li><strong>SkiaControl</strong>: Your lego brick to create anything</li>
<li><strong><a href="shapes.html">SkiaShape</a></strong>: Path, Rectangle, Circle, Ellipse, Gauge etc, can wrap other elements to be clipped inside</li>
<li><strong><a href="text.html#skialabel">SkiaLabel</a></strong>: Multiline with many options like dropshadow, gradients etc</li>
<li><strong><a href="images.html#skiaimage">SkiaImage</a></strong>: With many options and filters</li>
<li><strong><a href="images.html#skiasvg">SkiaSvg</a></strong>: SVG rendering with many options</li>
<li><strong><a href="layouts.html#skialayout">SkiaLayout</a></strong>: (Absolute, Grid, Vertical stack, Horizontal stack) with templates support</li>
<li><strong><a href="scroll.html">SkiaScroll</a></strong>: (Horizontal, Vertical, Both) with header, footer, zoom support and adjustable inertia, bounce, snap and much more</li>
<li><strong><a href="input.html#skiahotspot">SkiaHotspot</a></strong>: To handle gestures in a lazy way</li>
<li><strong>SkiaBackdrop</strong>: To apply effects to background below, like blur etc</li>
<li><strong><a href="native-integration.html">SkiaMauiElement</a></strong>: To embed MAUI controls in your canvas</li>
</ul>
<h2 id="custom-controls">Custom Controls</h2>
<p>These are specialized controls derived from the base ones:</p>
<h3 id="text-and-content">Text and Content</h3>
<ul>
<li><strong><a href="text.html#skiamarkdownlabel">SkiaMarkdownLabel</a></strong>: Will find an installed font for any unicode text and auto-create spans for markdown</li>
</ul>
<h3 id="buttons-and-interaction">Buttons and Interaction</h3>
<ul>
<li><strong><a href="buttons.html">SkiaButton</a></strong>: Include anything inside, text, images etc</li>
<li><strong>SkiaRadioButton</strong>: Select something unique from options</li>
</ul>
<h3 id="toggle-controls">Toggle Controls</h3>
<ul>
<li><strong><a href="switches.html#skiaswitch">SkiaSwitch</a></strong>: To be able to toggle anything</li>
<li><strong><a href="switches.html#skiacheckbox">SkiaCheckbox</a></strong>: Platform-styled checkbox</li>
<li><strong><a href="switches.html#skiatoggle">SkiaToggle</a></strong>: Base toggle class for custom toggles</li>
</ul>
<h3 id="progress-and-input">Progress and Input</h3>
<ul>
<li><strong><a href="progress.html">SkiaProgress</a></strong>: To show that you are actually doing something</li>
<li><strong><a href="input.html#skiaslider">SkiaSlider</a></strong>: Including range selection capability</li>
<li><strong><a href="input.html#skiawheelpicker">SkiaWheelPicker</a></strong>: Your iOS-look picker wheel</li>
<li><strong><a href="input.html#skiahotspot">SkiaHotspot</a></strong>: Handle gestures in a lazy way</li>
</ul>
<h3 id="media-and-animation">Media and Animation</h3>
<ul>
<li><strong><a href="animations.html#skialottie">SkiaLottie</a></strong>: With tint customization</li>
<li><strong><a href="animations.html#skiagif">SkiaGif</a></strong>: A dedicated lightweight GIF-player with playback properties</li>
<li><strong><a href="sprites.html">SkiaSprite</a></strong>: High-performance sprite sheet animations</li>
<li><strong>SkiaMediaImage</strong>: A subclassed <code>SkiaImage</code> for displaying any kind of images (image/animated gif/more..)</li>
<li><strong>SkiaCamera</strong>: That day we draw it on the canvas has finally come</li>
</ul>
<h3 id="layout-and-navigation">Layout and Navigation</h3>
<ul>
<li><strong><a href="carousels.html">SkiaCarousel</a></strong>: Swipe and slide controls inside a carousel</li>
<li><strong><a href="drawers.html">SkiaDrawer</a></strong>: To swipe in and out your controls</li>
<li><strong>SkiaScrollLooped</strong>: A subclassed <code>SkiaScroll</code> for neverending scrolls</li>
<li><strong><a href="layouts.html">SkiaDecoratedGrid</a></strong>: To draw shapes between rows and columns</li>
<li><strong><a href="shell.html">SkiaShell</a></strong>: For navigation inside a drawn app</li>
<li><strong>SkiaViewSwitcher</strong>: Switch your views, pop, push and slide</li>
</ul>
<h3 id="specialized-controls">Specialized Controls</h3>
<ul>
<li><strong><a href="progress.html#refreshindicator">RefreshIndicator</a></strong>: Can use Lottie and anything as ActivityIndicator or for your scroll RefreshView</li>
<li><strong>SkiaSpinner</strong>: To test your luck</li>
<li><strong>SkiaHoverMask</strong>: To overlay a clipping shape</li>
<li><strong>SkiaTabsSelector</strong>: Create top and bottom tabs</li>
<li><strong>SkiaLabelFps</strong>: For development</li>
</ul>
<h2 id="control-aliases">Control Aliases</h2>
<p>These are controls that are aliases for other controls, to make porting existing native apps easier:</p>
<ul>
<li><strong>SkiaFrame</strong>: Alias for SkiaShape of Rectangle type (MAUI Frame)</li>
<li><strong>SkiaStack</strong>: For SkiaLayout type Column with default horizontal Fill (MAUI VerticalStackLayout)</li>
<li><strong>SkiaRow</strong>: For SkiaLayout type Row (MAUI HorizontalStackLayout)</li>
<li><strong>SkiaLayer</strong>: For SkiaLayout type Absolute with default horizontal Fill (MAUI Grid with 1 col/row used for layering)</li>
<li><strong>SkiaGrid</strong>: For SkiaLayout type Grid with default horizontal Fill (MAUI Grid)</li>
<li><strong>SkiaWrap</strong>: For SkiaLayout type Wrap with default horizontal Fill (similar to MAUI FlexLayout)</li>
</ul>
<h2 id="getting-started">Getting Started</h2>
<p>To start using DrawnUi controls, wrap them inside a <code>Canvas</code> view in your MAUI app:</p>
<pre><code class="lang-xml">&lt;draw:Canvas&gt;
    &lt;draw:SkiaLayout Type=&quot;Column&quot;&gt;
        &lt;draw:SkiaLabel Text=&quot;Hello DrawnUi!&quot; /&gt;
        &lt;draw:SkiaButton Text=&quot;Click Me&quot; /&gt;
    &lt;/draw:SkiaLayout&gt;
&lt;/draw:Canvas&gt;
</code></pre>
<p>For more examples and detailed documentation, explore the individual control pages linked above.</p>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/index.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>

<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Progress Controls | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Progress Controls | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="progress-controls">Progress Controls</h1>

<p>DrawnUi provides progress controls for showing loading states and progress indicators.</p>
<h2 id="skiaprogress">SkiaProgress</h2>
<p>SkiaProgress is a control for displaying progress indicators and loading states.</p>
<h3 id="basic-usage">Basic Usage</h3>
<pre><code class="lang-xml">&lt;draw:SkiaProgress
    Progress=&quot;0.5&quot;
    WidthRequest=&quot;200&quot;
    HeightRequest=&quot;20&quot;
    BackgroundColor=&quot;LightGray&quot;
    ProgressColor=&quot;Blue&quot; /&gt;
</code></pre>
<h3 id="key-properties">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Progress</code></td>
<td>double</td>
<td>Progress value between 0.0 and 1.0</td>
</tr>
<tr>
<td><code>ProgressColor</code></td>
<td>Color</td>
<td>Color of the progress bar</td>
</tr>
<tr>
<td><code>BackgroundColor</code></td>
<td>Color</td>
<td>Background color of the progress track</td>
</tr>
<tr>
<td><code>IsIndeterminate</code></td>
<td>bool</td>
<td>Whether to show indeterminate progress</td>
</tr>
</tbody>
</table>
<h3 id="indeterminate-progress">Indeterminate Progress</h3>
<p>For loading states where progress is unknown:</p>
<pre><code class="lang-xml">&lt;draw:SkiaProgress
    IsIndeterminate=&quot;True&quot;
    WidthRequest=&quot;200&quot;
    HeightRequest=&quot;20&quot;
    ProgressColor=&quot;Blue&quot; /&gt;
</code></pre>
<h2 id="refreshindicator">RefreshIndicator</h2>
<p>RefreshIndicator can use Lottie animations and any content as an activity indicator for scroll refresh views.</p>
<h3 id="basic-usage-1">Basic Usage</h3>
<pre><code class="lang-xml">&lt;draw:RefreshIndicator
    IsRefreshing=&quot;{Binding IsLoading}&quot;
    RefreshCommand=&quot;{Binding RefreshCommand}&quot;&gt;
    
    &lt;draw:SkiaLottie
        Source=&quot;loading.json&quot;
        AutoPlay=&quot;True&quot;
        Repeat=&quot;-1&quot;
        WidthRequest=&quot;40&quot;
        HeightRequest=&quot;40&quot; /&gt;
        
&lt;/draw:RefreshIndicator&gt;
</code></pre>
<h3 id="with-skiascroll">With SkiaScroll</h3>
<pre><code class="lang-xml">&lt;draw:SkiaScroll RefreshIndicator=&quot;{x:Reference MyRefreshIndicator}&quot;&gt;
    &lt;!-- Your scrollable content --&gt;
    &lt;draw:SkiaLayout Type=&quot;Column&quot;&gt;
        &lt;!-- Content items --&gt;
    &lt;/draw:SkiaLayout&gt;
&lt;/draw:SkiaScroll&gt;

&lt;draw:RefreshIndicator x:Name=&quot;MyRefreshIndicator&quot;
    IsRefreshing=&quot;{Binding IsLoading}&quot;
    RefreshCommand=&quot;{Binding RefreshCommand}&quot;&gt;
    
    &lt;draw:SkiaLabel Text=&quot;Pull to refresh...&quot; /&gt;
    
&lt;/draw:RefreshIndicator&gt;
</code></pre>
<h3 id="key-properties-1">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>IsRefreshing</code></td>
<td>bool</td>
<td>Whether refresh is currently active</td>
</tr>
<tr>
<td><code>RefreshCommand</code></td>
<td>ICommand</td>
<td>Command to execute when refresh is triggered</td>
</tr>
<tr>
<td><code>Content</code></td>
<td>SkiaControl</td>
<td>The visual content of the refresh indicator</td>
</tr>
</tbody>
</table>
<h2 id="examples">Examples</h2>
<h3 id="animated-progress-with-lottie">Animated Progress with Lottie</h3>
<pre><code class="lang-xml">&lt;draw:SkiaLayout Type=&quot;Column&quot; Spacing=&quot;20&quot;&gt;
    
    &lt;!-- Traditional progress bar --&gt;
    &lt;draw:SkiaProgress
        Progress=&quot;{Binding DownloadProgress}&quot;
        WidthRequest=&quot;300&quot;
        HeightRequest=&quot;8&quot;
        BackgroundColor=&quot;LightGray&quot;
        ProgressColor=&quot;Green&quot;
        CornerRadius=&quot;4&quot; /&gt;
    
    &lt;!-- Lottie-based loading indicator --&gt;
    &lt;draw:RefreshIndicator IsRefreshing=&quot;{Binding IsLoading}&quot;&gt;
        &lt;draw:SkiaLottie
            Source=&quot;spinner.json&quot;
            AutoPlay=&quot;True&quot;
            Repeat=&quot;-1&quot;
            WidthRequest=&quot;60&quot;
            HeightRequest=&quot;60&quot;
            ColorTint=&quot;{StaticResource Primary}&quot; /&gt;
    &lt;/draw:RefreshIndicator&gt;
    
&lt;/draw:SkiaLayout&gt;
</code></pre>
<h3 id="code-behind-progress-control">Code-Behind Progress Control</h3>
<pre><code class="lang-csharp">public partial class ProgressPage : ContentPage
{
    private SkiaProgress progressBar;
    
    public ProgressPage()
    {
        InitializeComponent();
        CreateProgressBar();
        SimulateProgress();
    }
    
    private void CreateProgressBar()
    {
        progressBar = new SkiaProgress()
        {
            Progress = 0,
            WidthRequest = 300,
            HeightRequest = 20,
            BackgroundColor = Colors.LightGray,
            ProgressColor = Colors.Blue,
            CornerRadius = 10
        };
        
        MainLayout.Children.Add(progressBar);
    }
    
    private async void SimulateProgress()
    {
        for (int i = 0; i &lt;= 100; i++)
        {
            progressBar.Progress = i / 100.0;
            await Task.Delay(50);
        }
    }
}
</code></pre>
<h2 id="performance-tips">Performance Tips</h2>
<ul>
<li>Use <code>IsIndeterminate=&quot;True&quot;</code> for unknown progress durations</li>
<li>Combine with Lottie animations for engaging loading experiences</li>
<li>Consider using RefreshIndicator with SkiaScroll for pull-to-refresh functionality</li>
<li>Progress animations are optimized for smooth 60fps rendering</li>
</ul>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/progress.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>

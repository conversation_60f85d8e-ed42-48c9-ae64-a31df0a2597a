<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Building Game UIs and Interactive Games with DrawnUi.Maui | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Building Game UIs and Interactive Games with DrawnUi.Maui | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="building-game-uis-and-interactive-games-with-drawnuimaui">Building Game UIs and Interactive Games with DrawnUi.Maui</h1>

<p>DrawnUi.Maui is not just for business apps—it’s also a platform for building interactive 2D-games and game-like UIs. With direct SkiaSharp rendering, real-time animation, and flexible input handling, you can create everything from simple arcade games to rich, animated dashboards.</p>
<p>Mainly tested for  scenarios</p>
<h2 id="why-use-drawnuimaui-for-2d-games">Why Use DrawnUi.Maui for 2D-Games?</h2>
<ul>
<li><strong>True cross-platform</strong><br>
No more Windows-only, your game will look and run the same way on Apple and Android devices along with Windows and Mac desktops, with sound and all the goodies .NET MAUI provides for those platforms.</li>
<li><strong>High-performance canvas rendering</strong><br>
On most platforms DrawnUi uses its own hardware-accelerated views handlers to ensure smooth display-synched multi-buffered rendering. They also provide an experimental &quot;retained feature&quot; (Canvas property <code>RenderingMode=RenderingModeType.AcceleratedRetained</code>) that on every frame returns to you the previous frame result for you to draw on top, instead of an empty surface. This can be useful for old-shool game engines that save/restore background for moving sprites, and other similar scenarios, for drawing only changed areas and obtain max performance.</li>
<li><strong>Frame-based animation controls</strong><br>
You can use built-in frame-based contols like SkiaSprite, SkiaLottie, SkiaGif (create your own) to easily use your existing art.</li>
<li><strong>Drawing primitives</strong><br>
You can use SkiaShape, SkiaLabel, SkiaImage, and other controls to build your game UI in a fast and declarative way.</li>
<li><strong>Flexible input</strong><br>
Canvas supports all multi-touch gestures, desktop mouse and keyboard. Controllers are on the roadmap.</li>
<li><strong>Custom drawing</strong><br>
You can override <code>Paint</code> inside any control and use all the power of SkiaSharp to draw directly on the canvas.</li>
<li><strong>Easy integration</strong><br>
with other .NET MAUI native controls and layouts to use-platform-specific features.</li>
</ul>
<h2 id="game-loop-and-real-time-updates">Game Loop and Real-Time Updates</h2>
<p>For interactive games, you need a game loop that updates the game state and redraws the UI at regular intervals.</p>
<h3 id="example-simple-game-loop">Example: Simple Game Loop</h3>
<pre><code class="lang-csharp">public class GamePage : SkiaLayout
{
    private bool _running;
    private Timer _timer;
    private int _playerX = 100;
    private int _playerY = 100;

    public GamePage()
    {
        // Start the game loop
        _running = true;
        _timer = new Timer(OnTick, null, 0, 16); // ~60 FPS
    }

    private void OnTick(object state)
    {
        if (!_running) return;
        // Update game state
        _playerX += 1;
        // Redraw
        Invalidate();
    }

    protected override void OnDraw(SKCanvas canvas, SKRect destination, float scale)
    {
        base.OnDraw(canvas, destination, scale);
        // Draw player as a circle
        canvas.DrawCircle(_playerX, _playerY, 20, new SKPaint { Color = SKColors.Blue });
    }

    protected override void OnDisposing()
    {
        _running = false;
        _timer?.Dispose();
        base.OnDisposing();
    }
}
</code></pre>
<h2 id="using-skiasprite-for-animated-characters">Using SkiaSprite for Animated Characters</h2>
<p>SkiaSprite makes it easy to animate sprite sheets:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaSprite
    x:Name=&quot;PlayerSprite&quot;
    Source=&quot;character_run.png&quot;
    Columns=&quot;8&quot;
    Rows=&quot;1&quot;
    FramesPerSecond=&quot;12&quot;
    AutoPlay=&quot;True&quot;
    WidthRequest=&quot;128&quot;
    HeightRequest=&quot;128&quot;
    HorizontalOptions=&quot;Center&quot;
    VerticalOptions=&quot;Center&quot; /&gt;
</code></pre>
<p>In code-behind, you can control animation state:</p>
<pre><code class="lang-csharp">PlayerSprite.Start(); // Start animation
PlayerSprite.Stop();  // Stop animation
PlayerSprite.CurrentFrame = 0; // Set frame
</code></pre>
<h2 id="handling-input-tap-drag-and-gestures">Handling Input: Tap, Drag, and Gestures</h2>
<p>DrawnUi.Maui supports rich gesture handling for interactive games:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaHotspot Tapped=&quot;OnPlayerTapped&quot;&gt;
    &lt;DrawUi:SkiaSprite ... /&gt;
&lt;/DrawUi:SkiaHotspot&gt;
</code></pre>
<p>In code-behind:</p>
<pre><code class="lang-csharp">private void OnPlayerTapped(object sender, EventArgs e)
{
    // Respond to tap (e.g., jump, attack)
}
</code></pre>
<p>For drag or swipe, use gesture listeners or override touch methods in your control.</p>
<h2 id="combining-ui-and-game-elements">Combining UI and Game Elements</h2>
<p>You can mix game elements with standard DrawnUi controls:</p>
<pre><code class="lang-xml">&lt;draw:SkiaLayout Type=&quot;Column&quot;&gt;
    &lt;draw:SkiaLabel Text=&quot;Score: 123&quot; FontSize=&quot;24&quot; /&gt;
    &lt;draw:SkiaSprite ... /&gt;
    &lt;draw:SkiaButton Text=&quot;Pause&quot; Clicked=&quot;OnPause&quot; /&gt;
&lt;/draw:SkiaLayout&gt;
</code></pre>
<h2 id="example-simple-tap-game">Example: Simple Tap Game</h2>
<pre><code class="lang-xml">&lt;draw:SkiaLayout&gt;
    &lt;draw:SkiaHotspot Tapped=&quot;OnTap&quot;&gt;
        &lt;draw:SkiaShape Type=&quot;Circle&quot; WidthRequest=&quot;100&quot; HeightRequest=&quot;100&quot; BackgroundColor=&quot;Red&quot; /&gt;
    &lt;/draw:SkiaHotspot&gt;
    &lt;draw:SkiaLabel x:Name=&quot;ScoreLabel&quot; Text=&quot;Score: 0&quot; FontSize=&quot;24&quot; /&gt;
&lt;/draw:SkiaLayout&gt;
</code></pre>
<pre><code class="lang-csharp">private int _score = 0;
private void OnTap(object sender, SkiaGesturesParameters e)
{
    _score++;
    ScoreLabel.Text = $&quot;Score: {_score}&quot;;
}
</code></pre>
<h2 id="tips-for-game-ui-performance">Tips for Game UI Performance</h2>
<ul>
<li>Use <code>Cache=&quot;Operations&quot;</code> or <code>Cache=&quot;Image&quot;</code> for static backgrounds or UI elements</li>
<li>Minimize redraws: only call <code>Invalidate()</code> when needed</li>
<li>Use SkiaLabelFps to monitor frame rate</li>
<li>For complex games, manage game state and rendering in a dedicated class</li>
</ul>
<h2 id="integrating-addons-camera-maps-charts">Integrating Addons (Camera, Maps, Charts)</h2>
<ul>
<li>Use DrawnUi.Maui.Camera for AR or camera-based games</li>
<li>Overlay charts or live data with DrawnUi.Maui.LiveCharts</li>
<li>Add maps or location-based features with DrawnUi.Maui.MapsUi</li>
</ul>
<h2 id="summary">Summary</h2>
<p>DrawnUi.Maui enables you to build interactive, animated, and performant game UIs on any platform. Combine sprites, custom drawing, and flexible input to create unique experiences—whether for games, dashboards, or playful business apps.</p>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/advanced/game-ui.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>

﻿using AppoMobi.Mobile.Views;
using DrawnUi.Controls;

namespace AppoMobi.Mobile;

/// <summary>
/// User profile editor screen
/// </summary>
public class ScreenReviewEditor : AppScreen, IQueryAttributable
{
    public void ApplyQueryAttributes(IDictionary<string, object> query)
    {
        foreach (var key in query.Keys)
        {
            if (query.TryGetValue(key, out var value))
            {
                if (key == "value")
                {
                    Value = (string)value;
                }
                else if (key == "title")
                {
                    _title = (string)value;
                }
                else if (key == "callback")
                {
                    _callback = (Action<string, int>)value;
                }
            }
        }
    }

    private Action<string, int> _callback;
    private string _title;
    private string _value;
    private readonly bool _multiline;

    public string Value
    {
        get { return _value; }
        set
        {
            var newValue = Uri.UnescapeDataString(value ?? string.Empty);
            if (_value != newValue)
            {
                _value = newValue;
                OnPropertyChanged();
                OnParametersSet(_value);
            }
        }
    }

    public int Rating { get; set; }

    private void OnParametersSet(string id)
    {
        Debug.WriteLine($"[ModalScreenEnterText] OnParametersSet: {Value}");
    }

    public ScreenReviewEditor
        (string Title, string value, int rating, Action<string, int> callback)
    {
        _callback = callback;
        _title = Title;
        _value = value;
        _multiline = true;

        Rating = rating;

        BindingContext = this;

        Shell.SetPresentationMode(this, PresentationMode.ModalAnimated);
        BackgroundColor = Colors.Transparent; //for modal

        Margin = UiHelper.ModalInsets;
        HorizontalOptions = LayoutOptions.Fill;
        VerticalOptions = LayoutOptions.Fill;

        Type = LayoutType.Column;
        Spacing = 0;

        Rendered += (s, a) =>
        {
            Tasks.StartDelayed(TimeSpan.FromMilliseconds(500), () =>
            {
                try
                {
                    MainEditor.IsFocused = true;
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
            });
        };

        CreateContent();
    }

    SkiaMauiEditor MainEditor;

    private void CreateContent()
    {
        SkiaLayout? LayoutStars = null;

        void ApplyRating()
        {
            LayoutStars.ItemsSource = Enumerable.Range(1, Rating).ToArray();
        }

        Children = new List<SkiaControl>
        {
            new StatusBarPlaceholder(),

            // HEADER
            new ModalHeader
            {
                Title = _title,
            },

            // CONTENT
            new SkiaShape()
            {
                //dot not cache cuz native element inside
                StrokeColor = AppColors.ControlPrimary,
                StrokeWidth = 1,
                BackgroundColor = AppColors.Background,
                HorizontalOptions = LayoutOptions.Fill,
                VerticalOptions = LayoutOptions.Fill,
                Children =
                {
                    new ScreenVerticalStack()
                    {
                        Padding = new(16),
                        Children = new List<SkiaControl>()
                        {
                            new SkiaLayout()
                            {
                                Type = LayoutType.Column,
                                Spacing = 16,
                                Children = new List<SkiaControl>()
                                {
                                    //STARS
                                    new SkiaLayout()
                                    {
                                        HeightRequest = 64,
                                        HorizontalOptions = LayoutOptions.Fill,
                                        Children = new List<SkiaControl>()
                                        {
                                            //unselected
                                            new SkiaLayout()
                                            {
                                                UseCache = SkiaCacheType.Image,
                                                InputTransparent = true,
                                                Spacing = 0,
                                                Margin = 4,
                                                VerticalOptions = LayoutOptions.Center,
                                                Type = LayoutType.Row,
                                                ItemsSource = new int[] { 1, 2, 3, 4, 5 },
                                                ItemTemplate = new DataTemplate(() =>
                                                {
                                                    var cell = new SkiaSvg()
                                                    {
                                                        UseCache = SkiaCacheType.Operations,
                                                        Margin = 4,
                                                        SvgString = App.Current.Resources.Get<string>("SvgStar"),
                                                        TintColor = Color.Parse("#BBBBBB"),
                                                        HeightRequest = 36,
                                                        LockRatio = 1 //not working
                                                    };
                                                    return cell;
                                                }),
                                            },

                                            //selected
                                            new SkiaLayout()
                                                {
                                                    UseCache = SkiaCacheType.Image,
                                                    InputTransparent = true,
                                                    Spacing = 0,
                                                    Margin = 4,
                                                    VerticalOptions = LayoutOptions.Center,
                                                    Type = LayoutType.Row,
                                                    //ItemsSource = new int[] { 1, 2, 3 },
                                                    ItemTemplate = new DataTemplate(() =>
                                                    {
                                                        var cell = new SkiaSvg()
                                                        {
                                                            UseCache = SkiaCacheType.Operations,
                                                            Margin = 4,
                                                            SvgString = App.Current.Resources.Get<string>("SvgStar"),
                                                            TintColor = AppColors.Primary,
                                                            HeightRequest = 36,
                                                            LockRatio = 1 //not working
                                                        };
                                                        return cell;
                                                    }),
                                                }
                                                .Initialize((me) => { ApplyRating(); })
                                                .Assign(out LayoutStars),

                                            //hotspots
                                            new SkiaLayout()
                                            {
                                                UseCache = SkiaCacheType.Image,
                                                Spacing = 0,
                                                VerticalOptions = LayoutOptions.Fill,
                                                Type = LayoutType.Row,
                                                ItemsSource = new int[] { 1, 2, 3, 4, 5 },
                                                ItemTemplate = new DataTemplate(() =>
                                                {
                                                    var cell = new SkiaControl
                                                    {
                                                        WidthRequest = 36 + 8,
                                                        VerticalOptions = LayoutOptions.Fill,
                                                        //BackgroundColor = Color.Parse("#33ff0000")
                                                    }.OnTapped((me) =>
                                                    {
                                                        Rating = (int)(me.BindingContext);
                                                        ApplyRating();
                                                    });
                                                    return cell;
                                                }),
                                            },
                                        }
                                    },

                                    //App.Instance.Presentation.Shell.Elements.CreateInputSection(_title, _value, 2)
                                    // Entry Field Container
                                    new AppFrame()
                                    {
                                        Padding = new(4, 2),
                                        Children =
                                        {
                                            new SkiaLayout()
                                            {
                                                HorizontalOptions = LayoutOptions.Fill,
                                                Type = LayoutType.Column,
                                                Spacing = 0,
                                                Children = new List<SkiaControl>()
                                                {
                                                    // Text Entry
                                                    new AppEditor(_value)
                                                        {
                                                            //BackgroundColor = Colors.Red,
                                                            MaxLines = 1,
                                                            HeightRequest = 32,
                                                            Placeholder = "...",
                                                            PlaceholderColor = AppColors.Primary,
                                                            Padding = new Thickness(0, 2, 0, 4),
                                                            VerticalOptions = LayoutOptions.Start,
                                                        }.Assign(out MainEditor)
                                                        .Adapt(entry =>
                                                        {
                                                            //todo size, lines upon settings
                                                            if (_multiline)
                                                            {
                                                                entry.MaxLines = -1;
                                                                entry.HeightRequest = 180;
                                                                entry.VerticalOptions = LayoutOptions.Start;
                                                            }

                                                            entry.SetBinding(SkiaMauiEditor.TextProperty, $"Value",
                                                                BindingMode.TwoWay);

                                                            // Set up focus trigger
                                                            entry.PropertyChanged += (s, e) =>
                                                            {
                                                                if (e.PropertyName == nameof(entry.IsFocused))
                                                                {
                                                                    entry.TextColor = entry.IsFocused
                                                                        ? AppColors.PrimaryDark
                                                                        : AppColors.Text;
                                                                }
                                                            };
                                                        }),
                                                }
                                            }
                                        }
                                    }
                                }
                            },

                            // Submit Button
                            new ButtonMedium()
                            {
                                UseCache = SkiaCacheType.Image,
                                HorizontalOptions = LayoutOptions.Center,
                                WidthRequest = 250,
                                Margin = 0,
                                HeightRequest = 44,
                                Text = ResStrings.BtnOk,
                            }.OnTapped((btn) =>
                            {
                                App.Instance.Presentation.Shell.GoBack(true);
                                _callback?.Invoke(Value, Rating);
                            }),

                            // Keyboard Offset
                            new KeyboardPlaceholder()
                        }
                    }
                }
            }
        };
    }
}
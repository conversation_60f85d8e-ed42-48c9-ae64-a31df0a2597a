### YamlMime:ManagedReference
items:
- uid: DrawnUi.Views.Canvas
  commentId: T:DrawnUi.Views.Canvas
  id: Canvas
  parent: DrawnUi.Views
  children:
  - DrawnUi.Views.Canvas.#ctor
  - DrawnUi.Views.Canvas.AdaptHeightContraintToContentRequest(System.Single,DrawnUi.Draw.ScaledSize,System.Double)
  - DrawnUi.Views.Canvas.AdaptHeightContraintToRequest(System.Double)
  - DrawnUi.Views.Canvas.AdaptSizeRequestToContent(System.Double,System.Double)
  - DrawnUi.Views.Canvas.AdaptSizeToContentIfNeeded(System.Double,System.Double,System.Boolean)
  - DrawnUi.Views.Canvas.AdaptWidthContraintToContentRequest(System.Single,DrawnUi.Draw.ScaledSize,System.Double)
  - DrawnUi.Views.Canvas.AdaptWidthContraintToRequest(System.Double)
  - DrawnUi.Views.Canvas.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  - DrawnUi.Views.Canvas.BreakLine
  - DrawnUi.Views.Canvas.Clear
  - DrawnUi.Views.Canvas.CollectGarbage(System.Int64)
  - DrawnUi.Views.Canvas.Content
  - DrawnUi.Views.Canvas.ContentProperty
  - DrawnUi.Views.Canvas.ContentSize
  - DrawnUi.Views.Canvas.Context
  - DrawnUi.Views.Canvas.CreateDebugPointer
  - DrawnUi.Views.Canvas.DebugPointer
  - DrawnUi.Views.Canvas.DelayNanosBetweenGC
  - DrawnUi.Views.Canvas.DisableUpdates
  - DrawnUi.Views.Canvas.Draw(DrawnUi.Draw.DrawingContext)
  - DrawnUi.Views.Canvas.DumpDebug
  - DrawnUi.Views.Canvas.EnableUpdates
  - DrawnUi.Views.Canvas.FirstPanThreshold
  - DrawnUi.Views.Canvas.Gestures
  - DrawnUi.Views.Canvas.GesturesDebugColor
  - DrawnUi.Views.Canvas.GesturesDebugColorProperty
  - DrawnUi.Views.Canvas.GesturesProperty
  - DrawnUi.Views.Canvas.GetMeasuringRectForChildren(System.Single,System.Single,System.Single)
  - DrawnUi.Views.Canvas.HadInput
  - DrawnUi.Views.Canvas.Invalidate
  - DrawnUi.Views.Canvas.InvalidateMeasure
  - DrawnUi.Views.Canvas.IsSavedGesture(AppoMobi.Maui.Gestures.TouchActionResult)
  - DrawnUi.Views.Canvas.LineBreaks
  - DrawnUi.Views.Canvas.Measure(System.Single,System.Single)
  - DrawnUi.Views.Canvas.MeasureChild(DrawnUi.Draw.SkiaControl,System.Double,System.Double,System.Double)
  - DrawnUi.Views.Canvas.MeasureOverride(System.Double,System.Double)
  - DrawnUi.Views.Canvas.OnChildAdded(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Views.Canvas.OnDisposing
  - DrawnUi.Views.Canvas.OnGestureEvent(AppoMobi.Maui.Gestures.TouchActionType,AppoMobi.Maui.Gestures.TouchActionEventArgs,AppoMobi.Maui.Gestures.TouchActionResult)
  - DrawnUi.Views.Canvas.OnGesturesAttachChanged
  - DrawnUi.Views.Canvas.OnHandlerChanged
  - DrawnUi.Views.Canvas.OnParentChanged
  - DrawnUi.Views.Canvas.OnPropertyChanged(System.String)
  - DrawnUi.Views.Canvas.OnSizeChanged
  - DrawnUi.Views.Canvas.PlayRippleAnimation(Microsoft.Maui.Graphics.Color,System.Double,System.Double,System.Boolean)
  - DrawnUi.Views.Canvas.PlayShimmerAnimation(Microsoft.Maui.Graphics.Color,System.Single,System.Single,System.Int32,System.Boolean)
  - DrawnUi.Views.Canvas.ProcessGestures(DrawnUi.Draw.SkiaGesturesParameters)
  - DrawnUi.Views.Canvas.ReceivedInput
  - DrawnUi.Views.Canvas.ReserveSpaceAround
  - DrawnUi.Views.Canvas.ReserveSpaceAroundProperty
  - DrawnUi.Views.Canvas.SetChildren(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})
  - DrawnUi.Views.Canvas.SetContent(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Views.Canvas.SignalInput(DrawnUi.Draw.ISkiaGestureListener,AppoMobi.Maui.Gestures.TouchActionResult)
  - DrawnUi.Views.Canvas.Tapped
  - DrawnUi.Views.Canvas.TimeLastGC
  langs:
  - csharp
  - vb
  name: Canvas
  nameWithType: Canvas
  fullName: DrawnUi.Views.Canvas
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Canvas
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: >-
    Optimized DrawnView having only one child inside Content property. Can autosize to to children size.

    For all drawn app put this directly inside the ContentPage as root view.

    If you put this inside some Maui control like Grid whatever expect more GC collections during animations making them somewhat less fluid.
  example: []
  syntax:
    content: >-
      [ContentProperty("Content")]

      public class Canvas : DrawnView, INotifyPropertyChanged, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IAnimatable, IViewController, IVisualElementController, IElementController, IGestureController, IGestureRecognizers, IPropertyMapperView, IHotReloadableView, IReplaceableView, ILayout, ILayoutController, IContentView, IView, IElement, ITransform, IPadding, ICrossPlatformLayout, IDrawnBase, IDisposable, ICanBeUpdatedWithContext, ICanBeUpdated, IAnimatorsManager, IVisualTreeElement, IGestureListener
    content.vb: >-
      <ContentProperty("Content")>

      Public Class Canvas Inherits DrawnView Implements INotifyPropertyChanged, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IAnimatable, IViewController, IVisualElementController, IElementController, IGestureController, IGestureRecognizers, IPropertyMapperView, IHotReloadableView, IReplaceableView, ILayout, ILayoutController, IContentView, IView, IElement, ITransform, IPadding, ICrossPlatformLayout, IDrawnBase, IDisposable, ICanBeUpdatedWithContext, ICanBeUpdated, IAnimatorsManager, IVisualTreeElement, IGestureListener
  inheritance:
  - System.Object
  - Microsoft.Maui.Controls.BindableObject
  - Microsoft.Maui.Controls.Element
  - Microsoft.Maui.Controls.StyleableElement
  - Microsoft.Maui.Controls.NavigableElement
  - Microsoft.Maui.Controls.VisualElement
  - Microsoft.Maui.Controls.View
  - Microsoft.Maui.Controls.Compatibility.Layout
  - Microsoft.Maui.Controls.TemplatedView
  - Microsoft.Maui.Controls.ContentView
  - DrawnUi.Views.DrawnView
  implements:
  - System.ComponentModel.INotifyPropertyChanged
  - Microsoft.Maui.Controls.IEffectControlProvider
  - Microsoft.Maui.IToolTipElement
  - Microsoft.Maui.IContextFlyoutElement
  - Microsoft.Maui.Controls.IAnimatable
  - Microsoft.Maui.Controls.IViewController
  - Microsoft.Maui.Controls.IVisualElementController
  - Microsoft.Maui.Controls.IElementController
  - Microsoft.Maui.Controls.Internals.IGestureController
  - Microsoft.Maui.Controls.IGestureRecognizers
  - Microsoft.Maui.IPropertyMapperView
  - Microsoft.Maui.HotReload.IHotReloadableView
  - Microsoft.Maui.IReplaceableView
  - Microsoft.Maui.Controls.ILayout
  - Microsoft.Maui.Controls.ILayoutController
  - Microsoft.Maui.IContentView
  - Microsoft.Maui.IView
  - Microsoft.Maui.IElement
  - Microsoft.Maui.ITransform
  - Microsoft.Maui.IPadding
  - Microsoft.Maui.ICrossPlatformLayout
  - DrawnUi.Draw.IDrawnBase
  - System.IDisposable
  - DrawnUi.Draw.ICanBeUpdatedWithContext
  - DrawnUi.Draw.ICanBeUpdated
  - DrawnUi.Draw.IAnimatorsManager
  - Microsoft.Maui.IVisualTreeElement
  - AppoMobi.Maui.Gestures.IGestureListener
  inheritedMembers:
  - DrawnUi.Views.DrawnView.DumpLayersTree(DrawnUi.Draw.VisualLayer,System.String,System.Boolean,System.Int32)
  - DrawnUi.Views.DrawnView.Diagnostics
  - DrawnUi.Views.DrawnView.Update
  - DrawnUi.Views.DrawnView.IsUsingHardwareAcceleration
  - DrawnUi.Views.DrawnView.NeedRedraw
  - DrawnUi.Views.DrawnView.IsDirty
  - DrawnUi.Views.DrawnView.IsVisibleInViewTree
  - DrawnUi.Views.DrawnView.TakeScreenShot(System.Action{SkiaSharp.SKImage})
  - DrawnUi.Views.DrawnView.InvalidateByChild(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Views.DrawnView.UpdateByChild(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Views.DrawnView.GetOnScreenVisibleArea(DrawnUi.Draw.DrawingContext,System.Numerics.Vector2)
  - DrawnUi.Views.DrawnView.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  - DrawnUi.Views.DrawnView.HandlerWasSet
  - DrawnUi.Views.DrawnView.TakeScreenShotInternal(SkiaSharp.SKSurface)
  - DrawnUi.Views.DrawnView.PostponeExecutionBeforeDraw(System.Action)
  - DrawnUi.Views.DrawnView.PostponeExecutionAfterDraw(System.Action)
  - DrawnUi.Views.DrawnView.ExecuteBeforeDraw
  - DrawnUi.Views.DrawnView.ExecuteAfterDraw
  - DrawnUi.Views.DrawnView.CallbackScreenshot
  - DrawnUi.Views.DrawnView.RenderingSubscribers
  - DrawnUi.Views.DrawnView.SubscribeToRenderingFinished(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Views.DrawnView.UsubscribeFromRenderingFinished(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Views.DrawnView.RegisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
  - DrawnUi.Views.DrawnView.UnregisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
  - DrawnUi.Views.DrawnView.LockIterateListeners
  - DrawnUi.Views.DrawnView.GestureListeners
  - DrawnUi.Views.DrawnView.DrawingRect
  - DrawnUi.Views.DrawnView.AddAnimator(DrawnUi.Draw.ISkiaAnimator)
  - DrawnUi.Views.DrawnView.RemoveAnimator(System.Guid)
  - DrawnUi.Views.DrawnView.RegisterAnimator(DrawnUi.Draw.ISkiaAnimator)
  - DrawnUi.Views.DrawnView.UnregisterAnimator(System.Guid)
  - DrawnUi.Views.DrawnView.UnregisterAllAnimatorsByType(System.Type)
  - DrawnUi.Views.DrawnView.UnregisterAllAnimatorsByParent(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Views.DrawnView.SetViewTreeVisibilityByParent(DrawnUi.Draw.SkiaControl,System.Boolean)
  - DrawnUi.Views.DrawnView.SetPauseStateOfAllAnimatorsByParent(DrawnUi.Draw.SkiaControl,System.Boolean)
  - DrawnUi.Views.DrawnView.ExecutePostAnimators(DrawnUi.Draw.DrawingContext)
  - DrawnUi.Views.DrawnView.PostAnimators
  - DrawnUi.Views.DrawnView.AnimatingControls
  - DrawnUi.Views.DrawnView.FrameTimeInterpolator
  - DrawnUi.Views.DrawnView.mLastFrameTime
  - DrawnUi.Views.DrawnView.ExecuteAnimators(System.Int64)
  - DrawnUi.Views.DrawnView.OnCanvasViewChanged
  - DrawnUi.Views.DrawnView.CanvasView
  - DrawnUi.Views.DrawnView.SetDeviceOrientation(System.Int32)
  - DrawnUi.Views.DrawnView.DeviceRotationChanged
  - DrawnUi.Views.DrawnView.DisplayRotationProperty
  - DrawnUi.Views.DrawnView.DeviceRotation
  - DrawnUi.Views.DrawnView.HasHandler
  - DrawnUi.Views.DrawnView.DisconnectedHandler
  - DrawnUi.Views.DrawnView.NeedGlobalRefreshCount
  - DrawnUi.Views.DrawnView.UpdateGlobal
  - DrawnUi.Views.DrawnView.NeedMeasureDrawn
  - DrawnUi.Views.DrawnView.OnCanRenderChanged(System.Boolean)
  - DrawnUi.Views.DrawnView.ConnectedHandler
  - DrawnUi.Views.DrawnView.FixDensity
  - DrawnUi.Views.DrawnView.StopDrawingWhenUpdateIsLocked
  - DrawnUi.Views.DrawnView.TimeDrawingStarted
  - DrawnUi.Views.DrawnView.TimeDrawingComplete
  - DrawnUi.Views.DrawnView.InvalidateViewport
  - DrawnUi.Views.DrawnView.Repaint
  - DrawnUi.Views.DrawnView.OrderedDraw
  - DrawnUi.Views.DrawnView.ResetUpdate
  - DrawnUi.Views.DrawnView.InvalidatedCanvas
  - DrawnUi.Views.DrawnView.IsRendering
  - DrawnUi.Views.DrawnView.Delayed
  - DrawnUi.Views.DrawnView.GetDensity
  - DrawnUi.Views.DrawnView.CreateSkiaView
  - DrawnUi.Views.DrawnView.DestroySkiaView
  - DrawnUi.Views.DrawnView.IsDisposing
  - DrawnUi.Views.DrawnView.Dispose
  - DrawnUi.Views.DrawnView.ViewDisposing
  - DrawnUi.Views.DrawnView.WillDispose
  - DrawnUi.Views.DrawnView.OnDispose
  - DrawnUi.Views.DrawnView.InvalidateParents
  - DrawnUi.Views.DrawnView.OnSuperviewShouldRenderChanged(System.Boolean)
  - DrawnUi.Views.DrawnView.InvalidateChildren
  - DrawnUi.Views.DrawnView.PrintDebug(System.String)
  - DrawnUi.Views.DrawnView.NeedAutoSize
  - DrawnUi.Views.DrawnView.NeedAutoHeight
  - DrawnUi.Views.DrawnView.NeedAutoWidth
  - DrawnUi.Views.DrawnView.ShouldInvalidateByChildren
  - DrawnUi.Views.DrawnView.UpdateLocksProperty
  - DrawnUi.Views.DrawnView.UpdateLocks
  - DrawnUi.Views.DrawnView.Uid
  - DrawnUi.Views.DrawnView.LinearGradientAngleToPoints(System.Double)
  - DrawnUi.Views.DrawnView.IsGhost
  - DrawnUi.Views.DrawnView.Clipping
  - DrawnUi.Views.DrawnView.CreateClip(System.Object,System.Boolean,SkiaSharp.SKPath)
  - DrawnUi.Views.DrawnView.Tag
  - DrawnUi.Views.DrawnView.IsRootView(System.Single,System.Single,SkiaSharp.SKRect)
  - DrawnUi.Views.DrawnView.CalculateLayout(SkiaSharp.SKRect,System.Double,System.Double,System.Double)
  - DrawnUi.Views.DrawnView.Arrange(SkiaSharp.SKRect,System.Double,System.Double,System.Double)
  - DrawnUi.Views.DrawnView.MeasuredSize
  - DrawnUi.Views.DrawnView.NeedMeasure
  - DrawnUi.Views.DrawnView.SetMeasured(System.Single,System.Single,System.Single)
  - DrawnUi.Views.DrawnView.OnMeasured
  - DrawnUi.Views.DrawnView.Measured
  - DrawnUi.Views.DrawnView.IsDisposed
  - DrawnUi.Views.DrawnView.DrawingThreadId
  - DrawnUi.Views.DrawnView.WasRendered
  - DrawnUi.Views.DrawnView.WasDrawn
  - DrawnUi.Views.DrawnView.WillDraw
  - DrawnUi.Views.DrawnView.WillFirstTimeDraw
  - DrawnUi.Views.DrawnView.LastDrawnRect
  - DrawnUi.Views.DrawnView.OnStartRendering(SkiaSharp.SKCanvas)
  - DrawnUi.Views.DrawnView.OnFinalizeRendering
  - DrawnUi.Views.DrawnView.AvailableDestination
  - DrawnUi.Views.DrawnView.GetOrderedSubviews
  - DrawnUi.Views.DrawnView.InvalidateViewsList
  - DrawnUi.Views.DrawnView._fps
  - DrawnUi.Views.DrawnView.FrameTime
  - DrawnUi.Views.DrawnView.CanvasFps
  - DrawnUi.Views.DrawnView.FPS
  - DrawnUi.Views.DrawnView.LockDraw
  - DrawnUi.Views.DrawnView.FrameNumber
  - DrawnUi.Views.DrawnView.DisposeObject(System.IDisposable)
  - DrawnUi.Views.DrawnView.DisposeManager
  - DrawnUi.Views.DrawnView.PostponeInvalidation(DrawnUi.Draw.SkiaControl,System.Action)
  - DrawnUi.Views.DrawnView.InvalidationActionsA
  - DrawnUi.Views.DrawnView.InvalidationActionsB
  - DrawnUi.Views.DrawnView.GetFrontInvalidations
  - DrawnUi.Views.DrawnView.GetBackInvalidations
  - DrawnUi.Views.DrawnView.SwapInvalidations
  - DrawnUi.Views.DrawnView.DrawingThreads
  - DrawnUi.Views.DrawnView.DirtyChildrenTracker
  - DrawnUi.Views.DrawnView.SetChildAsDirty(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Views.DrawnView.CommitInvalidations
  - DrawnUi.Views.DrawnView.LockStartOffscreenQueue
  - DrawnUi.Views.DrawnView.semaphoreOffscreenProcess
  - DrawnUi.Views.DrawnView.KickOffscreenCacheRendering
  - DrawnUi.Views.DrawnView.PushToOffscreenRendering(DrawnUi.Draw.SkiaControl,System.Threading.CancellationToken)
  - DrawnUi.Views.DrawnView.ProcessOffscreenCacheRenderingAsync
  - DrawnUi.Views.DrawnView.RenderingScaleProperty
  - DrawnUi.Views.DrawnView.OnDensityChanged
  - DrawnUi.Views.DrawnView.RenderingScale
  - DrawnUi.Views.DrawnView.RenderingModeProperty
  - DrawnUi.Views.DrawnView.RenderingMode
  - DrawnUi.Views.DrawnView.CanRenderOffScreenProperty
  - DrawnUi.Views.DrawnView.CanRenderOffScreen
  - DrawnUi.Views.DrawnView.CanDraw
  - DrawnUi.Views.DrawnView.IsHiddenInViewTree
  - DrawnUi.Views.DrawnView.NeedCheckParentVisibility
  - DrawnUi.Views.DrawnView.StreamFromString(System.String)
  - DrawnUi.Views.DrawnView.PaintSystem
  - DrawnUi.Views.DrawnView.Destination
  - DrawnUi.Views.DrawnView.PaintTintBackground(SkiaSharp.SKCanvas)
  - DrawnUi.Views.DrawnView.RedrawCanvas(Microsoft.Maui.Controls.BindableObject,System.Object,System.Object)
  - DrawnUi.Views.DrawnView.OnBindingContextChanged
  - DrawnUi.Views.DrawnView.Views
  - DrawnUi.Views.DrawnView.ClearChildren
  - DrawnUi.Views.DrawnView.AddSubView(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Views.DrawnView.ReportHotreloadChildAdded(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Views.DrawnView.RemoveSubView(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Views.DrawnView.ReportHotreloadChildRemoved(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Views.DrawnView.OnChildRemoved(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Views.DrawnView.ChildrenProperty
  - DrawnUi.Views.DrawnView.Children
  - DrawnUi.Views.DrawnView.AddOrRemoveView(DrawnUi.Draw.SkiaControl,System.Boolean)
  - DrawnUi.Views.DrawnView.UpdateModeProperty
  - DrawnUi.Views.DrawnView.UpdateMode
  - DrawnUi.Views.DrawnView.ClipEffectsProperty
  - DrawnUi.Views.DrawnView.ClipEffects
  - DrawnUi.Views.DrawnView.Value1Property
  - DrawnUi.Views.DrawnView.Value1
  - DrawnUi.Views.DrawnView.Value2Property
  - DrawnUi.Views.DrawnView.Value2
  - DrawnUi.Views.DrawnView.Value3Property
  - DrawnUi.Views.DrawnView.Value3
  - DrawnUi.Views.DrawnView.Value4Property
  - DrawnUi.Views.DrawnView.Value4
  - DrawnUi.Views.DrawnView.FocusLocked
  - DrawnUi.Views.DrawnView.FocusedItemChanged
  - DrawnUi.Views.DrawnView.ReportFocus(DrawnUi.Draw.ISkiaGestureListener,DrawnUi.Draw.ISkiaGestureListener)
  - DrawnUi.Views.DrawnView.ResetFocusWithDelay(System.Int32)
  - DrawnUi.Views.DrawnView.FocusedChild
  - DrawnUi.Views.DrawnView.InvalidateCanvas
  - DrawnUi.Views.DrawnView.OnParentSet
  - DrawnUi.Views.DrawnView.GetIsVisibleWithParent(Microsoft.Maui.Controls.VisualElement)
  - DrawnUi.Views.DrawnView.CheckElementVisibility(Microsoft.Maui.Controls.VisualElement)
  - DrawnUi.Views.DrawnView.ClipSmart(SkiaSharp.SKCanvas,SkiaSharp.SKPath,SkiaSharp.SKClipOperation)
  - DrawnUi.Views.DrawnView.OnHotReload
  - DrawnUi.Views.DrawnView.InitFramework(System.Boolean)
  - Microsoft.Maui.Controls.TemplatedView.ControlTemplateProperty
  - Microsoft.Maui.Controls.TemplatedView.LayoutChildren(System.Double,System.Double,System.Double,System.Double)
  - Microsoft.Maui.Controls.TemplatedView.OnMeasure(System.Double,System.Double)
  - Microsoft.Maui.Controls.TemplatedView.OnApplyTemplate
  - Microsoft.Maui.Controls.TemplatedView.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
  - Microsoft.Maui.Controls.TemplatedView.GetTemplateChild(System.String)
  - Microsoft.Maui.Controls.TemplatedView.ResolveControlTemplate
  - Microsoft.Maui.Controls.TemplatedView.ControlTemplate
  - Microsoft.Maui.Controls.Compatibility.Layout.IsClippedToBoundsProperty
  - Microsoft.Maui.Controls.Compatibility.Layout.CascadeInputTransparentProperty
  - Microsoft.Maui.Controls.Compatibility.Layout.PaddingProperty
  - Microsoft.Maui.Controls.Compatibility.Layout.ForceLayout
  - Microsoft.Maui.Controls.Compatibility.Layout.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
  - Microsoft.Maui.Controls.Compatibility.Layout.LayoutChildIntoBoundingRegion(Microsoft.Maui.Controls.VisualElement,Microsoft.Maui.Graphics.Rect)
  - Microsoft.Maui.Controls.Compatibility.Layout.LowerChild(Microsoft.Maui.Controls.View)
  - Microsoft.Maui.Controls.Compatibility.Layout.RaiseChild(Microsoft.Maui.Controls.View)
  - Microsoft.Maui.Controls.Compatibility.Layout.InvalidateLayout
  - Microsoft.Maui.Controls.Compatibility.Layout.OnChildMeasureInvalidated(System.Object,System.EventArgs)
  - Microsoft.Maui.Controls.Compatibility.Layout.OnChildMeasureInvalidated
  - Microsoft.Maui.Controls.Compatibility.Layout.OnSizeAllocated(System.Double,System.Double)
  - Microsoft.Maui.Controls.Compatibility.Layout.ShouldInvalidateOnChildAdded(Microsoft.Maui.Controls.View)
  - Microsoft.Maui.Controls.Compatibility.Layout.ShouldInvalidateOnChildRemoved(Microsoft.Maui.Controls.View)
  - Microsoft.Maui.Controls.Compatibility.Layout.UpdateChildrenLayout
  - Microsoft.Maui.Controls.Compatibility.Layout.CrossPlatformMeasure(System.Double,System.Double)
  - Microsoft.Maui.Controls.Compatibility.Layout.CrossPlatformArrange(Microsoft.Maui.Graphics.Rect)
  - Microsoft.Maui.Controls.Compatibility.Layout.IsClippedToBounds
  - Microsoft.Maui.Controls.Compatibility.Layout.Padding
  - Microsoft.Maui.Controls.Compatibility.Layout.CascadeInputTransparent
  - Microsoft.Maui.Controls.Compatibility.Layout.LayoutChanged
  - Microsoft.Maui.Controls.View.VerticalOptionsProperty
  - Microsoft.Maui.Controls.View.HorizontalOptionsProperty
  - Microsoft.Maui.Controls.View.MarginProperty
  - Microsoft.Maui.Controls.View.propertyMapper
  - Microsoft.Maui.Controls.View.ChangeVisualState
  - Microsoft.Maui.Controls.View.GetChildElements(Microsoft.Maui.Graphics.Point)
  - Microsoft.Maui.Controls.View.GetRendererOverrides``1
  - Microsoft.Maui.Controls.View.GestureController
  - Microsoft.Maui.Controls.View.GestureRecognizers
  - Microsoft.Maui.Controls.View.HorizontalOptions
  - Microsoft.Maui.Controls.View.Margin
  - Microsoft.Maui.Controls.View.VerticalOptions
  - Microsoft.Maui.Controls.VisualElement.NavigationProperty
  - Microsoft.Maui.Controls.VisualElement.StyleProperty
  - Microsoft.Maui.Controls.VisualElement.InputTransparentProperty
  - Microsoft.Maui.Controls.VisualElement.IsEnabledProperty
  - Microsoft.Maui.Controls.VisualElement.XProperty
  - Microsoft.Maui.Controls.VisualElement.YProperty
  - Microsoft.Maui.Controls.VisualElement.AnchorXProperty
  - Microsoft.Maui.Controls.VisualElement.AnchorYProperty
  - Microsoft.Maui.Controls.VisualElement.TranslationXProperty
  - Microsoft.Maui.Controls.VisualElement.TranslationYProperty
  - Microsoft.Maui.Controls.VisualElement.WidthProperty
  - Microsoft.Maui.Controls.VisualElement.HeightProperty
  - Microsoft.Maui.Controls.VisualElement.RotationProperty
  - Microsoft.Maui.Controls.VisualElement.RotationXProperty
  - Microsoft.Maui.Controls.VisualElement.RotationYProperty
  - Microsoft.Maui.Controls.VisualElement.ScaleProperty
  - Microsoft.Maui.Controls.VisualElement.ScaleXProperty
  - Microsoft.Maui.Controls.VisualElement.ScaleYProperty
  - Microsoft.Maui.Controls.VisualElement.ClipProperty
  - Microsoft.Maui.Controls.VisualElement.VisualProperty
  - Microsoft.Maui.Controls.VisualElement.IsVisibleProperty
  - Microsoft.Maui.Controls.VisualElement.OpacityProperty
  - Microsoft.Maui.Controls.VisualElement.BackgroundColorProperty
  - Microsoft.Maui.Controls.VisualElement.BackgroundProperty
  - Microsoft.Maui.Controls.VisualElement.BehaviorsProperty
  - Microsoft.Maui.Controls.VisualElement.TriggersProperty
  - Microsoft.Maui.Controls.VisualElement.WidthRequestProperty
  - Microsoft.Maui.Controls.VisualElement.HeightRequestProperty
  - Microsoft.Maui.Controls.VisualElement.MinimumWidthRequestProperty
  - Microsoft.Maui.Controls.VisualElement.MinimumHeightRequestProperty
  - Microsoft.Maui.Controls.VisualElement.MaximumWidthRequestProperty
  - Microsoft.Maui.Controls.VisualElement.MaximumHeightRequestProperty
  - Microsoft.Maui.Controls.VisualElement.IsFocusedProperty
  - Microsoft.Maui.Controls.VisualElement.FlowDirectionProperty
  - Microsoft.Maui.Controls.VisualElement.WindowProperty
  - Microsoft.Maui.Controls.VisualElement.ShadowProperty
  - Microsoft.Maui.Controls.VisualElement.ZIndexProperty
  - Microsoft.Maui.Controls.VisualElement.BatchBegin
  - Microsoft.Maui.Controls.VisualElement.BatchCommit
  - Microsoft.Maui.Controls.VisualElement.Focus
  - Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
  - Microsoft.Maui.Controls.VisualElement.Unfocus
  - Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
  - Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
  - Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
  - Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
  - Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
  - Microsoft.Maui.Controls.VisualElement.InvalidateMeasureOverride
  - Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  - Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  - Microsoft.Maui.Controls.VisualElement.Visual
  - Microsoft.Maui.Controls.VisualElement.FlowDirection
  - Microsoft.Maui.Controls.VisualElement.Window
  - Microsoft.Maui.Controls.VisualElement.AnchorX
  - Microsoft.Maui.Controls.VisualElement.AnchorY
  - Microsoft.Maui.Controls.VisualElement.BackgroundColor
  - Microsoft.Maui.Controls.VisualElement.Background
  - Microsoft.Maui.Controls.VisualElement.Behaviors
  - Microsoft.Maui.Controls.VisualElement.Bounds
  - Microsoft.Maui.Controls.VisualElement.Height
  - Microsoft.Maui.Controls.VisualElement.HeightRequest
  - Microsoft.Maui.Controls.VisualElement.InputTransparent
  - Microsoft.Maui.Controls.VisualElement.IsEnabled
  - Microsoft.Maui.Controls.VisualElement.IsEnabledCore
  - Microsoft.Maui.Controls.VisualElement.IsFocused
  - Microsoft.Maui.Controls.VisualElement.IsVisible
  - Microsoft.Maui.Controls.VisualElement.MinimumHeightRequest
  - Microsoft.Maui.Controls.VisualElement.MinimumWidthRequest
  - Microsoft.Maui.Controls.VisualElement.MaximumHeightRequest
  - Microsoft.Maui.Controls.VisualElement.MaximumWidthRequest
  - Microsoft.Maui.Controls.VisualElement.Opacity
  - Microsoft.Maui.Controls.VisualElement.Rotation
  - Microsoft.Maui.Controls.VisualElement.RotationX
  - Microsoft.Maui.Controls.VisualElement.RotationY
  - Microsoft.Maui.Controls.VisualElement.Scale
  - Microsoft.Maui.Controls.VisualElement.ScaleX
  - Microsoft.Maui.Controls.VisualElement.ScaleY
  - Microsoft.Maui.Controls.VisualElement.TranslationX
  - Microsoft.Maui.Controls.VisualElement.TranslationY
  - Microsoft.Maui.Controls.VisualElement.Triggers
  - Microsoft.Maui.Controls.VisualElement.Width
  - Microsoft.Maui.Controls.VisualElement.WidthRequest
  - Microsoft.Maui.Controls.VisualElement.X
  - Microsoft.Maui.Controls.VisualElement.Y
  - Microsoft.Maui.Controls.VisualElement.Clip
  - Microsoft.Maui.Controls.VisualElement.Resources
  - Microsoft.Maui.Controls.VisualElement.Frame
  - Microsoft.Maui.Controls.VisualElement.Handler
  - Microsoft.Maui.Controls.VisualElement.Shadow
  - Microsoft.Maui.Controls.VisualElement.ZIndex
  - Microsoft.Maui.Controls.VisualElement.DesiredSize
  - Microsoft.Maui.Controls.VisualElement.IsLoaded
  - Microsoft.Maui.Controls.VisualElement.ChildrenReordered
  - Microsoft.Maui.Controls.VisualElement.Focused
  - Microsoft.Maui.Controls.VisualElement.MeasureInvalidated
  - Microsoft.Maui.Controls.VisualElement.SizeChanged
  - Microsoft.Maui.Controls.VisualElement.Unfocused
  - Microsoft.Maui.Controls.VisualElement.Loaded
  - Microsoft.Maui.Controls.VisualElement.Unloaded
  - Microsoft.Maui.Controls.NavigableElement.Navigation
  - Microsoft.Maui.Controls.StyleableElement.Style
  - Microsoft.Maui.Controls.StyleableElement.StyleClass
  - Microsoft.Maui.Controls.StyleableElement.class
  - Microsoft.Maui.Controls.Element.AutomationIdProperty
  - Microsoft.Maui.Controls.Element.ClassIdProperty
  - Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.ClearLogicalChildren
  - Microsoft.Maui.Controls.Element.FindByName(System.String)
  - Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
  - Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  - Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.AutomationId
  - Microsoft.Maui.Controls.Element.ClassId
  - Microsoft.Maui.Controls.Element.Effects
  - Microsoft.Maui.Controls.Element.Id
  - Microsoft.Maui.Controls.Element.StyleId
  - Microsoft.Maui.Controls.Element.Parent
  - Microsoft.Maui.Controls.Element.ChildAdded
  - Microsoft.Maui.Controls.Element.ChildRemoved
  - Microsoft.Maui.Controls.Element.DescendantAdded
  - Microsoft.Maui.Controls.Element.DescendantRemoved
  - Microsoft.Maui.Controls.Element.ParentChanging
  - Microsoft.Maui.Controls.Element.ParentChanged
  - Microsoft.Maui.Controls.Element.HandlerChanging
  - Microsoft.Maui.Controls.Element.HandlerChanged
  - Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  - Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  - Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  - Microsoft.Maui.Controls.BindableObject.ApplyBindings
  - Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  - Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  - Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  - Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  - Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  - Microsoft.Maui.Controls.BindableObject.Dispatcher
  - Microsoft.Maui.Controls.BindableObject.BindingContext
  - Microsoft.Maui.Controls.BindableObject.PropertyChanged
  - Microsoft.Maui.Controls.BindableObject.PropertyChanging
  - Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - DrawnUi.Draw.IDrawnBase.DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.ISkiaControl)
  - DrawnUi.Views.Canvas.DrawnUi.Draw.FluentExtensions.AssignNative``1(DrawnUi.Views.Canvas@)
  - Microsoft.Maui.Controls.Element.DrawnUi.Draw.StaticResourcesExtensions.FindParent``1
  - Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.FindMauiContext(System.Boolean)
  - Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.GetParentsPath
  - Microsoft.Maui.Controls.VisualElement.DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents
  - Microsoft.Maui.IView.DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  attributes:
  - type: Microsoft.Maui.Controls.ContentPropertyAttribute
    ctor: Microsoft.Maui.Controls.ContentPropertyAttribute.#ctor(System.String)
    arguments:
    - type: System.String
      value: Content
- uid: DrawnUi.Views.Canvas.SetChildren(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})
  commentId: M:DrawnUi.Views.Canvas.SetChildren(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})
  id: SetChildren(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: SetChildren(IEnumerable<SkiaControl>)
  nameWithType: Canvas.SetChildren(IEnumerable<SkiaControl>)
  fullName: DrawnUi.Views.Canvas.SetChildren(System.Collections.Generic.IEnumerable<DrawnUi.Draw.SkiaControl>)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SetChildren
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 22
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  example: []
  syntax:
    content: public override void SetChildren(IEnumerable<SkiaControl> views)
    parameters:
    - id: views
      type: System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}
    content.vb: Public Overrides Sub SetChildren(views As IEnumerable(Of SkiaControl))
  overridden: DrawnUi.Views.DrawnView.SetChildren(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})
  overload: DrawnUi.Views.Canvas.SetChildren*
  nameWithType.vb: Canvas.SetChildren(IEnumerable(Of SkiaControl))
  fullName.vb: DrawnUi.Views.Canvas.SetChildren(System.Collections.Generic.IEnumerable(Of DrawnUi.Draw.SkiaControl))
  name.vb: SetChildren(IEnumerable(Of SkiaControl))
- uid: DrawnUi.Views.Canvas.DumpDebug
  commentId: M:DrawnUi.Views.Canvas.DumpDebug
  id: DumpDebug
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: DumpDebug()
  nameWithType: Canvas.DumpDebug()
  fullName: DrawnUi.Views.Canvas.DumpDebug()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DumpDebug
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 28
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public void DumpDebug()
    content.vb: Public Sub DumpDebug()
  overload: DrawnUi.Views.Canvas.DumpDebug*
- uid: DrawnUi.Views.Canvas.OnChildAdded(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Views.Canvas.OnChildAdded(DrawnUi.Draw.SkiaControl)
  id: OnChildAdded(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: OnChildAdded(SkiaControl)
  nameWithType: Canvas.OnChildAdded(SkiaControl)
  fullName: DrawnUi.Views.Canvas.OnChildAdded(DrawnUi.Draw.SkiaControl)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnChildAdded
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 34
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  example: []
  syntax:
    content: protected override void OnChildAdded(SkiaControl child)
    parameters:
    - id: child
      type: DrawnUi.Draw.SkiaControl
    content.vb: Protected Overrides Sub OnChildAdded(child As SkiaControl)
  overridden: DrawnUi.Views.DrawnView.OnChildAdded(DrawnUi.Draw.SkiaControl)
  overload: DrawnUi.Views.Canvas.OnChildAdded*
- uid: DrawnUi.Views.Canvas.SetContent(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Views.Canvas.SetContent(DrawnUi.Draw.SkiaControl)
  id: SetContent(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: SetContent(SkiaControl)
  nameWithType: Canvas.SetContent(SkiaControl)
  fullName: DrawnUi.Views.Canvas.SetContent(DrawnUi.Draw.SkiaControl)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SetContent
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 48
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Use Content property for direct access
  example: []
  syntax:
    content: protected virtual void SetContent(SkiaControl view)
    parameters:
    - id: view
      type: DrawnUi.Draw.SkiaControl
    content.vb: Protected Overridable Sub SetContent(view As SkiaControl)
  overload: DrawnUi.Views.Canvas.SetContent*
- uid: DrawnUi.Views.Canvas.OnSizeChanged
  commentId: M:DrawnUi.Views.Canvas.OnSizeChanged
  id: OnSizeChanged
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: OnSizeChanged()
  nameWithType: Canvas.OnSizeChanged()
  fullName: DrawnUi.Views.Canvas.OnSizeChanged()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnSizeChanged
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 79
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  example: []
  syntax:
    content: protected override void OnSizeChanged()
    content.vb: Protected Overrides Sub OnSizeChanged()
  overridden: DrawnUi.Views.DrawnView.OnSizeChanged
  overload: DrawnUi.Views.Canvas.OnSizeChanged*
- uid: DrawnUi.Views.Canvas.InvalidateMeasure
  commentId: M:DrawnUi.Views.Canvas.InvalidateMeasure
  id: InvalidateMeasure
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: InvalidateMeasure()
  nameWithType: Canvas.InvalidateMeasure()
  fullName: DrawnUi.Views.Canvas.InvalidateMeasure()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: InvalidateMeasure
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 86
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Marks the current measure of an element as invalidated.
  example: []
  syntax:
    content: protected override void InvalidateMeasure()
    content.vb: Protected Overrides Sub InvalidateMeasure()
  overridden: Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
  overload: DrawnUi.Views.Canvas.InvalidateMeasure*
- uid: DrawnUi.Views.Canvas.AdaptSizeToContentIfNeeded(System.Double,System.Double,System.Boolean)
  commentId: M:DrawnUi.Views.Canvas.AdaptSizeToContentIfNeeded(System.Double,System.Double,System.Boolean)
  id: AdaptSizeToContentIfNeeded(System.Double,System.Double,System.Boolean)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: AdaptSizeToContentIfNeeded(double, double, bool)
  nameWithType: Canvas.AdaptSizeToContentIfNeeded(double, double, bool)
  fullName: DrawnUi.Views.Canvas.AdaptSizeToContentIfNeeded(double, double, bool)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AdaptSizeToContentIfNeeded
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 95
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: protected Size AdaptSizeToContentIfNeeded(double widthConstraint, double heightConstraint, bool force = false)
    parameters:
    - id: widthConstraint
      type: System.Double
    - id: heightConstraint
      type: System.Double
    - id: force
      type: System.Boolean
    return:
      type: Microsoft.Maui.Graphics.Size
    content.vb: Protected Function AdaptSizeToContentIfNeeded(widthConstraint As Double, heightConstraint As Double, force As Boolean = False) As Size
  overload: DrawnUi.Views.Canvas.AdaptSizeToContentIfNeeded*
  nameWithType.vb: Canvas.AdaptSizeToContentIfNeeded(Double, Double, Boolean)
  fullName.vb: DrawnUi.Views.Canvas.AdaptSizeToContentIfNeeded(Double, Double, Boolean)
  name.vb: AdaptSizeToContentIfNeeded(Double, Double, Boolean)
- uid: DrawnUi.Views.Canvas.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  commentId: M:DrawnUi.Views.Canvas.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  id: ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: ArrangeOverride(Rect)
  nameWithType: Canvas.ArrangeOverride(Rect)
  fullName: DrawnUi.Views.Canvas.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ArrangeOverride
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 125
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: >-
    We need this mainly to autosize inside grid cells

    This is also called when parent visibilty changes
  example: []
  syntax:
    content: protected override Size ArrangeOverride(Rect bounds)
    parameters:
    - id: bounds
      type: Microsoft.Maui.Graphics.Rect
      description: ''
    return:
      type: Microsoft.Maui.Graphics.Size
      description: ''
    content.vb: Protected Overrides Function ArrangeOverride(bounds As Rect) As Size
  overridden: Microsoft.Maui.Controls.TemplatedView.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  overload: DrawnUi.Views.Canvas.ArrangeOverride*
- uid: DrawnUi.Views.Canvas.MeasureOverride(System.Double,System.Double)
  commentId: M:DrawnUi.Views.Canvas.MeasureOverride(System.Double,System.Double)
  id: MeasureOverride(System.Double,System.Double)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: MeasureOverride(double, double)
  nameWithType: Canvas.MeasureOverride(double, double)
  fullName: DrawnUi.Views.Canvas.MeasureOverride(double, double)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MeasureOverride
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 140
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Allows subclasses to implement custom Measure logic during a controls measure pass.
  example: []
  syntax:
    content: protected override Size MeasureOverride(double widthConstraint, double heightConstraint)
    parameters:
    - id: widthConstraint
      type: System.Double
      description: The width constraint to request.
    - id: heightConstraint
      type: System.Double
      description: The height constraint to request.
    return:
      type: Microsoft.Maui.Graphics.Size
      description: The requested size that an element wants in order to be displayed on the device.
    content.vb: Protected Overrides Function MeasureOverride(widthConstraint As Double, heightConstraint As Double) As Size
  overridden: Microsoft.Maui.Controls.TemplatedView.MeasureOverride(System.Double,System.Double)
  overload: DrawnUi.Views.Canvas.MeasureOverride*
  nameWithType.vb: Canvas.MeasureOverride(Double, Double)
  fullName.vb: DrawnUi.Views.Canvas.MeasureOverride(Double, Double)
  name.vb: MeasureOverride(Double, Double)
- uid: DrawnUi.Views.Canvas.Invalidate
  commentId: M:DrawnUi.Views.Canvas.Invalidate
  id: Invalidate
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: Invalidate()
  nameWithType: Canvas.Invalidate()
  fullName: DrawnUi.Views.Canvas.Invalidate()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Invalidate
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 194
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Makes the control dirty, in need to be remeasured and rendered but this doesn't call Update, it's up yo you
  example: []
  syntax:
    content: public override void Invalidate()
    content.vb: Public Overrides Sub Invalidate()
  overridden: DrawnUi.Views.DrawnView.Invalidate
  overload: DrawnUi.Views.Canvas.Invalidate*
- uid: DrawnUi.Views.Canvas.AdaptWidthContraintToRequest(System.Double)
  commentId: M:DrawnUi.Views.Canvas.AdaptWidthContraintToRequest(System.Double)
  id: AdaptWidthContraintToRequest(System.Double)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: AdaptWidthContraintToRequest(double)
  nameWithType: Canvas.AdaptWidthContraintToRequest(double)
  fullName: DrawnUi.Views.Canvas.AdaptWidthContraintToRequest(double)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AdaptWidthContraintToRequest
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 213
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public float AdaptWidthContraintToRequest(double widthConstraint)
    parameters:
    - id: widthConstraint
      type: System.Double
    return:
      type: System.Single
    content.vb: Public Function AdaptWidthContraintToRequest(widthConstraint As Double) As Single
  overload: DrawnUi.Views.Canvas.AdaptWidthContraintToRequest*
  nameWithType.vb: Canvas.AdaptWidthContraintToRequest(Double)
  fullName.vb: DrawnUi.Views.Canvas.AdaptWidthContraintToRequest(Double)
  name.vb: AdaptWidthContraintToRequest(Double)
- uid: DrawnUi.Views.Canvas.AdaptHeightContraintToRequest(System.Double)
  commentId: M:DrawnUi.Views.Canvas.AdaptHeightContraintToRequest(System.Double)
  id: AdaptHeightContraintToRequest(System.Double)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: AdaptHeightContraintToRequest(double)
  nameWithType: Canvas.AdaptHeightContraintToRequest(double)
  fullName: DrawnUi.Views.Canvas.AdaptHeightContraintToRequest(double)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AdaptHeightContraintToRequest
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 221
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public float AdaptHeightContraintToRequest(double heightConstraint)
    parameters:
    - id: heightConstraint
      type: System.Double
    return:
      type: System.Single
    content.vb: Public Function AdaptHeightContraintToRequest(heightConstraint As Double) As Single
  overload: DrawnUi.Views.Canvas.AdaptHeightContraintToRequest*
  nameWithType.vb: Canvas.AdaptHeightContraintToRequest(Double)
  fullName.vb: DrawnUi.Views.Canvas.AdaptHeightContraintToRequest(Double)
  name.vb: AdaptHeightContraintToRequest(Double)
- uid: DrawnUi.Views.Canvas.Measure(System.Single,System.Single)
  commentId: M:DrawnUi.Views.Canvas.Measure(System.Single,System.Single)
  id: Measure(System.Single,System.Single)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: Measure(float, float)
  nameWithType: Canvas.Measure(float, float)
  fullName: DrawnUi.Views.Canvas.Measure(float, float)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Measure
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 229
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  example: []
  syntax:
    content: public override ScaledSize Measure(float widthConstraintPts, float heightConstraintPts)
    parameters:
    - id: widthConstraintPts
      type: System.Single
    - id: heightConstraintPts
      type: System.Single
    return:
      type: DrawnUi.Draw.ScaledSize
    content.vb: Public Overrides Function Measure(widthConstraintPts As Single, heightConstraintPts As Single) As ScaledSize
  overridden: DrawnUi.Views.DrawnView.Measure(System.Single,System.Single)
  overload: DrawnUi.Views.Canvas.Measure*
  nameWithType.vb: Canvas.Measure(Single, Single)
  fullName.vb: DrawnUi.Views.Canvas.Measure(Single, Single)
  name.vb: Measure(Single, Single)
- uid: DrawnUi.Views.Canvas.GetMeasuringRectForChildren(System.Single,System.Single,System.Single)
  commentId: M:DrawnUi.Views.Canvas.GetMeasuringRectForChildren(System.Single,System.Single,System.Single)
  id: GetMeasuringRectForChildren(System.Single,System.Single,System.Single)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: GetMeasuringRectForChildren(float, float, float)
  nameWithType: Canvas.GetMeasuringRectForChildren(float, float, float)
  fullName: DrawnUi.Views.Canvas.GetMeasuringRectForChildren(float, float, float)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetMeasuringRectForChildren
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 299
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: All in in UNITS, OUT in PIXELS
  example: []
  syntax:
    content: public SKRect GetMeasuringRectForChildren(float widthConstraint, float heightConstraint, float scale)
    parameters:
    - id: widthConstraint
      type: System.Single
      description: ''
    - id: heightConstraint
      type: System.Single
      description: ''
    - id: scale
      type: System.Single
      description: ''
    return:
      type: SkiaSharp.SKRect
      description: ''
    content.vb: Public Function GetMeasuringRectForChildren(widthConstraint As Single, heightConstraint As Single, scale As Single) As SKRect
  overload: DrawnUi.Views.Canvas.GetMeasuringRectForChildren*
  nameWithType.vb: Canvas.GetMeasuringRectForChildren(Single, Single, Single)
  fullName.vb: DrawnUi.Views.Canvas.GetMeasuringRectForChildren(Single, Single, Single)
  name.vb: GetMeasuringRectForChildren(Single, Single, Single)
- uid: DrawnUi.Views.Canvas.AdaptWidthContraintToContentRequest(System.Single,DrawnUi.Draw.ScaledSize,System.Double)
  commentId: M:DrawnUi.Views.Canvas.AdaptWidthContraintToContentRequest(System.Single,DrawnUi.Draw.ScaledSize,System.Double)
  id: AdaptWidthContraintToContentRequest(System.Single,DrawnUi.Draw.ScaledSize,System.Double)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: AdaptWidthContraintToContentRequest(float, ScaledSize, double)
  nameWithType: Canvas.AdaptWidthContraintToContentRequest(float, ScaledSize, double)
  fullName: DrawnUi.Views.Canvas.AdaptWidthContraintToContentRequest(float, DrawnUi.Draw.ScaledSize, double)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AdaptWidthContraintToContentRequest
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 314
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public float AdaptWidthContraintToContentRequest(float widthConstraintUnits, ScaledSize measuredContent, double sideConstraintsUnits)
    parameters:
    - id: widthConstraintUnits
      type: System.Single
    - id: measuredContent
      type: DrawnUi.Draw.ScaledSize
    - id: sideConstraintsUnits
      type: System.Double
    return:
      type: System.Single
    content.vb: Public Function AdaptWidthContraintToContentRequest(widthConstraintUnits As Single, measuredContent As ScaledSize, sideConstraintsUnits As Double) As Single
  overload: DrawnUi.Views.Canvas.AdaptWidthContraintToContentRequest*
  nameWithType.vb: Canvas.AdaptWidthContraintToContentRequest(Single, ScaledSize, Double)
  fullName.vb: DrawnUi.Views.Canvas.AdaptWidthContraintToContentRequest(Single, DrawnUi.Draw.ScaledSize, Double)
  name.vb: AdaptWidthContraintToContentRequest(Single, ScaledSize, Double)
- uid: DrawnUi.Views.Canvas.AdaptHeightContraintToContentRequest(System.Single,DrawnUi.Draw.ScaledSize,System.Double)
  commentId: M:DrawnUi.Views.Canvas.AdaptHeightContraintToContentRequest(System.Single,DrawnUi.Draw.ScaledSize,System.Double)
  id: AdaptHeightContraintToContentRequest(System.Single,DrawnUi.Draw.ScaledSize,System.Double)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: AdaptHeightContraintToContentRequest(float, ScaledSize, double)
  nameWithType: Canvas.AdaptHeightContraintToContentRequest(float, ScaledSize, double)
  fullName: DrawnUi.Views.Canvas.AdaptHeightContraintToContentRequest(float, DrawnUi.Draw.ScaledSize, double)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AdaptHeightContraintToContentRequest
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 327
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public float AdaptHeightContraintToContentRequest(float heightConstraintUnits, ScaledSize measuredContent, double sideConstraintsUnits)
    parameters:
    - id: heightConstraintUnits
      type: System.Single
    - id: measuredContent
      type: DrawnUi.Draw.ScaledSize
    - id: sideConstraintsUnits
      type: System.Double
    return:
      type: System.Single
    content.vb: Public Function AdaptHeightContraintToContentRequest(heightConstraintUnits As Single, measuredContent As ScaledSize, sideConstraintsUnits As Double) As Single
  overload: DrawnUi.Views.Canvas.AdaptHeightContraintToContentRequest*
  nameWithType.vb: Canvas.AdaptHeightContraintToContentRequest(Single, ScaledSize, Double)
  fullName.vb: DrawnUi.Views.Canvas.AdaptHeightContraintToContentRequest(Single, DrawnUi.Draw.ScaledSize, Double)
  name.vb: AdaptHeightContraintToContentRequest(Single, ScaledSize, Double)
- uid: DrawnUi.Views.Canvas.AdaptSizeRequestToContent(System.Double,System.Double)
  commentId: M:DrawnUi.Views.Canvas.AdaptSizeRequestToContent(System.Double,System.Double)
  id: AdaptSizeRequestToContent(System.Double,System.Double)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: AdaptSizeRequestToContent(double, double)
  nameWithType: Canvas.AdaptSizeRequestToContent(double, double)
  fullName: DrawnUi.Views.Canvas.AdaptSizeRequestToContent(double, double)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AdaptSizeRequestToContent
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 346
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: In UNITS
  example: []
  syntax:
    content: protected Size AdaptSizeRequestToContent(double widthRequest, double heightRequest)
    parameters:
    - id: widthRequest
      type: System.Double
      description: ''
    - id: heightRequest
      type: System.Double
      description: ''
    return:
      type: Microsoft.Maui.Graphics.Size
      description: ''
    content.vb: Protected Function AdaptSizeRequestToContent(widthRequest As Double, heightRequest As Double) As Size
  overload: DrawnUi.Views.Canvas.AdaptSizeRequestToContent*
  nameWithType.vb: Canvas.AdaptSizeRequestToContent(Double, Double)
  fullName.vb: DrawnUi.Views.Canvas.AdaptSizeRequestToContent(Double, Double)
  name.vb: AdaptSizeRequestToContent(Double, Double)
- uid: DrawnUi.Views.Canvas.ContentSize
  commentId: P:DrawnUi.Views.Canvas.ContentSize
  id: ContentSize
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: ContentSize
  nameWithType: Canvas.ContentSize
  fullName: DrawnUi.Views.Canvas.ContentSize
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ContentSize
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 364
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public ScaledSize ContentSize { get; protected set; }
    parameters: []
    return:
      type: DrawnUi.Draw.ScaledSize
    content.vb: Public Property ContentSize As ScaledSize
  overload: DrawnUi.Views.Canvas.ContentSize*
- uid: DrawnUi.Views.Canvas.MeasureChild(DrawnUi.Draw.SkiaControl,System.Double,System.Double,System.Double)
  commentId: M:DrawnUi.Views.Canvas.MeasureChild(DrawnUi.Draw.SkiaControl,System.Double,System.Double,System.Double)
  id: MeasureChild(DrawnUi.Draw.SkiaControl,System.Double,System.Double,System.Double)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: MeasureChild(SkiaControl, double, double, double)
  nameWithType: Canvas.MeasureChild(SkiaControl, double, double, double)
  fullName: DrawnUi.Views.Canvas.MeasureChild(DrawnUi.Draw.SkiaControl, double, double, double)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MeasureChild
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 385
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: PIXELS
  example: []
  syntax:
    content: protected ScaledSize MeasureChild(SkiaControl child, double availableWidth, double availableHeight, double scale)
    parameters:
    - id: child
      type: DrawnUi.Draw.SkiaControl
      description: ''
    - id: availableWidth
      type: System.Double
      description: ''
    - id: availableHeight
      type: System.Double
      description: ''
    - id: scale
      type: System.Double
      description: ''
    return:
      type: DrawnUi.Draw.ScaledSize
      description: ''
    content.vb: Protected Function MeasureChild(child As SkiaControl, availableWidth As Double, availableHeight As Double, scale As Double) As ScaledSize
  overload: DrawnUi.Views.Canvas.MeasureChild*
  nameWithType.vb: Canvas.MeasureChild(SkiaControl, Double, Double, Double)
  fullName.vb: DrawnUi.Views.Canvas.MeasureChild(DrawnUi.Draw.SkiaControl, Double, Double, Double)
  name.vb: MeasureChild(SkiaControl, Double, Double, Double)
- uid: DrawnUi.Views.Canvas.OnHandlerChanged
  commentId: M:DrawnUi.Views.Canvas.OnHandlerChanged
  id: OnHandlerChanged
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: OnHandlerChanged()
  nameWithType: Canvas.OnHandlerChanged()
  fullName: DrawnUi.Views.Canvas.OnHandlerChanged()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnHandlerChanged
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 398
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: When overridden in a derived class, should raise the <xref href="Microsoft.Maui.Controls.Element.HandlerChanged" data-throw-if-not-resolved="false"></xref> event.
  remarks: It is the implementor's responsibility to raise the <xref href="Microsoft.Maui.Controls.Element.HandlerChanged" data-throw-if-not-resolved="false"></xref> event.
  example: []
  syntax:
    content: protected override void OnHandlerChanged()
    content.vb: Protected Overrides Sub OnHandlerChanged()
  overridden: DrawnUi.Views.DrawnView.OnHandlerChanged
  overload: DrawnUi.Views.Canvas.OnHandlerChanged*
- uid: DrawnUi.Views.Canvas.OnGesturesAttachChanged
  commentId: M:DrawnUi.Views.Canvas.OnGesturesAttachChanged
  id: OnGesturesAttachChanged
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: OnGesturesAttachChanged()
  nameWithType: Canvas.OnGesturesAttachChanged()
  fullName: DrawnUi.Views.Canvas.OnGesturesAttachChanged()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnGesturesAttachChanged
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 407
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: protected virtual void OnGesturesAttachChanged()
    content.vb: Protected Overridable Sub OnGesturesAttachChanged()
  overload: DrawnUi.Views.Canvas.OnGesturesAttachChanged*
- uid: DrawnUi.Views.Canvas.CreateDebugPointer
  commentId: M:DrawnUi.Views.Canvas.CreateDebugPointer
  id: CreateDebugPointer
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: CreateDebugPointer()
  nameWithType: Canvas.CreateDebugPointer()
  fullName: DrawnUi.Views.Canvas.CreateDebugPointer()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CreateDebugPointer
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 433
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: protected virtual SkiaSvg CreateDebugPointer()
    return:
      type: DrawnUi.Draw.SkiaSvg
    content.vb: Protected Overridable Function CreateDebugPointer() As SkiaSvg
  overload: DrawnUi.Views.Canvas.CreateDebugPointer*
- uid: DrawnUi.Views.Canvas.DebugPointer
  commentId: P:DrawnUi.Views.Canvas.DebugPointer
  id: DebugPointer
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: DebugPointer
  nameWithType: Canvas.DebugPointer
  fullName: DrawnUi.Views.Canvas.DebugPointer
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DebugPointer
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 446
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: protected SkiaSvg DebugPointer { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.SkiaSvg
    content.vb: Protected Property DebugPointer As SkiaSvg
  overload: DrawnUi.Views.Canvas.DebugPointer*
- uid: DrawnUi.Views.Canvas.OnDisposing
  commentId: M:DrawnUi.Views.Canvas.OnDisposing
  id: OnDisposing
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: OnDisposing()
  nameWithType: Canvas.OnDisposing()
  fullName: DrawnUi.Views.Canvas.OnDisposing()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnDisposing
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 448
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  example: []
  syntax:
    content: public override void OnDisposing()
    content.vb: Public Overrides Sub OnDisposing()
  overridden: DrawnUi.Views.DrawnView.OnDisposing
  overload: DrawnUi.Views.Canvas.OnDisposing*
- uid: DrawnUi.Views.Canvas.Context
  commentId: F:DrawnUi.Views.Canvas.Context
  id: Context
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: Context
  nameWithType: Canvas.Context
  fullName: DrawnUi.Views.Canvas.Context
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Context
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 456
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: protected DrawingContext Context
    return:
      type: DrawnUi.Draw.DrawingContext
    content.vb: Protected Context As DrawingContext
- uid: DrawnUi.Views.Canvas.ReceivedInput
  commentId: P:DrawnUi.Views.Canvas.ReceivedInput
  id: ReceivedInput
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: ReceivedInput
  nameWithType: Canvas.ReceivedInput
  fullName: DrawnUi.Views.Canvas.ReceivedInput
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ReceivedInput
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 466
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Got input during current pass
  example: []
  syntax:
    content: public ConcurrentBag<ISkiaGestureListener> ReceivedInput { get; }
    parameters: []
    return:
      type: System.Collections.Concurrent.ConcurrentBag{DrawnUi.Draw.ISkiaGestureListener}
    content.vb: Public ReadOnly Property ReceivedInput As ConcurrentBag(Of ISkiaGestureListener)
  overload: DrawnUi.Views.Canvas.ReceivedInput*
- uid: DrawnUi.Views.Canvas.HadInput
  commentId: P:DrawnUi.Views.Canvas.HadInput
  id: HadInput
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: HadInput
  nameWithType: Canvas.HadInput
  fullName: DrawnUi.Views.Canvas.HadInput
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: HadInput
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 471
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Have consumed input in previous or current pass. To notify them about UP (release) even if they don't get it via touch.
  example: []
  syntax:
    content: public Dictionary<Guid, ISkiaGestureListener> HadInput { get; }
    parameters: []
    return:
      type: System.Collections.Generic.Dictionary{System.Guid,DrawnUi.Draw.ISkiaGestureListener}
    content.vb: Public ReadOnly Property HadInput As Dictionary(Of Guid, ISkiaGestureListener)
  overload: DrawnUi.Views.Canvas.HadInput*
- uid: DrawnUi.Views.Canvas.IsSavedGesture(AppoMobi.Maui.Gestures.TouchActionResult)
  commentId: M:DrawnUi.Views.Canvas.IsSavedGesture(AppoMobi.Maui.Gestures.TouchActionResult)
  id: IsSavedGesture(AppoMobi.Maui.Gestures.TouchActionResult)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: IsSavedGesture(TouchActionResult)
  nameWithType: Canvas.IsSavedGesture(TouchActionResult)
  fullName: DrawnUi.Views.Canvas.IsSavedGesture(AppoMobi.Maui.Gestures.TouchActionResult)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsSavedGesture
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 478
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Define if consuming this gesture will register child inside `HadInput`
  example: []
  syntax:
    content: protected bool IsSavedGesture(TouchActionResult type)
    parameters:
    - id: type
      type: AppoMobi.Maui.Gestures.TouchActionResult
      description: ''
    return:
      type: System.Boolean
      description: ''
    content.vb: Protected Function IsSavedGesture(type As TouchActionResult) As Boolean
  overload: DrawnUi.Views.Canvas.IsSavedGesture*
- uid: DrawnUi.Views.Canvas.ProcessGestures(DrawnUi.Draw.SkiaGesturesParameters)
  commentId: M:DrawnUi.Views.Canvas.ProcessGestures(DrawnUi.Draw.SkiaGesturesParameters)
  id: ProcessGestures(DrawnUi.Draw.SkiaGesturesParameters)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: ProcessGestures(SkiaGesturesParameters)
  nameWithType: Canvas.ProcessGestures(SkiaGesturesParameters)
  fullName: DrawnUi.Views.Canvas.ProcessGestures(DrawnUi.Draw.SkiaGesturesParameters)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ProcessGestures
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 488
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: protected virtual void ProcessGestures(SkiaGesturesParameters args)
    parameters:
    - id: args
      type: DrawnUi.Draw.SkiaGesturesParameters
    content.vb: Protected Overridable Sub ProcessGestures(args As SkiaGesturesParameters)
  overload: DrawnUi.Views.Canvas.ProcessGestures*
- uid: DrawnUi.Views.Canvas.SignalInput(DrawnUi.Draw.ISkiaGestureListener,AppoMobi.Maui.Gestures.TouchActionResult)
  commentId: M:DrawnUi.Views.Canvas.SignalInput(DrawnUi.Draw.ISkiaGestureListener,AppoMobi.Maui.Gestures.TouchActionResult)
  id: SignalInput(DrawnUi.Draw.ISkiaGestureListener,AppoMobi.Maui.Gestures.TouchActionResult)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: SignalInput(ISkiaGestureListener, TouchActionResult)
  nameWithType: Canvas.SignalInput(ISkiaGestureListener, TouchActionResult)
  fullName: DrawnUi.Views.Canvas.SignalInput(DrawnUi.Draw.ISkiaGestureListener, AppoMobi.Maui.Gestures.TouchActionResult)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SignalInput
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 634
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Gets signal from a listener that in has processed gestures. Return false if should not rpocess gestures.
  example: []
  syntax:
    content: public bool SignalInput(ISkiaGestureListener listener, TouchActionResult gestureType)
    parameters:
    - id: listener
      type: DrawnUi.Draw.ISkiaGestureListener
    - id: gestureType
      type: AppoMobi.Maui.Gestures.TouchActionResult
    return:
      type: System.Boolean
    content.vb: Public Function SignalInput(listener As ISkiaGestureListener, gestureType As TouchActionResult) As Boolean
  overload: DrawnUi.Views.Canvas.SignalInput*
- uid: DrawnUi.Views.Canvas.Tapped
  commentId: E:DrawnUi.Views.Canvas.Tapped
  id: Tapped
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: Tapped
  nameWithType: Canvas.Tapped
  fullName: DrawnUi.Views.Canvas.Tapped
  type: Event
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Tapped
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 649
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public event EventHandler Tapped
    return:
      type: System.EventHandler
    content.vb: Public Event Tapped As EventHandler
- uid: DrawnUi.Views.Canvas.FirstPanThreshold
  commentId: F:DrawnUi.Views.Canvas.FirstPanThreshold
  id: FirstPanThreshold
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: FirstPanThreshold
  nameWithType: Canvas.FirstPanThreshold
  fullName: DrawnUi.Views.Canvas.FirstPanThreshold
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FirstPanThreshold
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 654
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: To filter micro-gestures on super sensitive screens, start passing panning only when threshold is once overpassed
  example: []
  syntax:
    content: public static float FirstPanThreshold
    return:
      type: System.Single
    content.vb: Public Shared FirstPanThreshold As Single
- uid: DrawnUi.Views.Canvas.OnGestureEvent(AppoMobi.Maui.Gestures.TouchActionType,AppoMobi.Maui.Gestures.TouchActionEventArgs,AppoMobi.Maui.Gestures.TouchActionResult)
  commentId: M:DrawnUi.Views.Canvas.OnGestureEvent(AppoMobi.Maui.Gestures.TouchActionType,AppoMobi.Maui.Gestures.TouchActionEventArgs,AppoMobi.Maui.Gestures.TouchActionResult)
  id: OnGestureEvent(AppoMobi.Maui.Gestures.TouchActionType,AppoMobi.Maui.Gestures.TouchActionEventArgs,AppoMobi.Maui.Gestures.TouchActionResult)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: OnGestureEvent(TouchActionType, TouchActionEventArgs, TouchActionResult)
  nameWithType: Canvas.OnGestureEvent(TouchActionType, TouchActionEventArgs, TouchActionResult)
  fullName: DrawnUi.Views.Canvas.OnGestureEvent(AppoMobi.Maui.Gestures.TouchActionType, AppoMobi.Maui.Gestures.TouchActionEventArgs, AppoMobi.Maui.Gestures.TouchActionResult)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnGestureEvent
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 667
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: IGestureListener implementation
  example: []
  syntax:
    content: public virtual void OnGestureEvent(TouchActionType type, TouchActionEventArgs args1, TouchActionResult touchAction)
    parameters:
    - id: type
      type: AppoMobi.Maui.Gestures.TouchActionType
      description: ''
    - id: args1
      type: AppoMobi.Maui.Gestures.TouchActionEventArgs
      description: ''
    - id: touchAction
      type: AppoMobi.Maui.Gestures.TouchActionResult
      description: ''
    content.vb: Public Overridable Sub OnGestureEvent(type As TouchActionType, args1 As TouchActionEventArgs, touchAction As TouchActionResult)
  overload: DrawnUi.Views.Canvas.OnGestureEvent*
  implements:
  - AppoMobi.Maui.Gestures.IGestureListener.OnGestureEvent(AppoMobi.Maui.Gestures.TouchActionType,AppoMobi.Maui.Gestures.TouchActionEventArgs,AppoMobi.Maui.Gestures.TouchActionResult)
- uid: DrawnUi.Views.Canvas.BreakLine
  commentId: M:DrawnUi.Views.Canvas.BreakLine
  id: BreakLine
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: BreakLine()
  nameWithType: Canvas.BreakLine()
  fullName: DrawnUi.Views.Canvas.BreakLine()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: BreakLine
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 803
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public void BreakLine()
    content.vb: Public Sub BreakLine()
  overload: DrawnUi.Views.Canvas.BreakLine*
- uid: DrawnUi.Views.Canvas.LineBreaks
  commentId: F:DrawnUi.Views.Canvas.LineBreaks
  id: LineBreaks
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: LineBreaks
  nameWithType: Canvas.LineBreaks
  fullName: DrawnUi.Views.Canvas.LineBreaks
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LineBreaks
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 808
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: protected List<int> LineBreaks
    return:
      type: System.Collections.Generic.List{System.Int32}
    content.vb: Protected LineBreaks As List(Of Integer)
- uid: DrawnUi.Views.Canvas.#ctor
  commentId: M:DrawnUi.Views.Canvas.#ctor
  id: '#ctor'
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: Canvas()
  nameWithType: Canvas.Canvas()
  fullName: DrawnUi.Views.Canvas.Canvas()
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 810
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public Canvas()
    content.vb: Public Sub New()
  overload: DrawnUi.Views.Canvas.#ctor*
  nameWithType.vb: Canvas.New()
  fullName.vb: DrawnUi.Views.Canvas.New()
  name.vb: New()
- uid: DrawnUi.Views.Canvas.Clear
  commentId: M:DrawnUi.Views.Canvas.Clear
  id: Clear
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: Clear()
  nameWithType: Canvas.Clear()
  fullName: DrawnUi.Views.Canvas.Clear()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Clear
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 814
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public void Clear()
    content.vb: Public Sub Clear()
  overload: DrawnUi.Views.Canvas.Clear*
- uid: DrawnUi.Views.Canvas.PlayRippleAnimation(Microsoft.Maui.Graphics.Color,System.Double,System.Double,System.Boolean)
  commentId: M:DrawnUi.Views.Canvas.PlayRippleAnimation(Microsoft.Maui.Graphics.Color,System.Double,System.Double,System.Boolean)
  id: PlayRippleAnimation(Microsoft.Maui.Graphics.Color,System.Double,System.Double,System.Boolean)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: PlayRippleAnimation(Color, double, double, bool)
  nameWithType: Canvas.PlayRippleAnimation(Color, double, double, bool)
  fullName: DrawnUi.Views.Canvas.PlayRippleAnimation(Microsoft.Maui.Graphics.Color, double, double, bool)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: PlayRippleAnimation
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 819
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public void PlayRippleAnimation(Color color, double x, double y, bool removePrevious = true)
    parameters:
    - id: color
      type: Microsoft.Maui.Graphics.Color
    - id: x
      type: System.Double
    - id: y
      type: System.Double
    - id: removePrevious
      type: System.Boolean
    content.vb: Public Sub PlayRippleAnimation(color As Color, x As Double, y As Double, removePrevious As Boolean = True)
  overload: DrawnUi.Views.Canvas.PlayRippleAnimation*
  nameWithType.vb: Canvas.PlayRippleAnimation(Color, Double, Double, Boolean)
  fullName.vb: DrawnUi.Views.Canvas.PlayRippleAnimation(Microsoft.Maui.Graphics.Color, Double, Double, Boolean)
  name.vb: PlayRippleAnimation(Color, Double, Double, Boolean)
- uid: DrawnUi.Views.Canvas.PlayShimmerAnimation(Microsoft.Maui.Graphics.Color,System.Single,System.Single,System.Int32,System.Boolean)
  commentId: M:DrawnUi.Views.Canvas.PlayShimmerAnimation(Microsoft.Maui.Graphics.Color,System.Single,System.Single,System.Int32,System.Boolean)
  id: PlayShimmerAnimation(Microsoft.Maui.Graphics.Color,System.Single,System.Single,System.Int32,System.Boolean)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: PlayShimmerAnimation(Color, float, float, int, bool)
  nameWithType: Canvas.PlayShimmerAnimation(Color, float, float, int, bool)
  fullName: DrawnUi.Views.Canvas.PlayShimmerAnimation(Microsoft.Maui.Graphics.Color, float, float, int, bool)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: PlayShimmerAnimation
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 825
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public void PlayShimmerAnimation(Color color, float shimmerWidth, float shimmerAngle, int speedMs = 1000, bool removePrevious = false)
    parameters:
    - id: color
      type: Microsoft.Maui.Graphics.Color
    - id: shimmerWidth
      type: System.Single
    - id: shimmerAngle
      type: System.Single
    - id: speedMs
      type: System.Int32
    - id: removePrevious
      type: System.Boolean
    content.vb: Public Sub PlayShimmerAnimation(color As Color, shimmerWidth As Single, shimmerAngle As Single, speedMs As Integer = 1000, removePrevious As Boolean = False)
  overload: DrawnUi.Views.Canvas.PlayShimmerAnimation*
  nameWithType.vb: Canvas.PlayShimmerAnimation(Color, Single, Single, Integer, Boolean)
  fullName.vb: DrawnUi.Views.Canvas.PlayShimmerAnimation(Microsoft.Maui.Graphics.Color, Single, Single, Integer, Boolean)
  name.vb: PlayShimmerAnimation(Color, Single, Single, Integer, Boolean)
- uid: DrawnUi.Views.Canvas.TimeLastGC
  commentId: P:DrawnUi.Views.Canvas.TimeLastGC
  id: TimeLastGC
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: TimeLastGC
  nameWithType: Canvas.TimeLastGC
  fullName: DrawnUi.Views.Canvas.TimeLastGC
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TimeLastGC
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 835
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public static long TimeLastGC { get; set; }
    parameters: []
    return:
      type: System.Int64
    content.vb: Public Shared Property TimeLastGC As Long
  overload: DrawnUi.Views.Canvas.TimeLastGC*
- uid: DrawnUi.Views.Canvas.DelayNanosBetweenGC
  commentId: P:DrawnUi.Views.Canvas.DelayNanosBetweenGC
  id: DelayNanosBetweenGC
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: DelayNanosBetweenGC
  nameWithType: Canvas.DelayNanosBetweenGC
  fullName: DrawnUi.Views.Canvas.DelayNanosBetweenGC
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DelayNanosBetweenGC
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 836
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public static long DelayNanosBetweenGC { get; set; }
    parameters: []
    return:
      type: System.Int64
    content.vb: Public Shared Property DelayNanosBetweenGC As Long
  overload: DrawnUi.Views.Canvas.DelayNanosBetweenGC*
- uid: DrawnUi.Views.Canvas.CollectGarbage(System.Int64)
  commentId: M:DrawnUi.Views.Canvas.CollectGarbage(System.Int64)
  id: CollectGarbage(System.Int64)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: CollectGarbage(long)
  nameWithType: Canvas.CollectGarbage(long)
  fullName: DrawnUi.Views.Canvas.CollectGarbage(long)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CollectGarbage
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 838
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public static void CollectGarbage(long timeNanos)
    parameters:
    - id: timeNanos
      type: System.Int64
    content.vb: Public Shared Sub CollectGarbage(timeNanos As Long)
  overload: DrawnUi.Views.Canvas.CollectGarbage*
  nameWithType.vb: Canvas.CollectGarbage(Long)
  fullName.vb: DrawnUi.Views.Canvas.CollectGarbage(Long)
  name.vb: CollectGarbage(Long)
- uid: DrawnUi.Views.Canvas.OnPropertyChanged(System.String)
  commentId: M:DrawnUi.Views.Canvas.OnPropertyChanged(System.String)
  id: OnPropertyChanged(System.String)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: OnPropertyChanged(string)
  nameWithType: Canvas.OnPropertyChanged(string)
  fullName: DrawnUi.Views.Canvas.OnPropertyChanged(string)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnPropertyChanged
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 855
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Method that is called when a bound property is changed.
  example: []
  syntax:
    content: protected override void OnPropertyChanged(string propertyName = null)
    parameters:
    - id: propertyName
      type: System.String
      description: The name of the bound property that changed.
    content.vb: Protected Overrides Sub OnPropertyChanged(propertyName As String = Nothing)
  overridden: DrawnUi.Views.DrawnView.OnPropertyChanged(System.String)
  overload: DrawnUi.Views.Canvas.OnPropertyChanged*
  nameWithType.vb: Canvas.OnPropertyChanged(String)
  fullName.vb: DrawnUi.Views.Canvas.OnPropertyChanged(String)
  name.vb: OnPropertyChanged(String)
- uid: DrawnUi.Views.Canvas.GesturesDebugColorProperty
  commentId: F:DrawnUi.Views.Canvas.GesturesDebugColorProperty
  id: GesturesDebugColorProperty
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: GesturesDebugColorProperty
  nameWithType: Canvas.GesturesDebugColorProperty
  fullName: DrawnUi.Views.Canvas.GesturesDebugColorProperty
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GesturesDebugColorProperty
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 868
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public static readonly BindableProperty GesturesDebugColorProperty
    return:
      type: Microsoft.Maui.Controls.BindableProperty
    content.vb: Public Shared ReadOnly GesturesDebugColorProperty As BindableProperty
- uid: DrawnUi.Views.Canvas.GesturesDebugColor
  commentId: P:DrawnUi.Views.Canvas.GesturesDebugColor
  id: GesturesDebugColor
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: GesturesDebugColor
  nameWithType: Canvas.GesturesDebugColor
  fullName: DrawnUi.Views.Canvas.GesturesDebugColor
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GesturesDebugColor
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 874
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public Color GesturesDebugColor { get; set; }
    parameters: []
    return:
      type: Microsoft.Maui.Graphics.Color
    content.vb: Public Property GesturesDebugColor As Color
  overload: DrawnUi.Views.Canvas.GesturesDebugColor*
- uid: DrawnUi.Views.Canvas.ReserveSpaceAroundProperty
  commentId: F:DrawnUi.Views.Canvas.ReserveSpaceAroundProperty
  id: ReserveSpaceAroundProperty
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: ReserveSpaceAroundProperty
  nameWithType: Canvas.ReserveSpaceAroundProperty
  fullName: DrawnUi.Views.Canvas.ReserveSpaceAroundProperty
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ReserveSpaceAroundProperty
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 880
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public static readonly BindableProperty ReserveSpaceAroundProperty
    return:
      type: Microsoft.Maui.Controls.BindableProperty
    content.vb: Public Shared ReadOnly ReserveSpaceAroundProperty As BindableProperty
- uid: DrawnUi.Views.Canvas.ReserveSpaceAround
  commentId: P:DrawnUi.Views.Canvas.ReserveSpaceAround
  id: ReserveSpaceAround
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: ReserveSpaceAround
  nameWithType: Canvas.ReserveSpaceAround
  fullName: DrawnUi.Views.Canvas.ReserveSpaceAround
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ReserveSpaceAround
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 886
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public Thickness ReserveSpaceAround { get; set; }
    parameters: []
    return:
      type: Microsoft.Maui.Thickness
    content.vb: Public Property ReserveSpaceAround As Thickness
  overload: DrawnUi.Views.Canvas.ReserveSpaceAround*
- uid: DrawnUi.Views.Canvas.GesturesProperty
  commentId: F:DrawnUi.Views.Canvas.GesturesProperty
  id: GesturesProperty
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: GesturesProperty
  nameWithType: Canvas.GesturesProperty
  fullName: DrawnUi.Views.Canvas.GesturesProperty
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GesturesProperty
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 900
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public static readonly BindableProperty GesturesProperty
    return:
      type: Microsoft.Maui.Controls.BindableProperty
    content.vb: Public Shared ReadOnly GesturesProperty As BindableProperty
- uid: DrawnUi.Views.Canvas.Gestures
  commentId: P:DrawnUi.Views.Canvas.Gestures
  id: Gestures
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: Gestures
  nameWithType: Canvas.Gestures
  fullName: DrawnUi.Views.Canvas.Gestures
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Gestures
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 906
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public GesturesMode Gestures { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.GesturesMode
    content.vb: Public Property Gestures As GesturesMode
  overload: DrawnUi.Views.Canvas.Gestures*
- uid: DrawnUi.Views.Canvas.ContentProperty
  commentId: F:DrawnUi.Views.Canvas.ContentProperty
  id: ContentProperty
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: ContentProperty
  nameWithType: Canvas.ContentProperty
  fullName: DrawnUi.Views.Canvas.ContentProperty
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ContentProperty
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 914
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public static readonly BindableProperty ContentProperty
    return:
      type: Microsoft.Maui.Controls.BindableProperty
    content.vb: Public Shared ReadOnly ContentProperty As BindableProperty
- uid: DrawnUi.Views.Canvas.Content
  commentId: P:DrawnUi.Views.Canvas.Content
  id: Content
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: Content
  nameWithType: Canvas.Content
  fullName: DrawnUi.Views.Canvas.Content
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Content
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 930
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public ISkiaControl Content { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.ISkiaControl
    content.vb: Public Property Content As ISkiaControl
  overload: DrawnUi.Views.Canvas.Content*
- uid: DrawnUi.Views.Canvas.OnParentChanged
  commentId: M:DrawnUi.Views.Canvas.OnParentChanged
  id: OnParentChanged
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: OnParentChanged()
  nameWithType: Canvas.OnParentChanged()
  fullName: DrawnUi.Views.Canvas.OnParentChanged()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnParentChanged
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 940
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: When overridden in a derived class, should raise the <xref href="Microsoft.Maui.Controls.Element.ParentChanged" data-throw-if-not-resolved="false"></xref> event.
  remarks: It is the implementor's responsibility to raise the <xref href="Microsoft.Maui.Controls.Element.ParentChanged" data-throw-if-not-resolved="false"></xref> event.
  example: []
  syntax:
    content: protected override void OnParentChanged()
    content.vb: Protected Overrides Sub OnParentChanged()
  overridden: Microsoft.Maui.Controls.Element.OnParentChanged
  overload: DrawnUi.Views.Canvas.OnParentChanged*
- uid: DrawnUi.Views.Canvas.EnableUpdates
  commentId: M:DrawnUi.Views.Canvas.EnableUpdates
  id: EnableUpdates
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: EnableUpdates()
  nameWithType: Canvas.EnableUpdates()
  fullName: DrawnUi.Views.Canvas.EnableUpdates()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: EnableUpdates
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 950
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Enable canvas rendering itsself
  example: []
  syntax:
    content: public virtual void EnableUpdates()
    content.vb: Public Overridable Sub EnableUpdates()
  overload: DrawnUi.Views.Canvas.EnableUpdates*
- uid: DrawnUi.Views.Canvas.DisableUpdates
  commentId: M:DrawnUi.Views.Canvas.DisableUpdates
  id: DisableUpdates
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: DisableUpdates()
  nameWithType: Canvas.DisableUpdates()
  fullName: DrawnUi.Views.Canvas.DisableUpdates()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DisableUpdates
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 960
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Disable invalidating and drawing on the canvas
  example: []
  syntax:
    content: public virtual void DisableUpdates()
    content.vb: Public Overridable Sub DisableUpdates()
  overload: DrawnUi.Views.Canvas.DisableUpdates*
- uid: DrawnUi.Views.Canvas.Draw(DrawnUi.Draw.DrawingContext)
  commentId: M:DrawnUi.Views.Canvas.Draw(DrawnUi.Draw.DrawingContext)
  id: Draw(DrawnUi.Draw.DrawingContext)
  parent: DrawnUi.Views.Canvas
  langs:
  - csharp
  - vb
  name: Draw(DrawingContext)
  nameWithType: Canvas.Draw(DrawingContext)
  fullName: DrawnUi.Views.Canvas.Draw(DrawnUi.Draw.DrawingContext)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/Canvas.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Draw
    path: ../src/Maui/DrawnUi/Views/Canvas.cs
    startLine: 965
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  example: []
  syntax:
    content: protected override void Draw(DrawingContext context)
    parameters:
    - id: context
      type: DrawnUi.Draw.DrawingContext
    content.vb: Protected Overrides Sub Draw(context As DrawingContext)
  overridden: DrawnUi.Views.DrawnView.Draw(DrawnUi.Draw.DrawingContext)
  overload: DrawnUi.Views.Canvas.Draw*
references:
- uid: DrawnUi.Views
  commentId: N:DrawnUi.Views
  href: DrawnUi.html
  name: DrawnUi.Views
  nameWithType: DrawnUi.Views
  fullName: DrawnUi.Views
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Views
    name: Views
    href: DrawnUi.Views.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Views
    name: Views
    href: DrawnUi.Views.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: Microsoft.Maui.Controls.BindableObject
  commentId: T:Microsoft.Maui.Controls.BindableObject
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject
  name: BindableObject
  nameWithType: BindableObject
  fullName: Microsoft.Maui.Controls.BindableObject
- uid: Microsoft.Maui.Controls.Element
  commentId: T:Microsoft.Maui.Controls.Element
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  name: Element
  nameWithType: Element
  fullName: Microsoft.Maui.Controls.Element
- uid: Microsoft.Maui.Controls.StyleableElement
  commentId: T:Microsoft.Maui.Controls.StyleableElement
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement
  name: StyleableElement
  nameWithType: StyleableElement
  fullName: Microsoft.Maui.Controls.StyleableElement
- uid: Microsoft.Maui.Controls.NavigableElement
  commentId: T:Microsoft.Maui.Controls.NavigableElement
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement
  name: NavigableElement
  nameWithType: NavigableElement
  fullName: Microsoft.Maui.Controls.NavigableElement
- uid: Microsoft.Maui.Controls.VisualElement
  commentId: T:Microsoft.Maui.Controls.VisualElement
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  name: VisualElement
  nameWithType: VisualElement
  fullName: Microsoft.Maui.Controls.VisualElement
- uid: Microsoft.Maui.Controls.View
  commentId: T:Microsoft.Maui.Controls.View
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view
  name: View
  nameWithType: View
  fullName: Microsoft.Maui.Controls.View
- uid: Microsoft.Maui.Controls.Compatibility.Layout
  commentId: T:Microsoft.Maui.Controls.Compatibility.Layout
  parent: Microsoft.Maui.Controls.Compatibility
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout
  name: Layout
  nameWithType: Layout
  fullName: Microsoft.Maui.Controls.Compatibility.Layout
- uid: Microsoft.Maui.Controls.TemplatedView
  commentId: T:Microsoft.Maui.Controls.TemplatedView
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview
  name: TemplatedView
  nameWithType: TemplatedView
  fullName: Microsoft.Maui.Controls.TemplatedView
- uid: Microsoft.Maui.Controls.ContentView
  commentId: T:Microsoft.Maui.Controls.ContentView
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.contentview
  name: ContentView
  nameWithType: ContentView
  fullName: Microsoft.Maui.Controls.ContentView
- uid: DrawnUi.Views.DrawnView
  commentId: T:DrawnUi.Views.DrawnView
  parent: DrawnUi.Views
  href: DrawnUi.Views.DrawnView.html
  name: DrawnView
  nameWithType: DrawnView
  fullName: DrawnUi.Views.DrawnView
- uid: System.ComponentModel.INotifyPropertyChanged
  commentId: T:System.ComponentModel.INotifyPropertyChanged
  parent: System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged
  name: INotifyPropertyChanged
  nameWithType: INotifyPropertyChanged
  fullName: System.ComponentModel.INotifyPropertyChanged
- uid: Microsoft.Maui.Controls.IEffectControlProvider
  commentId: T:Microsoft.Maui.Controls.IEffectControlProvider
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ieffectcontrolprovider
  name: IEffectControlProvider
  nameWithType: IEffectControlProvider
  fullName: Microsoft.Maui.Controls.IEffectControlProvider
- uid: Microsoft.Maui.IToolTipElement
  commentId: T:Microsoft.Maui.IToolTipElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.itooltipelement
  name: IToolTipElement
  nameWithType: IToolTipElement
  fullName: Microsoft.Maui.IToolTipElement
- uid: Microsoft.Maui.IContextFlyoutElement
  commentId: T:Microsoft.Maui.IContextFlyoutElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.icontextflyoutelement
  name: IContextFlyoutElement
  nameWithType: IContextFlyoutElement
  fullName: Microsoft.Maui.IContextFlyoutElement
- uid: Microsoft.Maui.Controls.IAnimatable
  commentId: T:Microsoft.Maui.Controls.IAnimatable
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ianimatable
  name: IAnimatable
  nameWithType: IAnimatable
  fullName: Microsoft.Maui.Controls.IAnimatable
- uid: Microsoft.Maui.Controls.IViewController
  commentId: T:Microsoft.Maui.Controls.IViewController
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.iviewcontroller
  name: IViewController
  nameWithType: IViewController
  fullName: Microsoft.Maui.Controls.IViewController
- uid: Microsoft.Maui.Controls.IVisualElementController
  commentId: T:Microsoft.Maui.Controls.IVisualElementController
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivisualelementcontroller
  name: IVisualElementController
  nameWithType: IVisualElementController
  fullName: Microsoft.Maui.Controls.IVisualElementController
- uid: Microsoft.Maui.Controls.IElementController
  commentId: T:Microsoft.Maui.Controls.IElementController
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementcontroller
  name: IElementController
  nameWithType: IElementController
  fullName: Microsoft.Maui.Controls.IElementController
- uid: Microsoft.Maui.Controls.Internals.IGestureController
  commentId: T:Microsoft.Maui.Controls.Internals.IGestureController
  parent: Microsoft.Maui.Controls.Internals
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.internals.igesturecontroller
  name: IGestureController
  nameWithType: IGestureController
  fullName: Microsoft.Maui.Controls.Internals.IGestureController
- uid: Microsoft.Maui.Controls.IGestureRecognizers
  commentId: T:Microsoft.Maui.Controls.IGestureRecognizers
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.igesturerecognizers
  name: IGestureRecognizers
  nameWithType: IGestureRecognizers
  fullName: Microsoft.Maui.Controls.IGestureRecognizers
- uid: Microsoft.Maui.IPropertyMapperView
  commentId: T:Microsoft.Maui.IPropertyMapperView
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ipropertymapperview
  name: IPropertyMapperView
  nameWithType: IPropertyMapperView
  fullName: Microsoft.Maui.IPropertyMapperView
- uid: Microsoft.Maui.HotReload.IHotReloadableView
  commentId: T:Microsoft.Maui.HotReload.IHotReloadableView
  parent: Microsoft.Maui.HotReload
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload.ihotreloadableview
  name: IHotReloadableView
  nameWithType: IHotReloadableView
  fullName: Microsoft.Maui.HotReload.IHotReloadableView
- uid: Microsoft.Maui.IReplaceableView
  commentId: T:Microsoft.Maui.IReplaceableView
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ireplaceableview
  name: IReplaceableView
  nameWithType: IReplaceableView
  fullName: Microsoft.Maui.IReplaceableView
- uid: Microsoft.Maui.Controls.ILayout
  commentId: T:Microsoft.Maui.Controls.ILayout
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ilayout
  name: ILayout
  nameWithType: ILayout
  fullName: Microsoft.Maui.Controls.ILayout
- uid: Microsoft.Maui.Controls.ILayoutController
  commentId: T:Microsoft.Maui.Controls.ILayoutController
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ilayoutcontroller
  name: ILayoutController
  nameWithType: ILayoutController
  fullName: Microsoft.Maui.Controls.ILayoutController
- uid: Microsoft.Maui.IContentView
  commentId: T:Microsoft.Maui.IContentView
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.icontentview
  name: IContentView
  nameWithType: IContentView
  fullName: Microsoft.Maui.IContentView
- uid: Microsoft.Maui.IView
  commentId: T:Microsoft.Maui.IView
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  name: IView
  nameWithType: IView
  fullName: Microsoft.Maui.IView
- uid: Microsoft.Maui.IElement
  commentId: T:Microsoft.Maui.IElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielement
  name: IElement
  nameWithType: IElement
  fullName: Microsoft.Maui.IElement
- uid: Microsoft.Maui.ITransform
  commentId: T:Microsoft.Maui.ITransform
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.itransform
  name: ITransform
  nameWithType: ITransform
  fullName: Microsoft.Maui.ITransform
- uid: Microsoft.Maui.IPadding
  commentId: T:Microsoft.Maui.IPadding
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ipadding
  name: IPadding
  nameWithType: IPadding
  fullName: Microsoft.Maui.IPadding
- uid: Microsoft.Maui.ICrossPlatformLayout
  commentId: T:Microsoft.Maui.ICrossPlatformLayout
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.icrossplatformlayout
  name: ICrossPlatformLayout
  nameWithType: ICrossPlatformLayout
  fullName: Microsoft.Maui.ICrossPlatformLayout
- uid: DrawnUi.Draw.IDrawnBase
  commentId: T:DrawnUi.Draw.IDrawnBase
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IDrawnBase.html
  name: IDrawnBase
  nameWithType: IDrawnBase
  fullName: DrawnUi.Draw.IDrawnBase
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: DrawnUi.Draw.ICanBeUpdatedWithContext
  commentId: T:DrawnUi.Draw.ICanBeUpdatedWithContext
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ICanBeUpdatedWithContext.html
  name: ICanBeUpdatedWithContext
  nameWithType: ICanBeUpdatedWithContext
  fullName: DrawnUi.Draw.ICanBeUpdatedWithContext
- uid: DrawnUi.Draw.ICanBeUpdated
  commentId: T:DrawnUi.Draw.ICanBeUpdated
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ICanBeUpdated.html
  name: ICanBeUpdated
  nameWithType: ICanBeUpdated
  fullName: DrawnUi.Draw.ICanBeUpdated
- uid: DrawnUi.Draw.IAnimatorsManager
  commentId: T:DrawnUi.Draw.IAnimatorsManager
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IAnimatorsManager.html
  name: IAnimatorsManager
  nameWithType: IAnimatorsManager
  fullName: DrawnUi.Draw.IAnimatorsManager
- uid: Microsoft.Maui.IVisualTreeElement
  commentId: T:Microsoft.Maui.IVisualTreeElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ivisualtreeelement
  name: IVisualTreeElement
  nameWithType: IVisualTreeElement
  fullName: Microsoft.Maui.IVisualTreeElement
- uid: AppoMobi.Maui.Gestures.IGestureListener
  commentId: T:AppoMobi.Maui.Gestures.IGestureListener
  parent: AppoMobi.Maui.Gestures
  isExternal: true
  name: IGestureListener
  nameWithType: IGestureListener
  fullName: AppoMobi.Maui.Gestures.IGestureListener
- uid: DrawnUi.Views.DrawnView.DumpLayersTree(DrawnUi.Draw.VisualLayer,System.String,System.Boolean,System.Int32)
  commentId: M:DrawnUi.Views.DrawnView.DumpLayersTree(DrawnUi.Draw.VisualLayer,System.String,System.Boolean,System.Int32)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DumpLayersTree_DrawnUi_Draw_VisualLayer_System_String_System_Boolean_System_Int32_
  name: DumpLayersTree(VisualLayer, string, bool, int)
  nameWithType: DrawnView.DumpLayersTree(VisualLayer, string, bool, int)
  fullName: DrawnUi.Views.DrawnView.DumpLayersTree(DrawnUi.Draw.VisualLayer, string, bool, int)
  nameWithType.vb: DrawnView.DumpLayersTree(VisualLayer, String, Boolean, Integer)
  fullName.vb: DrawnUi.Views.DrawnView.DumpLayersTree(DrawnUi.Draw.VisualLayer, String, Boolean, Integer)
  name.vb: DumpLayersTree(VisualLayer, String, Boolean, Integer)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.DumpLayersTree(DrawnUi.Draw.VisualLayer,System.String,System.Boolean,System.Int32)
    name: DumpLayersTree
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DumpLayersTree_DrawnUi_Draw_VisualLayer_System_String_System_Boolean_System_Int32_
  - name: (
  - uid: DrawnUi.Draw.VisualLayer
    name: VisualLayer
    href: DrawnUi.Draw.VisualLayer.html
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.DumpLayersTree(DrawnUi.Draw.VisualLayer,System.String,System.Boolean,System.Int32)
    name: DumpLayersTree
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DumpLayersTree_DrawnUi_Draw_VisualLayer_System_String_System_Boolean_System_Int32_
  - name: (
  - uid: DrawnUi.Draw.VisualLayer
    name: VisualLayer
    href: DrawnUi.Draw.VisualLayer.html
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: DrawnUi.Views.DrawnView.Diagnostics
  commentId: F:DrawnUi.Views.DrawnView.Diagnostics
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Diagnostics
  name: Diagnostics
  nameWithType: DrawnView.Diagnostics
  fullName: DrawnUi.Views.DrawnView.Diagnostics
- uid: DrawnUi.Views.DrawnView.Update
  commentId: M:DrawnUi.Views.DrawnView.Update
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Update
  name: Update()
  nameWithType: DrawnView.Update()
  fullName: DrawnUi.Views.DrawnView.Update()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.Update
    name: Update
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Update
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.Update
    name: Update
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Update
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.IsUsingHardwareAcceleration
  commentId: P:DrawnUi.Views.DrawnView.IsUsingHardwareAcceleration
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_IsUsingHardwareAcceleration
  name: IsUsingHardwareAcceleration
  nameWithType: DrawnView.IsUsingHardwareAcceleration
  fullName: DrawnUi.Views.DrawnView.IsUsingHardwareAcceleration
- uid: DrawnUi.Views.DrawnView.NeedRedraw
  commentId: P:DrawnUi.Views.DrawnView.NeedRedraw
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_NeedRedraw
  name: NeedRedraw
  nameWithType: DrawnView.NeedRedraw
  fullName: DrawnUi.Views.DrawnView.NeedRedraw
- uid: DrawnUi.Views.DrawnView.IsDirty
  commentId: P:DrawnUi.Views.DrawnView.IsDirty
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_IsDirty
  name: IsDirty
  nameWithType: DrawnView.IsDirty
  fullName: DrawnUi.Views.DrawnView.IsDirty
- uid: DrawnUi.Views.DrawnView.IsVisibleInViewTree
  commentId: M:DrawnUi.Views.DrawnView.IsVisibleInViewTree
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_IsVisibleInViewTree
  name: IsVisibleInViewTree()
  nameWithType: DrawnView.IsVisibleInViewTree()
  fullName: DrawnUi.Views.DrawnView.IsVisibleInViewTree()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.IsVisibleInViewTree
    name: IsVisibleInViewTree
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_IsVisibleInViewTree
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.IsVisibleInViewTree
    name: IsVisibleInViewTree
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_IsVisibleInViewTree
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.TakeScreenShot(System.Action{SkiaSharp.SKImage})
  commentId: M:DrawnUi.Views.DrawnView.TakeScreenShot(System.Action{SkiaSharp.SKImage})
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_TakeScreenShot_System_Action_SkiaSharp_SKImage__
  name: TakeScreenShot(Action<SKImage>)
  nameWithType: DrawnView.TakeScreenShot(Action<SKImage>)
  fullName: DrawnUi.Views.DrawnView.TakeScreenShot(System.Action<SkiaSharp.SKImage>)
  nameWithType.vb: DrawnView.TakeScreenShot(Action(Of SKImage))
  fullName.vb: DrawnUi.Views.DrawnView.TakeScreenShot(System.Action(Of SkiaSharp.SKImage))
  name.vb: TakeScreenShot(Action(Of SKImage))
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.TakeScreenShot(System.Action{SkiaSharp.SKImage})
    name: TakeScreenShot
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_TakeScreenShot_System_Action_SkiaSharp_SKImage__
  - name: (
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: <
  - uid: SkiaSharp.SKImage
    name: SKImage
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skimage
  - name: '>'
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.TakeScreenShot(System.Action{SkiaSharp.SKImage})
    name: TakeScreenShot
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_TakeScreenShot_System_Action_SkiaSharp_SKImage__
  - name: (
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: (
  - name: Of
  - name: " "
  - uid: SkiaSharp.SKImage
    name: SKImage
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skimage
  - name: )
  - name: )
- uid: DrawnUi.Views.DrawnView.InvalidateByChild(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Views.DrawnView.InvalidateByChild(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidateByChild_DrawnUi_Draw_SkiaControl_
  name: InvalidateByChild(SkiaControl)
  nameWithType: DrawnView.InvalidateByChild(SkiaControl)
  fullName: DrawnUi.Views.DrawnView.InvalidateByChild(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.InvalidateByChild(DrawnUi.Draw.SkiaControl)
    name: InvalidateByChild
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidateByChild_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.InvalidateByChild(DrawnUi.Draw.SkiaControl)
    name: InvalidateByChild
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidateByChild_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: DrawnUi.Views.DrawnView.UpdateByChild(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Views.DrawnView.UpdateByChild(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UpdateByChild_DrawnUi_Draw_SkiaControl_
  name: UpdateByChild(SkiaControl)
  nameWithType: DrawnView.UpdateByChild(SkiaControl)
  fullName: DrawnUi.Views.DrawnView.UpdateByChild(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.UpdateByChild(DrawnUi.Draw.SkiaControl)
    name: UpdateByChild
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UpdateByChild_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.UpdateByChild(DrawnUi.Draw.SkiaControl)
    name: UpdateByChild
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UpdateByChild_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: DrawnUi.Views.DrawnView.GetOnScreenVisibleArea(DrawnUi.Draw.DrawingContext,System.Numerics.Vector2)
  commentId: M:DrawnUi.Views.DrawnView.GetOnScreenVisibleArea(DrawnUi.Draw.DrawingContext,System.Numerics.Vector2)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GetOnScreenVisibleArea_DrawnUi_Draw_DrawingContext_System_Numerics_Vector2_
  name: GetOnScreenVisibleArea(DrawingContext, Vector2)
  nameWithType: DrawnView.GetOnScreenVisibleArea(DrawingContext, Vector2)
  fullName: DrawnUi.Views.DrawnView.GetOnScreenVisibleArea(DrawnUi.Draw.DrawingContext, System.Numerics.Vector2)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.GetOnScreenVisibleArea(DrawnUi.Draw.DrawingContext,System.Numerics.Vector2)
    name: GetOnScreenVisibleArea
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GetOnScreenVisibleArea_DrawnUi_Draw_DrawingContext_System_Numerics_Vector2_
  - name: (
  - uid: DrawnUi.Draw.DrawingContext
    name: DrawingContext
    href: DrawnUi.Draw.DrawingContext.html
  - name: ','
  - name: " "
  - uid: System.Numerics.Vector2
    name: Vector2
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.GetOnScreenVisibleArea(DrawnUi.Draw.DrawingContext,System.Numerics.Vector2)
    name: GetOnScreenVisibleArea
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GetOnScreenVisibleArea_DrawnUi_Draw_DrawingContext_System_Numerics_Vector2_
  - name: (
  - uid: DrawnUi.Draw.DrawingContext
    name: DrawingContext
    href: DrawnUi.Draw.DrawingContext.html
  - name: ','
  - name: " "
  - uid: System.Numerics.Vector2
    name: Vector2
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  - name: )
- uid: DrawnUi.Views.DrawnView.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  commentId: M:DrawnUi.Views.DrawnView.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnHandlerChanging_Microsoft_Maui_Controls_HandlerChangingEventArgs_
  name: OnHandlerChanging(HandlerChangingEventArgs)
  nameWithType: DrawnView.OnHandlerChanging(HandlerChangingEventArgs)
  fullName: DrawnUi.Views.DrawnView.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
    name: OnHandlerChanging
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnHandlerChanging_Microsoft_Maui_Controls_HandlerChangingEventArgs_
  - name: (
  - uid: Microsoft.Maui.Controls.HandlerChangingEventArgs
    name: HandlerChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.handlerchangingeventargs
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
    name: OnHandlerChanging
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnHandlerChanging_Microsoft_Maui_Controls_HandlerChangingEventArgs_
  - name: (
  - uid: Microsoft.Maui.Controls.HandlerChangingEventArgs
    name: HandlerChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.handlerchangingeventargs
  - name: )
- uid: DrawnUi.Views.DrawnView.HandlerWasSet
  commentId: E:DrawnUi.Views.DrawnView.HandlerWasSet
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_HandlerWasSet
  name: HandlerWasSet
  nameWithType: DrawnView.HandlerWasSet
  fullName: DrawnUi.Views.DrawnView.HandlerWasSet
- uid: DrawnUi.Views.DrawnView.TakeScreenShotInternal(SkiaSharp.SKSurface)
  commentId: M:DrawnUi.Views.DrawnView.TakeScreenShotInternal(SkiaSharp.SKSurface)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_TakeScreenShotInternal_SkiaSharp_SKSurface_
  name: TakeScreenShotInternal(SKSurface)
  nameWithType: DrawnView.TakeScreenShotInternal(SKSurface)
  fullName: DrawnUi.Views.DrawnView.TakeScreenShotInternal(SkiaSharp.SKSurface)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.TakeScreenShotInternal(SkiaSharp.SKSurface)
    name: TakeScreenShotInternal
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_TakeScreenShotInternal_SkiaSharp_SKSurface_
  - name: (
  - uid: SkiaSharp.SKSurface
    name: SKSurface
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.sksurface
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.TakeScreenShotInternal(SkiaSharp.SKSurface)
    name: TakeScreenShotInternal
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_TakeScreenShotInternal_SkiaSharp_SKSurface_
  - name: (
  - uid: SkiaSharp.SKSurface
    name: SKSurface
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.sksurface
  - name: )
- uid: DrawnUi.Views.DrawnView.PostponeExecutionBeforeDraw(System.Action)
  commentId: M:DrawnUi.Views.DrawnView.PostponeExecutionBeforeDraw(System.Action)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PostponeExecutionBeforeDraw_System_Action_
  name: PostponeExecutionBeforeDraw(Action)
  nameWithType: DrawnView.PostponeExecutionBeforeDraw(Action)
  fullName: DrawnUi.Views.DrawnView.PostponeExecutionBeforeDraw(System.Action)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.PostponeExecutionBeforeDraw(System.Action)
    name: PostponeExecutionBeforeDraw
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PostponeExecutionBeforeDraw_System_Action_
  - name: (
  - uid: System.Action
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.PostponeExecutionBeforeDraw(System.Action)
    name: PostponeExecutionBeforeDraw
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PostponeExecutionBeforeDraw_System_Action_
  - name: (
  - uid: System.Action
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action
  - name: )
- uid: DrawnUi.Views.DrawnView.PostponeExecutionAfterDraw(System.Action)
  commentId: M:DrawnUi.Views.DrawnView.PostponeExecutionAfterDraw(System.Action)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PostponeExecutionAfterDraw_System_Action_
  name: PostponeExecutionAfterDraw(Action)
  nameWithType: DrawnView.PostponeExecutionAfterDraw(Action)
  fullName: DrawnUi.Views.DrawnView.PostponeExecutionAfterDraw(System.Action)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.PostponeExecutionAfterDraw(System.Action)
    name: PostponeExecutionAfterDraw
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PostponeExecutionAfterDraw_System_Action_
  - name: (
  - uid: System.Action
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.PostponeExecutionAfterDraw(System.Action)
    name: PostponeExecutionAfterDraw
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PostponeExecutionAfterDraw_System_Action_
  - name: (
  - uid: System.Action
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action
  - name: )
- uid: DrawnUi.Views.DrawnView.ExecuteBeforeDraw
  commentId: P:DrawnUi.Views.DrawnView.ExecuteBeforeDraw
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ExecuteBeforeDraw
  name: ExecuteBeforeDraw
  nameWithType: DrawnView.ExecuteBeforeDraw
  fullName: DrawnUi.Views.DrawnView.ExecuteBeforeDraw
- uid: DrawnUi.Views.DrawnView.ExecuteAfterDraw
  commentId: P:DrawnUi.Views.DrawnView.ExecuteAfterDraw
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ExecuteAfterDraw
  name: ExecuteAfterDraw
  nameWithType: DrawnView.ExecuteAfterDraw
  fullName: DrawnUi.Views.DrawnView.ExecuteAfterDraw
- uid: DrawnUi.Views.DrawnView.CallbackScreenshot
  commentId: F:DrawnUi.Views.DrawnView.CallbackScreenshot
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CallbackScreenshot
  name: CallbackScreenshot
  nameWithType: DrawnView.CallbackScreenshot
  fullName: DrawnUi.Views.DrawnView.CallbackScreenshot
- uid: DrawnUi.Views.DrawnView.RenderingSubscribers
  commentId: F:DrawnUi.Views.DrawnView.RenderingSubscribers
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RenderingSubscribers
  name: RenderingSubscribers
  nameWithType: DrawnView.RenderingSubscribers
  fullName: DrawnUi.Views.DrawnView.RenderingSubscribers
- uid: DrawnUi.Views.DrawnView.SubscribeToRenderingFinished(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Views.DrawnView.SubscribeToRenderingFinished(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SubscribeToRenderingFinished_DrawnUi_Draw_SkiaControl_
  name: SubscribeToRenderingFinished(SkiaControl)
  nameWithType: DrawnView.SubscribeToRenderingFinished(SkiaControl)
  fullName: DrawnUi.Views.DrawnView.SubscribeToRenderingFinished(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.SubscribeToRenderingFinished(DrawnUi.Draw.SkiaControl)
    name: SubscribeToRenderingFinished
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SubscribeToRenderingFinished_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.SubscribeToRenderingFinished(DrawnUi.Draw.SkiaControl)
    name: SubscribeToRenderingFinished
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SubscribeToRenderingFinished_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: DrawnUi.Views.DrawnView.UsubscribeFromRenderingFinished(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Views.DrawnView.UsubscribeFromRenderingFinished(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UsubscribeFromRenderingFinished_DrawnUi_Draw_SkiaControl_
  name: UsubscribeFromRenderingFinished(SkiaControl)
  nameWithType: DrawnView.UsubscribeFromRenderingFinished(SkiaControl)
  fullName: DrawnUi.Views.DrawnView.UsubscribeFromRenderingFinished(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.UsubscribeFromRenderingFinished(DrawnUi.Draw.SkiaControl)
    name: UsubscribeFromRenderingFinished
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UsubscribeFromRenderingFinished_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.UsubscribeFromRenderingFinished(DrawnUi.Draw.SkiaControl)
    name: UsubscribeFromRenderingFinished
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UsubscribeFromRenderingFinished_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: DrawnUi.Views.DrawnView.RegisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
  commentId: M:DrawnUi.Views.DrawnView.RegisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RegisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_
  name: RegisterGestureListener(ISkiaGestureListener)
  nameWithType: DrawnView.RegisterGestureListener(ISkiaGestureListener)
  fullName: DrawnUi.Views.DrawnView.RegisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.RegisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
    name: RegisterGestureListener
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RegisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_
  - name: (
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.RegisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
    name: RegisterGestureListener
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RegisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_
  - name: (
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: )
- uid: DrawnUi.Views.DrawnView.UnregisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
  commentId: M:DrawnUi.Views.DrawnView.UnregisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UnregisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_
  name: UnregisterGestureListener(ISkiaGestureListener)
  nameWithType: DrawnView.UnregisterGestureListener(ISkiaGestureListener)
  fullName: DrawnUi.Views.DrawnView.UnregisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.UnregisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
    name: UnregisterGestureListener
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UnregisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_
  - name: (
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.UnregisterGestureListener(DrawnUi.Draw.ISkiaGestureListener)
    name: UnregisterGestureListener
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UnregisterGestureListener_DrawnUi_Draw_ISkiaGestureListener_
  - name: (
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: )
- uid: DrawnUi.Views.DrawnView.LockIterateListeners
  commentId: F:DrawnUi.Views.DrawnView.LockIterateListeners
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_LockIterateListeners
  name: LockIterateListeners
  nameWithType: DrawnView.LockIterateListeners
  fullName: DrawnUi.Views.DrawnView.LockIterateListeners
- uid: DrawnUi.Views.DrawnView.GestureListeners
  commentId: P:DrawnUi.Views.DrawnView.GestureListeners
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GestureListeners
  name: GestureListeners
  nameWithType: DrawnView.GestureListeners
  fullName: DrawnUi.Views.DrawnView.GestureListeners
- uid: DrawnUi.Views.DrawnView.DrawingRect
  commentId: P:DrawnUi.Views.DrawnView.DrawingRect
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DrawingRect
  name: DrawingRect
  nameWithType: DrawnView.DrawingRect
  fullName: DrawnUi.Views.DrawnView.DrawingRect
- uid: DrawnUi.Views.DrawnView.AddAnimator(DrawnUi.Draw.ISkiaAnimator)
  commentId: M:DrawnUi.Views.DrawnView.AddAnimator(DrawnUi.Draw.ISkiaAnimator)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_AddAnimator_DrawnUi_Draw_ISkiaAnimator_
  name: AddAnimator(ISkiaAnimator)
  nameWithType: DrawnView.AddAnimator(ISkiaAnimator)
  fullName: DrawnUi.Views.DrawnView.AddAnimator(DrawnUi.Draw.ISkiaAnimator)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.AddAnimator(DrawnUi.Draw.ISkiaAnimator)
    name: AddAnimator
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_AddAnimator_DrawnUi_Draw_ISkiaAnimator_
  - name: (
  - uid: DrawnUi.Draw.ISkiaAnimator
    name: ISkiaAnimator
    href: DrawnUi.Draw.ISkiaAnimator.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.AddAnimator(DrawnUi.Draw.ISkiaAnimator)
    name: AddAnimator
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_AddAnimator_DrawnUi_Draw_ISkiaAnimator_
  - name: (
  - uid: DrawnUi.Draw.ISkiaAnimator
    name: ISkiaAnimator
    href: DrawnUi.Draw.ISkiaAnimator.html
  - name: )
- uid: DrawnUi.Views.DrawnView.RemoveAnimator(System.Guid)
  commentId: M:DrawnUi.Views.DrawnView.RemoveAnimator(System.Guid)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RemoveAnimator_System_Guid_
  name: RemoveAnimator(Guid)
  nameWithType: DrawnView.RemoveAnimator(Guid)
  fullName: DrawnUi.Views.DrawnView.RemoveAnimator(System.Guid)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.RemoveAnimator(System.Guid)
    name: RemoveAnimator
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RemoveAnimator_System_Guid_
  - name: (
  - uid: System.Guid
    name: Guid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.guid
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.RemoveAnimator(System.Guid)
    name: RemoveAnimator
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RemoveAnimator_System_Guid_
  - name: (
  - uid: System.Guid
    name: Guid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.guid
  - name: )
- uid: DrawnUi.Views.DrawnView.RegisterAnimator(DrawnUi.Draw.ISkiaAnimator)
  commentId: M:DrawnUi.Views.DrawnView.RegisterAnimator(DrawnUi.Draw.ISkiaAnimator)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RegisterAnimator_DrawnUi_Draw_ISkiaAnimator_
  name: RegisterAnimator(ISkiaAnimator)
  nameWithType: DrawnView.RegisterAnimator(ISkiaAnimator)
  fullName: DrawnUi.Views.DrawnView.RegisterAnimator(DrawnUi.Draw.ISkiaAnimator)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.RegisterAnimator(DrawnUi.Draw.ISkiaAnimator)
    name: RegisterAnimator
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RegisterAnimator_DrawnUi_Draw_ISkiaAnimator_
  - name: (
  - uid: DrawnUi.Draw.ISkiaAnimator
    name: ISkiaAnimator
    href: DrawnUi.Draw.ISkiaAnimator.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.RegisterAnimator(DrawnUi.Draw.ISkiaAnimator)
    name: RegisterAnimator
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RegisterAnimator_DrawnUi_Draw_ISkiaAnimator_
  - name: (
  - uid: DrawnUi.Draw.ISkiaAnimator
    name: ISkiaAnimator
    href: DrawnUi.Draw.ISkiaAnimator.html
  - name: )
- uid: DrawnUi.Views.DrawnView.UnregisterAnimator(System.Guid)
  commentId: M:DrawnUi.Views.DrawnView.UnregisterAnimator(System.Guid)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UnregisterAnimator_System_Guid_
  name: UnregisterAnimator(Guid)
  nameWithType: DrawnView.UnregisterAnimator(Guid)
  fullName: DrawnUi.Views.DrawnView.UnregisterAnimator(System.Guid)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.UnregisterAnimator(System.Guid)
    name: UnregisterAnimator
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UnregisterAnimator_System_Guid_
  - name: (
  - uid: System.Guid
    name: Guid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.guid
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.UnregisterAnimator(System.Guid)
    name: UnregisterAnimator
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UnregisterAnimator_System_Guid_
  - name: (
  - uid: System.Guid
    name: Guid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.guid
  - name: )
- uid: DrawnUi.Views.DrawnView.UnregisterAllAnimatorsByType(System.Type)
  commentId: M:DrawnUi.Views.DrawnView.UnregisterAllAnimatorsByType(System.Type)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UnregisterAllAnimatorsByType_System_Type_
  name: UnregisterAllAnimatorsByType(Type)
  nameWithType: DrawnView.UnregisterAllAnimatorsByType(Type)
  fullName: DrawnUi.Views.DrawnView.UnregisterAllAnimatorsByType(System.Type)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.UnregisterAllAnimatorsByType(System.Type)
    name: UnregisterAllAnimatorsByType
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UnregisterAllAnimatorsByType_System_Type_
  - name: (
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.UnregisterAllAnimatorsByType(System.Type)
    name: UnregisterAllAnimatorsByType
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UnregisterAllAnimatorsByType_System_Type_
  - name: (
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: DrawnUi.Views.DrawnView.UnregisterAllAnimatorsByParent(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Views.DrawnView.UnregisterAllAnimatorsByParent(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UnregisterAllAnimatorsByParent_DrawnUi_Draw_SkiaControl_
  name: UnregisterAllAnimatorsByParent(SkiaControl)
  nameWithType: DrawnView.UnregisterAllAnimatorsByParent(SkiaControl)
  fullName: DrawnUi.Views.DrawnView.UnregisterAllAnimatorsByParent(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.UnregisterAllAnimatorsByParent(DrawnUi.Draw.SkiaControl)
    name: UnregisterAllAnimatorsByParent
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UnregisterAllAnimatorsByParent_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.UnregisterAllAnimatorsByParent(DrawnUi.Draw.SkiaControl)
    name: UnregisterAllAnimatorsByParent
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UnregisterAllAnimatorsByParent_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: DrawnUi.Views.DrawnView.SetViewTreeVisibilityByParent(DrawnUi.Draw.SkiaControl,System.Boolean)
  commentId: M:DrawnUi.Views.DrawnView.SetViewTreeVisibilityByParent(DrawnUi.Draw.SkiaControl,System.Boolean)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SetViewTreeVisibilityByParent_DrawnUi_Draw_SkiaControl_System_Boolean_
  name: SetViewTreeVisibilityByParent(SkiaControl, bool)
  nameWithType: DrawnView.SetViewTreeVisibilityByParent(SkiaControl, bool)
  fullName: DrawnUi.Views.DrawnView.SetViewTreeVisibilityByParent(DrawnUi.Draw.SkiaControl, bool)
  nameWithType.vb: DrawnView.SetViewTreeVisibilityByParent(SkiaControl, Boolean)
  fullName.vb: DrawnUi.Views.DrawnView.SetViewTreeVisibilityByParent(DrawnUi.Draw.SkiaControl, Boolean)
  name.vb: SetViewTreeVisibilityByParent(SkiaControl, Boolean)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.SetViewTreeVisibilityByParent(DrawnUi.Draw.SkiaControl,System.Boolean)
    name: SetViewTreeVisibilityByParent
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SetViewTreeVisibilityByParent_DrawnUi_Draw_SkiaControl_System_Boolean_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.SetViewTreeVisibilityByParent(DrawnUi.Draw.SkiaControl,System.Boolean)
    name: SetViewTreeVisibilityByParent
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SetViewTreeVisibilityByParent_DrawnUi_Draw_SkiaControl_System_Boolean_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: DrawnUi.Views.DrawnView.SetPauseStateOfAllAnimatorsByParent(DrawnUi.Draw.SkiaControl,System.Boolean)
  commentId: M:DrawnUi.Views.DrawnView.SetPauseStateOfAllAnimatorsByParent(DrawnUi.Draw.SkiaControl,System.Boolean)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SetPauseStateOfAllAnimatorsByParent_DrawnUi_Draw_SkiaControl_System_Boolean_
  name: SetPauseStateOfAllAnimatorsByParent(SkiaControl, bool)
  nameWithType: DrawnView.SetPauseStateOfAllAnimatorsByParent(SkiaControl, bool)
  fullName: DrawnUi.Views.DrawnView.SetPauseStateOfAllAnimatorsByParent(DrawnUi.Draw.SkiaControl, bool)
  nameWithType.vb: DrawnView.SetPauseStateOfAllAnimatorsByParent(SkiaControl, Boolean)
  fullName.vb: DrawnUi.Views.DrawnView.SetPauseStateOfAllAnimatorsByParent(DrawnUi.Draw.SkiaControl, Boolean)
  name.vb: SetPauseStateOfAllAnimatorsByParent(SkiaControl, Boolean)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.SetPauseStateOfAllAnimatorsByParent(DrawnUi.Draw.SkiaControl,System.Boolean)
    name: SetPauseStateOfAllAnimatorsByParent
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SetPauseStateOfAllAnimatorsByParent_DrawnUi_Draw_SkiaControl_System_Boolean_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.SetPauseStateOfAllAnimatorsByParent(DrawnUi.Draw.SkiaControl,System.Boolean)
    name: SetPauseStateOfAllAnimatorsByParent
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SetPauseStateOfAllAnimatorsByParent_DrawnUi_Draw_SkiaControl_System_Boolean_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: DrawnUi.Views.DrawnView.ExecutePostAnimators(DrawnUi.Draw.DrawingContext)
  commentId: M:DrawnUi.Views.DrawnView.ExecutePostAnimators(DrawnUi.Draw.DrawingContext)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ExecutePostAnimators_DrawnUi_Draw_DrawingContext_
  name: ExecutePostAnimators(DrawingContext)
  nameWithType: DrawnView.ExecutePostAnimators(DrawingContext)
  fullName: DrawnUi.Views.DrawnView.ExecutePostAnimators(DrawnUi.Draw.DrawingContext)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.ExecutePostAnimators(DrawnUi.Draw.DrawingContext)
    name: ExecutePostAnimators
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ExecutePostAnimators_DrawnUi_Draw_DrawingContext_
  - name: (
  - uid: DrawnUi.Draw.DrawingContext
    name: DrawingContext
    href: DrawnUi.Draw.DrawingContext.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.ExecutePostAnimators(DrawnUi.Draw.DrawingContext)
    name: ExecutePostAnimators
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ExecutePostAnimators_DrawnUi_Draw_DrawingContext_
  - name: (
  - uid: DrawnUi.Draw.DrawingContext
    name: DrawingContext
    href: DrawnUi.Draw.DrawingContext.html
  - name: )
- uid: DrawnUi.Views.DrawnView.PostAnimators
  commentId: P:DrawnUi.Views.DrawnView.PostAnimators
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PostAnimators
  name: PostAnimators
  nameWithType: DrawnView.PostAnimators
  fullName: DrawnUi.Views.DrawnView.PostAnimators
- uid: DrawnUi.Views.DrawnView.AnimatingControls
  commentId: P:DrawnUi.Views.DrawnView.AnimatingControls
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_AnimatingControls
  name: AnimatingControls
  nameWithType: DrawnView.AnimatingControls
  fullName: DrawnUi.Views.DrawnView.AnimatingControls
- uid: DrawnUi.Views.DrawnView.FrameTimeInterpolator
  commentId: F:DrawnUi.Views.DrawnView.FrameTimeInterpolator
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_FrameTimeInterpolator
  name: FrameTimeInterpolator
  nameWithType: DrawnView.FrameTimeInterpolator
  fullName: DrawnUi.Views.DrawnView.FrameTimeInterpolator
- uid: DrawnUi.Views.DrawnView.mLastFrameTime
  commentId: P:DrawnUi.Views.DrawnView.mLastFrameTime
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_mLastFrameTime
  name: mLastFrameTime
  nameWithType: DrawnView.mLastFrameTime
  fullName: DrawnUi.Views.DrawnView.mLastFrameTime
- uid: DrawnUi.Views.DrawnView.ExecuteAnimators(System.Int64)
  commentId: M:DrawnUi.Views.DrawnView.ExecuteAnimators(System.Int64)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ExecuteAnimators_System_Int64_
  name: ExecuteAnimators(long)
  nameWithType: DrawnView.ExecuteAnimators(long)
  fullName: DrawnUi.Views.DrawnView.ExecuteAnimators(long)
  nameWithType.vb: DrawnView.ExecuteAnimators(Long)
  fullName.vb: DrawnUi.Views.DrawnView.ExecuteAnimators(Long)
  name.vb: ExecuteAnimators(Long)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.ExecuteAnimators(System.Int64)
    name: ExecuteAnimators
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ExecuteAnimators_System_Int64_
  - name: (
  - uid: System.Int64
    name: long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.ExecuteAnimators(System.Int64)
    name: ExecuteAnimators
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ExecuteAnimators_System_Int64_
  - name: (
  - uid: System.Int64
    name: Long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
- uid: DrawnUi.Views.DrawnView.OnCanvasViewChanged
  commentId: M:DrawnUi.Views.DrawnView.OnCanvasViewChanged
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnCanvasViewChanged
  name: OnCanvasViewChanged()
  nameWithType: DrawnView.OnCanvasViewChanged()
  fullName: DrawnUi.Views.DrawnView.OnCanvasViewChanged()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.OnCanvasViewChanged
    name: OnCanvasViewChanged
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnCanvasViewChanged
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.OnCanvasViewChanged
    name: OnCanvasViewChanged
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnCanvasViewChanged
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.CanvasView
  commentId: P:DrawnUi.Views.DrawnView.CanvasView
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CanvasView
  name: CanvasView
  nameWithType: DrawnView.CanvasView
  fullName: DrawnUi.Views.DrawnView.CanvasView
- uid: DrawnUi.Views.DrawnView.SetDeviceOrientation(System.Int32)
  commentId: M:DrawnUi.Views.DrawnView.SetDeviceOrientation(System.Int32)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SetDeviceOrientation_System_Int32_
  name: SetDeviceOrientation(int)
  nameWithType: DrawnView.SetDeviceOrientation(int)
  fullName: DrawnUi.Views.DrawnView.SetDeviceOrientation(int)
  nameWithType.vb: DrawnView.SetDeviceOrientation(Integer)
  fullName.vb: DrawnUi.Views.DrawnView.SetDeviceOrientation(Integer)
  name.vb: SetDeviceOrientation(Integer)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.SetDeviceOrientation(System.Int32)
    name: SetDeviceOrientation
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SetDeviceOrientation_System_Int32_
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.SetDeviceOrientation(System.Int32)
    name: SetDeviceOrientation
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SetDeviceOrientation_System_Int32_
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: DrawnUi.Views.DrawnView.DeviceRotationChanged
  commentId: E:DrawnUi.Views.DrawnView.DeviceRotationChanged
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DeviceRotationChanged
  name: DeviceRotationChanged
  nameWithType: DrawnView.DeviceRotationChanged
  fullName: DrawnUi.Views.DrawnView.DeviceRotationChanged
- uid: DrawnUi.Views.DrawnView.DisplayRotationProperty
  commentId: F:DrawnUi.Views.DrawnView.DisplayRotationProperty
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DisplayRotationProperty
  name: DisplayRotationProperty
  nameWithType: DrawnView.DisplayRotationProperty
  fullName: DrawnUi.Views.DrawnView.DisplayRotationProperty
- uid: DrawnUi.Views.DrawnView.DeviceRotation
  commentId: P:DrawnUi.Views.DrawnView.DeviceRotation
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DeviceRotation
  name: DeviceRotation
  nameWithType: DrawnView.DeviceRotation
  fullName: DrawnUi.Views.DrawnView.DeviceRotation
- uid: DrawnUi.Views.DrawnView.HasHandler
  commentId: P:DrawnUi.Views.DrawnView.HasHandler
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_HasHandler
  name: HasHandler
  nameWithType: DrawnView.HasHandler
  fullName: DrawnUi.Views.DrawnView.HasHandler
- uid: DrawnUi.Views.DrawnView.DisconnectedHandler
  commentId: M:DrawnUi.Views.DrawnView.DisconnectedHandler
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DisconnectedHandler
  name: DisconnectedHandler()
  nameWithType: DrawnView.DisconnectedHandler()
  fullName: DrawnUi.Views.DrawnView.DisconnectedHandler()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.DisconnectedHandler
    name: DisconnectedHandler
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DisconnectedHandler
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.DisconnectedHandler
    name: DisconnectedHandler
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DisconnectedHandler
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.NeedGlobalRefreshCount
  commentId: P:DrawnUi.Views.DrawnView.NeedGlobalRefreshCount
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_NeedGlobalRefreshCount
  name: NeedGlobalRefreshCount
  nameWithType: DrawnView.NeedGlobalRefreshCount
  fullName: DrawnUi.Views.DrawnView.NeedGlobalRefreshCount
- uid: DrawnUi.Views.DrawnView.UpdateGlobal
  commentId: M:DrawnUi.Views.DrawnView.UpdateGlobal
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UpdateGlobal
  name: UpdateGlobal()
  nameWithType: DrawnView.UpdateGlobal()
  fullName: DrawnUi.Views.DrawnView.UpdateGlobal()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.UpdateGlobal
    name: UpdateGlobal
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UpdateGlobal
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.UpdateGlobal
    name: UpdateGlobal
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UpdateGlobal
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.NeedMeasureDrawn
  commentId: P:DrawnUi.Views.DrawnView.NeedMeasureDrawn
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_NeedMeasureDrawn
  name: NeedMeasureDrawn
  nameWithType: DrawnView.NeedMeasureDrawn
  fullName: DrawnUi.Views.DrawnView.NeedMeasureDrawn
- uid: DrawnUi.Views.DrawnView.OnCanRenderChanged(System.Boolean)
  commentId: M:DrawnUi.Views.DrawnView.OnCanRenderChanged(System.Boolean)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnCanRenderChanged_System_Boolean_
  name: OnCanRenderChanged(bool)
  nameWithType: DrawnView.OnCanRenderChanged(bool)
  fullName: DrawnUi.Views.DrawnView.OnCanRenderChanged(bool)
  nameWithType.vb: DrawnView.OnCanRenderChanged(Boolean)
  fullName.vb: DrawnUi.Views.DrawnView.OnCanRenderChanged(Boolean)
  name.vb: OnCanRenderChanged(Boolean)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.OnCanRenderChanged(System.Boolean)
    name: OnCanRenderChanged
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnCanRenderChanged_System_Boolean_
  - name: (
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.OnCanRenderChanged(System.Boolean)
    name: OnCanRenderChanged
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnCanRenderChanged_System_Boolean_
  - name: (
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: DrawnUi.Views.DrawnView.ConnectedHandler
  commentId: M:DrawnUi.Views.DrawnView.ConnectedHandler
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ConnectedHandler
  name: ConnectedHandler()
  nameWithType: DrawnView.ConnectedHandler()
  fullName: DrawnUi.Views.DrawnView.ConnectedHandler()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.ConnectedHandler
    name: ConnectedHandler
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ConnectedHandler
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.ConnectedHandler
    name: ConnectedHandler
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ConnectedHandler
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.FixDensity
  commentId: M:DrawnUi.Views.DrawnView.FixDensity
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_FixDensity
  name: FixDensity()
  nameWithType: DrawnView.FixDensity()
  fullName: DrawnUi.Views.DrawnView.FixDensity()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.FixDensity
    name: FixDensity
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_FixDensity
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.FixDensity
    name: FixDensity
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_FixDensity
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.StopDrawingWhenUpdateIsLocked
  commentId: P:DrawnUi.Views.DrawnView.StopDrawingWhenUpdateIsLocked
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_StopDrawingWhenUpdateIsLocked
  name: StopDrawingWhenUpdateIsLocked
  nameWithType: DrawnView.StopDrawingWhenUpdateIsLocked
  fullName: DrawnUi.Views.DrawnView.StopDrawingWhenUpdateIsLocked
- uid: DrawnUi.Views.DrawnView.TimeDrawingStarted
  commentId: P:DrawnUi.Views.DrawnView.TimeDrawingStarted
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_TimeDrawingStarted
  name: TimeDrawingStarted
  nameWithType: DrawnView.TimeDrawingStarted
  fullName: DrawnUi.Views.DrawnView.TimeDrawingStarted
- uid: DrawnUi.Views.DrawnView.TimeDrawingComplete
  commentId: P:DrawnUi.Views.DrawnView.TimeDrawingComplete
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_TimeDrawingComplete
  name: TimeDrawingComplete
  nameWithType: DrawnView.TimeDrawingComplete
  fullName: DrawnUi.Views.DrawnView.TimeDrawingComplete
- uid: DrawnUi.Views.DrawnView.InvalidateViewport
  commentId: M:DrawnUi.Views.DrawnView.InvalidateViewport
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidateViewport
  name: InvalidateViewport()
  nameWithType: DrawnView.InvalidateViewport()
  fullName: DrawnUi.Views.DrawnView.InvalidateViewport()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.InvalidateViewport
    name: InvalidateViewport
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidateViewport
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.InvalidateViewport
    name: InvalidateViewport
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidateViewport
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.Repaint
  commentId: M:DrawnUi.Views.DrawnView.Repaint
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Repaint
  name: Repaint()
  nameWithType: DrawnView.Repaint()
  fullName: DrawnUi.Views.DrawnView.Repaint()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.Repaint
    name: Repaint
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Repaint
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.Repaint
    name: Repaint
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Repaint
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.OrderedDraw
  commentId: P:DrawnUi.Views.DrawnView.OrderedDraw
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OrderedDraw
  name: OrderedDraw
  nameWithType: DrawnView.OrderedDraw
  fullName: DrawnUi.Views.DrawnView.OrderedDraw
- uid: DrawnUi.Views.DrawnView.ResetUpdate
  commentId: M:DrawnUi.Views.DrawnView.ResetUpdate
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ResetUpdate
  name: ResetUpdate()
  nameWithType: DrawnView.ResetUpdate()
  fullName: DrawnUi.Views.DrawnView.ResetUpdate()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.ResetUpdate
    name: ResetUpdate
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ResetUpdate
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.ResetUpdate
    name: ResetUpdate
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ResetUpdate
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.InvalidatedCanvas
  commentId: P:DrawnUi.Views.DrawnView.InvalidatedCanvas
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidatedCanvas
  name: InvalidatedCanvas
  nameWithType: DrawnView.InvalidatedCanvas
  fullName: DrawnUi.Views.DrawnView.InvalidatedCanvas
- uid: DrawnUi.Views.DrawnView.IsRendering
  commentId: P:DrawnUi.Views.DrawnView.IsRendering
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_IsRendering
  name: IsRendering
  nameWithType: DrawnView.IsRendering
  fullName: DrawnUi.Views.DrawnView.IsRendering
- uid: DrawnUi.Views.DrawnView.Delayed
  commentId: P:DrawnUi.Views.DrawnView.Delayed
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Delayed
  name: Delayed
  nameWithType: DrawnView.Delayed
  fullName: DrawnUi.Views.DrawnView.Delayed
- uid: DrawnUi.Views.DrawnView.GetDensity
  commentId: M:DrawnUi.Views.DrawnView.GetDensity
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GetDensity
  name: GetDensity()
  nameWithType: DrawnView.GetDensity()
  fullName: DrawnUi.Views.DrawnView.GetDensity()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.GetDensity
    name: GetDensity
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GetDensity
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.GetDensity
    name: GetDensity
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GetDensity
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.CreateSkiaView
  commentId: M:DrawnUi.Views.DrawnView.CreateSkiaView
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CreateSkiaView
  name: CreateSkiaView()
  nameWithType: DrawnView.CreateSkiaView()
  fullName: DrawnUi.Views.DrawnView.CreateSkiaView()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.CreateSkiaView
    name: CreateSkiaView
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CreateSkiaView
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.CreateSkiaView
    name: CreateSkiaView
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CreateSkiaView
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.DestroySkiaView
  commentId: M:DrawnUi.Views.DrawnView.DestroySkiaView
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DestroySkiaView
  name: DestroySkiaView()
  nameWithType: DrawnView.DestroySkiaView()
  fullName: DrawnUi.Views.DrawnView.DestroySkiaView()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.DestroySkiaView
    name: DestroySkiaView
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DestroySkiaView
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.DestroySkiaView
    name: DestroySkiaView
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DestroySkiaView
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.IsDisposing
  commentId: P:DrawnUi.Views.DrawnView.IsDisposing
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_IsDisposing
  name: IsDisposing
  nameWithType: DrawnView.IsDisposing
  fullName: DrawnUi.Views.DrawnView.IsDisposing
- uid: DrawnUi.Views.DrawnView.Dispose
  commentId: M:DrawnUi.Views.DrawnView.Dispose
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Dispose
  name: Dispose()
  nameWithType: DrawnView.Dispose()
  fullName: DrawnUi.Views.DrawnView.Dispose()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.Dispose
    name: Dispose
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Dispose
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.Dispose
    name: Dispose
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Dispose
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.ViewDisposing
  commentId: E:DrawnUi.Views.DrawnView.ViewDisposing
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ViewDisposing
  name: ViewDisposing
  nameWithType: DrawnView.ViewDisposing
  fullName: DrawnUi.Views.DrawnView.ViewDisposing
- uid: DrawnUi.Views.DrawnView.WillDispose
  commentId: M:DrawnUi.Views.DrawnView.WillDispose
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_WillDispose
  name: WillDispose()
  nameWithType: DrawnView.WillDispose()
  fullName: DrawnUi.Views.DrawnView.WillDispose()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.WillDispose
    name: WillDispose
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_WillDispose
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.WillDispose
    name: WillDispose
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_WillDispose
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.OnDispose
  commentId: M:DrawnUi.Views.DrawnView.OnDispose
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnDispose
  name: OnDispose()
  nameWithType: DrawnView.OnDispose()
  fullName: DrawnUi.Views.DrawnView.OnDispose()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.OnDispose
    name: OnDispose
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnDispose
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.OnDispose
    name: OnDispose
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnDispose
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.InvalidateParents
  commentId: M:DrawnUi.Views.DrawnView.InvalidateParents
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidateParents
  name: InvalidateParents()
  nameWithType: DrawnView.InvalidateParents()
  fullName: DrawnUi.Views.DrawnView.InvalidateParents()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.InvalidateParents
    name: InvalidateParents
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidateParents
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.InvalidateParents
    name: InvalidateParents
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidateParents
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.OnSuperviewShouldRenderChanged(System.Boolean)
  commentId: M:DrawnUi.Views.DrawnView.OnSuperviewShouldRenderChanged(System.Boolean)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnSuperviewShouldRenderChanged_System_Boolean_
  name: OnSuperviewShouldRenderChanged(bool)
  nameWithType: DrawnView.OnSuperviewShouldRenderChanged(bool)
  fullName: DrawnUi.Views.DrawnView.OnSuperviewShouldRenderChanged(bool)
  nameWithType.vb: DrawnView.OnSuperviewShouldRenderChanged(Boolean)
  fullName.vb: DrawnUi.Views.DrawnView.OnSuperviewShouldRenderChanged(Boolean)
  name.vb: OnSuperviewShouldRenderChanged(Boolean)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.OnSuperviewShouldRenderChanged(System.Boolean)
    name: OnSuperviewShouldRenderChanged
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnSuperviewShouldRenderChanged_System_Boolean_
  - name: (
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.OnSuperviewShouldRenderChanged(System.Boolean)
    name: OnSuperviewShouldRenderChanged
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnSuperviewShouldRenderChanged_System_Boolean_
  - name: (
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: DrawnUi.Views.DrawnView.InvalidateChildren
  commentId: M:DrawnUi.Views.DrawnView.InvalidateChildren
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidateChildren
  name: InvalidateChildren()
  nameWithType: DrawnView.InvalidateChildren()
  fullName: DrawnUi.Views.DrawnView.InvalidateChildren()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.InvalidateChildren
    name: InvalidateChildren
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidateChildren
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.InvalidateChildren
    name: InvalidateChildren
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidateChildren
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.PrintDebug(System.String)
  commentId: M:DrawnUi.Views.DrawnView.PrintDebug(System.String)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PrintDebug_System_String_
  name: PrintDebug(string)
  nameWithType: DrawnView.PrintDebug(string)
  fullName: DrawnUi.Views.DrawnView.PrintDebug(string)
  nameWithType.vb: DrawnView.PrintDebug(String)
  fullName.vb: DrawnUi.Views.DrawnView.PrintDebug(String)
  name.vb: PrintDebug(String)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.PrintDebug(System.String)
    name: PrintDebug
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PrintDebug_System_String_
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.PrintDebug(System.String)
    name: PrintDebug
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PrintDebug_System_String_
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: DrawnUi.Views.DrawnView.NeedAutoSize
  commentId: P:DrawnUi.Views.DrawnView.NeedAutoSize
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_NeedAutoSize
  name: NeedAutoSize
  nameWithType: DrawnView.NeedAutoSize
  fullName: DrawnUi.Views.DrawnView.NeedAutoSize
- uid: DrawnUi.Views.DrawnView.NeedAutoHeight
  commentId: P:DrawnUi.Views.DrawnView.NeedAutoHeight
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_NeedAutoHeight
  name: NeedAutoHeight
  nameWithType: DrawnView.NeedAutoHeight
  fullName: DrawnUi.Views.DrawnView.NeedAutoHeight
- uid: DrawnUi.Views.DrawnView.NeedAutoWidth
  commentId: P:DrawnUi.Views.DrawnView.NeedAutoWidth
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_NeedAutoWidth
  name: NeedAutoWidth
  nameWithType: DrawnView.NeedAutoWidth
  fullName: DrawnUi.Views.DrawnView.NeedAutoWidth
- uid: DrawnUi.Views.DrawnView.ShouldInvalidateByChildren
  commentId: P:DrawnUi.Views.DrawnView.ShouldInvalidateByChildren
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ShouldInvalidateByChildren
  name: ShouldInvalidateByChildren
  nameWithType: DrawnView.ShouldInvalidateByChildren
  fullName: DrawnUi.Views.DrawnView.ShouldInvalidateByChildren
- uid: DrawnUi.Views.DrawnView.UpdateLocksProperty
  commentId: F:DrawnUi.Views.DrawnView.UpdateLocksProperty
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UpdateLocksProperty
  name: UpdateLocksProperty
  nameWithType: DrawnView.UpdateLocksProperty
  fullName: DrawnUi.Views.DrawnView.UpdateLocksProperty
- uid: DrawnUi.Views.DrawnView.UpdateLocks
  commentId: P:DrawnUi.Views.DrawnView.UpdateLocks
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UpdateLocks
  name: UpdateLocks
  nameWithType: DrawnView.UpdateLocks
  fullName: DrawnUi.Views.DrawnView.UpdateLocks
- uid: DrawnUi.Views.DrawnView.Uid
  commentId: P:DrawnUi.Views.DrawnView.Uid
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Uid
  name: Uid
  nameWithType: DrawnView.Uid
  fullName: DrawnUi.Views.DrawnView.Uid
- uid: DrawnUi.Views.DrawnView.LinearGradientAngleToPoints(System.Double)
  commentId: M:DrawnUi.Views.DrawnView.LinearGradientAngleToPoints(System.Double)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_LinearGradientAngleToPoints_System_Double_
  name: LinearGradientAngleToPoints(double)
  nameWithType: DrawnView.LinearGradientAngleToPoints(double)
  fullName: DrawnUi.Views.DrawnView.LinearGradientAngleToPoints(double)
  nameWithType.vb: DrawnView.LinearGradientAngleToPoints(Double)
  fullName.vb: DrawnUi.Views.DrawnView.LinearGradientAngleToPoints(Double)
  name.vb: LinearGradientAngleToPoints(Double)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.LinearGradientAngleToPoints(System.Double)
    name: LinearGradientAngleToPoints
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_LinearGradientAngleToPoints_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.LinearGradientAngleToPoints(System.Double)
    name: LinearGradientAngleToPoints
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_LinearGradientAngleToPoints_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Views.DrawnView.IsGhost
  commentId: P:DrawnUi.Views.DrawnView.IsGhost
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_IsGhost
  name: IsGhost
  nameWithType: DrawnView.IsGhost
  fullName: DrawnUi.Views.DrawnView.IsGhost
- uid: DrawnUi.Views.DrawnView.Clipping
  commentId: P:DrawnUi.Views.DrawnView.Clipping
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Clipping
  name: Clipping
  nameWithType: DrawnView.Clipping
  fullName: DrawnUi.Views.DrawnView.Clipping
- uid: DrawnUi.Views.DrawnView.CreateClip(System.Object,System.Boolean,SkiaSharp.SKPath)
  commentId: M:DrawnUi.Views.DrawnView.CreateClip(System.Object,System.Boolean,SkiaSharp.SKPath)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CreateClip_System_Object_System_Boolean_SkiaSharp_SKPath_
  name: CreateClip(object, bool, SKPath)
  nameWithType: DrawnView.CreateClip(object, bool, SKPath)
  fullName: DrawnUi.Views.DrawnView.CreateClip(object, bool, SkiaSharp.SKPath)
  nameWithType.vb: DrawnView.CreateClip(Object, Boolean, SKPath)
  fullName.vb: DrawnUi.Views.DrawnView.CreateClip(Object, Boolean, SkiaSharp.SKPath)
  name.vb: CreateClip(Object, Boolean, SKPath)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.CreateClip(System.Object,System.Boolean,SkiaSharp.SKPath)
    name: CreateClip
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CreateClip_System_Object_System_Boolean_SkiaSharp_SKPath_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKPath
    name: SKPath
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skpath
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.CreateClip(System.Object,System.Boolean,SkiaSharp.SKPath)
    name: CreateClip
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CreateClip_System_Object_System_Boolean_SkiaSharp_SKPath_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKPath
    name: SKPath
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skpath
  - name: )
- uid: DrawnUi.Views.DrawnView.Tag
  commentId: P:DrawnUi.Views.DrawnView.Tag
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Tag
  name: Tag
  nameWithType: DrawnView.Tag
  fullName: DrawnUi.Views.DrawnView.Tag
- uid: DrawnUi.Views.DrawnView.IsRootView(System.Single,System.Single,SkiaSharp.SKRect)
  commentId: M:DrawnUi.Views.DrawnView.IsRootView(System.Single,System.Single,SkiaSharp.SKRect)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_IsRootView_System_Single_System_Single_SkiaSharp_SKRect_
  name: IsRootView(float, float, SKRect)
  nameWithType: DrawnView.IsRootView(float, float, SKRect)
  fullName: DrawnUi.Views.DrawnView.IsRootView(float, float, SkiaSharp.SKRect)
  nameWithType.vb: DrawnView.IsRootView(Single, Single, SKRect)
  fullName.vb: DrawnUi.Views.DrawnView.IsRootView(Single, Single, SkiaSharp.SKRect)
  name.vb: IsRootView(Single, Single, SKRect)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.IsRootView(System.Single,System.Single,SkiaSharp.SKRect)
    name: IsRootView
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_IsRootView_System_Single_System_Single_SkiaSharp_SKRect_
  - name: (
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: ','
  - name: " "
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKRect
    name: SKRect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.IsRootView(System.Single,System.Single,SkiaSharp.SKRect)
    name: IsRootView
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_IsRootView_System_Single_System_Single_SkiaSharp_SKRect_
  - name: (
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: ','
  - name: " "
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKRect
    name: SKRect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  - name: )
- uid: DrawnUi.Views.DrawnView.CalculateLayout(SkiaSharp.SKRect,System.Double,System.Double,System.Double)
  commentId: M:DrawnUi.Views.DrawnView.CalculateLayout(SkiaSharp.SKRect,System.Double,System.Double,System.Double)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CalculateLayout_SkiaSharp_SKRect_System_Double_System_Double_System_Double_
  name: CalculateLayout(SKRect, double, double, double)
  nameWithType: DrawnView.CalculateLayout(SKRect, double, double, double)
  fullName: DrawnUi.Views.DrawnView.CalculateLayout(SkiaSharp.SKRect, double, double, double)
  nameWithType.vb: DrawnView.CalculateLayout(SKRect, Double, Double, Double)
  fullName.vb: DrawnUi.Views.DrawnView.CalculateLayout(SkiaSharp.SKRect, Double, Double, Double)
  name.vb: CalculateLayout(SKRect, Double, Double, Double)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.CalculateLayout(SkiaSharp.SKRect,System.Double,System.Double,System.Double)
    name: CalculateLayout
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CalculateLayout_SkiaSharp_SKRect_System_Double_System_Double_System_Double_
  - name: (
  - uid: SkiaSharp.SKRect
    name: SKRect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.CalculateLayout(SkiaSharp.SKRect,System.Double,System.Double,System.Double)
    name: CalculateLayout
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CalculateLayout_SkiaSharp_SKRect_System_Double_System_Double_System_Double_
  - name: (
  - uid: SkiaSharp.SKRect
    name: SKRect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Views.DrawnView.Arrange(SkiaSharp.SKRect,System.Double,System.Double,System.Double)
  commentId: M:DrawnUi.Views.DrawnView.Arrange(SkiaSharp.SKRect,System.Double,System.Double,System.Double)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Arrange_SkiaSharp_SKRect_System_Double_System_Double_System_Double_
  name: Arrange(SKRect, double, double, double)
  nameWithType: DrawnView.Arrange(SKRect, double, double, double)
  fullName: DrawnUi.Views.DrawnView.Arrange(SkiaSharp.SKRect, double, double, double)
  nameWithType.vb: DrawnView.Arrange(SKRect, Double, Double, Double)
  fullName.vb: DrawnUi.Views.DrawnView.Arrange(SkiaSharp.SKRect, Double, Double, Double)
  name.vb: Arrange(SKRect, Double, Double, Double)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.Arrange(SkiaSharp.SKRect,System.Double,System.Double,System.Double)
    name: Arrange
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Arrange_SkiaSharp_SKRect_System_Double_System_Double_System_Double_
  - name: (
  - uid: SkiaSharp.SKRect
    name: SKRect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.Arrange(SkiaSharp.SKRect,System.Double,System.Double,System.Double)
    name: Arrange
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Arrange_SkiaSharp_SKRect_System_Double_System_Double_System_Double_
  - name: (
  - uid: SkiaSharp.SKRect
    name: SKRect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Views.DrawnView.MeasuredSize
  commentId: P:DrawnUi.Views.DrawnView.MeasuredSize
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_MeasuredSize
  name: MeasuredSize
  nameWithType: DrawnView.MeasuredSize
  fullName: DrawnUi.Views.DrawnView.MeasuredSize
- uid: DrawnUi.Views.DrawnView.NeedMeasure
  commentId: P:DrawnUi.Views.DrawnView.NeedMeasure
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_NeedMeasure
  name: NeedMeasure
  nameWithType: DrawnView.NeedMeasure
  fullName: DrawnUi.Views.DrawnView.NeedMeasure
- uid: DrawnUi.Views.DrawnView.SetMeasured(System.Single,System.Single,System.Single)
  commentId: M:DrawnUi.Views.DrawnView.SetMeasured(System.Single,System.Single,System.Single)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SetMeasured_System_Single_System_Single_System_Single_
  name: SetMeasured(float, float, float)
  nameWithType: DrawnView.SetMeasured(float, float, float)
  fullName: DrawnUi.Views.DrawnView.SetMeasured(float, float, float)
  nameWithType.vb: DrawnView.SetMeasured(Single, Single, Single)
  fullName.vb: DrawnUi.Views.DrawnView.SetMeasured(Single, Single, Single)
  name.vb: SetMeasured(Single, Single, Single)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.SetMeasured(System.Single,System.Single,System.Single)
    name: SetMeasured
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SetMeasured_System_Single_System_Single_System_Single_
  - name: (
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: ','
  - name: " "
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: ','
  - name: " "
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.SetMeasured(System.Single,System.Single,System.Single)
    name: SetMeasured
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SetMeasured_System_Single_System_Single_System_Single_
  - name: (
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: ','
  - name: " "
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: ','
  - name: " "
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
- uid: DrawnUi.Views.DrawnView.OnMeasured
  commentId: M:DrawnUi.Views.DrawnView.OnMeasured
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnMeasured
  name: OnMeasured()
  nameWithType: DrawnView.OnMeasured()
  fullName: DrawnUi.Views.DrawnView.OnMeasured()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.OnMeasured
    name: OnMeasured
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnMeasured
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.OnMeasured
    name: OnMeasured
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnMeasured
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.Measured
  commentId: E:DrawnUi.Views.DrawnView.Measured
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Measured
  name: Measured
  nameWithType: DrawnView.Measured
  fullName: DrawnUi.Views.DrawnView.Measured
- uid: DrawnUi.Views.DrawnView.IsDisposed
  commentId: P:DrawnUi.Views.DrawnView.IsDisposed
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_IsDisposed
  name: IsDisposed
  nameWithType: DrawnView.IsDisposed
  fullName: DrawnUi.Views.DrawnView.IsDisposed
- uid: DrawnUi.Views.DrawnView.DrawingThreadId
  commentId: P:DrawnUi.Views.DrawnView.DrawingThreadId
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DrawingThreadId
  name: DrawingThreadId
  nameWithType: DrawnView.DrawingThreadId
  fullName: DrawnUi.Views.DrawnView.DrawingThreadId
- uid: DrawnUi.Views.DrawnView.WasRendered
  commentId: P:DrawnUi.Views.DrawnView.WasRendered
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_WasRendered
  name: WasRendered
  nameWithType: DrawnView.WasRendered
  fullName: DrawnUi.Views.DrawnView.WasRendered
- uid: DrawnUi.Views.DrawnView.WasDrawn
  commentId: E:DrawnUi.Views.DrawnView.WasDrawn
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_WasDrawn
  name: WasDrawn
  nameWithType: DrawnView.WasDrawn
  fullName: DrawnUi.Views.DrawnView.WasDrawn
- uid: DrawnUi.Views.DrawnView.WillDraw
  commentId: E:DrawnUi.Views.DrawnView.WillDraw
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_WillDraw
  name: WillDraw
  nameWithType: DrawnView.WillDraw
  fullName: DrawnUi.Views.DrawnView.WillDraw
- uid: DrawnUi.Views.DrawnView.WillFirstTimeDraw
  commentId: E:DrawnUi.Views.DrawnView.WillFirstTimeDraw
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_WillFirstTimeDraw
  name: WillFirstTimeDraw
  nameWithType: DrawnView.WillFirstTimeDraw
  fullName: DrawnUi.Views.DrawnView.WillFirstTimeDraw
- uid: DrawnUi.Views.DrawnView.LastDrawnRect
  commentId: F:DrawnUi.Views.DrawnView.LastDrawnRect
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_LastDrawnRect
  name: LastDrawnRect
  nameWithType: DrawnView.LastDrawnRect
  fullName: DrawnUi.Views.DrawnView.LastDrawnRect
- uid: DrawnUi.Views.DrawnView.OnStartRendering(SkiaSharp.SKCanvas)
  commentId: M:DrawnUi.Views.DrawnView.OnStartRendering(SkiaSharp.SKCanvas)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnStartRendering_SkiaSharp_SKCanvas_
  name: OnStartRendering(SKCanvas)
  nameWithType: DrawnView.OnStartRendering(SKCanvas)
  fullName: DrawnUi.Views.DrawnView.OnStartRendering(SkiaSharp.SKCanvas)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.OnStartRendering(SkiaSharp.SKCanvas)
    name: OnStartRendering
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnStartRendering_SkiaSharp_SKCanvas_
  - name: (
  - uid: SkiaSharp.SKCanvas
    name: SKCanvas
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skcanvas
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.OnStartRendering(SkiaSharp.SKCanvas)
    name: OnStartRendering
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnStartRendering_SkiaSharp_SKCanvas_
  - name: (
  - uid: SkiaSharp.SKCanvas
    name: SKCanvas
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skcanvas
  - name: )
- uid: DrawnUi.Views.DrawnView.OnFinalizeRendering
  commentId: M:DrawnUi.Views.DrawnView.OnFinalizeRendering
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnFinalizeRendering
  name: OnFinalizeRendering()
  nameWithType: DrawnView.OnFinalizeRendering()
  fullName: DrawnUi.Views.DrawnView.OnFinalizeRendering()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.OnFinalizeRendering
    name: OnFinalizeRendering
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnFinalizeRendering
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.OnFinalizeRendering
    name: OnFinalizeRendering
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnFinalizeRendering
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.AvailableDestination
  commentId: P:DrawnUi.Views.DrawnView.AvailableDestination
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_AvailableDestination
  name: AvailableDestination
  nameWithType: DrawnView.AvailableDestination
  fullName: DrawnUi.Views.DrawnView.AvailableDestination
- uid: DrawnUi.Views.DrawnView.GetOrderedSubviews
  commentId: M:DrawnUi.Views.DrawnView.GetOrderedSubviews
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GetOrderedSubviews
  name: GetOrderedSubviews()
  nameWithType: DrawnView.GetOrderedSubviews()
  fullName: DrawnUi.Views.DrawnView.GetOrderedSubviews()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.GetOrderedSubviews
    name: GetOrderedSubviews
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GetOrderedSubviews
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.GetOrderedSubviews
    name: GetOrderedSubviews
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GetOrderedSubviews
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.InvalidateViewsList
  commentId: M:DrawnUi.Views.DrawnView.InvalidateViewsList
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidateViewsList
  name: InvalidateViewsList()
  nameWithType: DrawnView.InvalidateViewsList()
  fullName: DrawnUi.Views.DrawnView.InvalidateViewsList()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.InvalidateViewsList
    name: InvalidateViewsList
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidateViewsList
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.InvalidateViewsList
    name: InvalidateViewsList
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidateViewsList
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView._fps
  commentId: F:DrawnUi.Views.DrawnView._fps
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView__fps
  name: _fps
  nameWithType: DrawnView._fps
  fullName: DrawnUi.Views.DrawnView._fps
- uid: DrawnUi.Views.DrawnView.FrameTime
  commentId: P:DrawnUi.Views.DrawnView.FrameTime
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_FrameTime
  name: FrameTime
  nameWithType: DrawnView.FrameTime
  fullName: DrawnUi.Views.DrawnView.FrameTime
- uid: DrawnUi.Views.DrawnView.CanvasFps
  commentId: P:DrawnUi.Views.DrawnView.CanvasFps
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CanvasFps
  name: CanvasFps
  nameWithType: DrawnView.CanvasFps
  fullName: DrawnUi.Views.DrawnView.CanvasFps
- uid: DrawnUi.Views.DrawnView.FPS
  commentId: P:DrawnUi.Views.DrawnView.FPS
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_FPS
  name: FPS
  nameWithType: DrawnView.FPS
  fullName: DrawnUi.Views.DrawnView.FPS
- uid: DrawnUi.Views.DrawnView.LockDraw
  commentId: F:DrawnUi.Views.DrawnView.LockDraw
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_LockDraw
  name: LockDraw
  nameWithType: DrawnView.LockDraw
  fullName: DrawnUi.Views.DrawnView.LockDraw
- uid: DrawnUi.Views.DrawnView.FrameNumber
  commentId: P:DrawnUi.Views.DrawnView.FrameNumber
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_FrameNumber
  name: FrameNumber
  nameWithType: DrawnView.FrameNumber
  fullName: DrawnUi.Views.DrawnView.FrameNumber
- uid: DrawnUi.Views.DrawnView.DisposeObject(System.IDisposable)
  commentId: M:DrawnUi.Views.DrawnView.DisposeObject(System.IDisposable)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DisposeObject_System_IDisposable_
  name: DisposeObject(IDisposable)
  nameWithType: DrawnView.DisposeObject(IDisposable)
  fullName: DrawnUi.Views.DrawnView.DisposeObject(System.IDisposable)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.DisposeObject(System.IDisposable)
    name: DisposeObject
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DisposeObject_System_IDisposable_
  - name: (
  - uid: System.IDisposable
    name: IDisposable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.DisposeObject(System.IDisposable)
    name: DisposeObject
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DisposeObject_System_IDisposable_
  - name: (
  - uid: System.IDisposable
    name: IDisposable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable
  - name: )
- uid: DrawnUi.Views.DrawnView.DisposeManager
  commentId: P:DrawnUi.Views.DrawnView.DisposeManager
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DisposeManager
  name: DisposeManager
  nameWithType: DrawnView.DisposeManager
  fullName: DrawnUi.Views.DrawnView.DisposeManager
- uid: DrawnUi.Views.DrawnView.PostponeInvalidation(DrawnUi.Draw.SkiaControl,System.Action)
  commentId: M:DrawnUi.Views.DrawnView.PostponeInvalidation(DrawnUi.Draw.SkiaControl,System.Action)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PostponeInvalidation_DrawnUi_Draw_SkiaControl_System_Action_
  name: PostponeInvalidation(SkiaControl, Action)
  nameWithType: DrawnView.PostponeInvalidation(SkiaControl, Action)
  fullName: DrawnUi.Views.DrawnView.PostponeInvalidation(DrawnUi.Draw.SkiaControl, System.Action)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.PostponeInvalidation(DrawnUi.Draw.SkiaControl,System.Action)
    name: PostponeInvalidation
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PostponeInvalidation_DrawnUi_Draw_SkiaControl_System_Action_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: ','
  - name: " "
  - uid: System.Action
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.PostponeInvalidation(DrawnUi.Draw.SkiaControl,System.Action)
    name: PostponeInvalidation
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PostponeInvalidation_DrawnUi_Draw_SkiaControl_System_Action_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: ','
  - name: " "
  - uid: System.Action
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action
  - name: )
- uid: DrawnUi.Views.DrawnView.InvalidationActionsA
  commentId: F:DrawnUi.Views.DrawnView.InvalidationActionsA
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidationActionsA
  name: InvalidationActionsA
  nameWithType: DrawnView.InvalidationActionsA
  fullName: DrawnUi.Views.DrawnView.InvalidationActionsA
- uid: DrawnUi.Views.DrawnView.InvalidationActionsB
  commentId: F:DrawnUi.Views.DrawnView.InvalidationActionsB
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidationActionsB
  name: InvalidationActionsB
  nameWithType: DrawnView.InvalidationActionsB
  fullName: DrawnUi.Views.DrawnView.InvalidationActionsB
- uid: DrawnUi.Views.DrawnView.GetFrontInvalidations
  commentId: M:DrawnUi.Views.DrawnView.GetFrontInvalidations
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GetFrontInvalidations
  name: GetFrontInvalidations()
  nameWithType: DrawnView.GetFrontInvalidations()
  fullName: DrawnUi.Views.DrawnView.GetFrontInvalidations()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.GetFrontInvalidations
    name: GetFrontInvalidations
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GetFrontInvalidations
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.GetFrontInvalidations
    name: GetFrontInvalidations
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GetFrontInvalidations
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.GetBackInvalidations
  commentId: M:DrawnUi.Views.DrawnView.GetBackInvalidations
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GetBackInvalidations
  name: GetBackInvalidations()
  nameWithType: DrawnView.GetBackInvalidations()
  fullName: DrawnUi.Views.DrawnView.GetBackInvalidations()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.GetBackInvalidations
    name: GetBackInvalidations
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GetBackInvalidations
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.GetBackInvalidations
    name: GetBackInvalidations
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GetBackInvalidations
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.SwapInvalidations
  commentId: M:DrawnUi.Views.DrawnView.SwapInvalidations
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SwapInvalidations
  name: SwapInvalidations()
  nameWithType: DrawnView.SwapInvalidations()
  fullName: DrawnUi.Views.DrawnView.SwapInvalidations()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.SwapInvalidations
    name: SwapInvalidations
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SwapInvalidations
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.SwapInvalidations
    name: SwapInvalidations
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SwapInvalidations
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.DrawingThreads
  commentId: P:DrawnUi.Views.DrawnView.DrawingThreads
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DrawingThreads
  name: DrawingThreads
  nameWithType: DrawnView.DrawingThreads
  fullName: DrawnUi.Views.DrawnView.DrawingThreads
- uid: DrawnUi.Views.DrawnView.DirtyChildrenTracker
  commentId: F:DrawnUi.Views.DrawnView.DirtyChildrenTracker
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_DirtyChildrenTracker
  name: DirtyChildrenTracker
  nameWithType: DrawnView.DirtyChildrenTracker
  fullName: DrawnUi.Views.DrawnView.DirtyChildrenTracker
- uid: DrawnUi.Views.DrawnView.SetChildAsDirty(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Views.DrawnView.SetChildAsDirty(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SetChildAsDirty_DrawnUi_Draw_SkiaControl_
  name: SetChildAsDirty(SkiaControl)
  nameWithType: DrawnView.SetChildAsDirty(SkiaControl)
  fullName: DrawnUi.Views.DrawnView.SetChildAsDirty(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.SetChildAsDirty(DrawnUi.Draw.SkiaControl)
    name: SetChildAsDirty
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SetChildAsDirty_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.SetChildAsDirty(DrawnUi.Draw.SkiaControl)
    name: SetChildAsDirty
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SetChildAsDirty_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: DrawnUi.Views.DrawnView.CommitInvalidations
  commentId: M:DrawnUi.Views.DrawnView.CommitInvalidations
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CommitInvalidations
  name: CommitInvalidations()
  nameWithType: DrawnView.CommitInvalidations()
  fullName: DrawnUi.Views.DrawnView.CommitInvalidations()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.CommitInvalidations
    name: CommitInvalidations
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CommitInvalidations
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.CommitInvalidations
    name: CommitInvalidations
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CommitInvalidations
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.LockStartOffscreenQueue
  commentId: F:DrawnUi.Views.DrawnView.LockStartOffscreenQueue
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_LockStartOffscreenQueue
  name: LockStartOffscreenQueue
  nameWithType: DrawnView.LockStartOffscreenQueue
  fullName: DrawnUi.Views.DrawnView.LockStartOffscreenQueue
- uid: DrawnUi.Views.DrawnView.semaphoreOffscreenProcess
  commentId: F:DrawnUi.Views.DrawnView.semaphoreOffscreenProcess
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_semaphoreOffscreenProcess
  name: semaphoreOffscreenProcess
  nameWithType: DrawnView.semaphoreOffscreenProcess
  fullName: DrawnUi.Views.DrawnView.semaphoreOffscreenProcess
- uid: DrawnUi.Views.DrawnView.KickOffscreenCacheRendering
  commentId: M:DrawnUi.Views.DrawnView.KickOffscreenCacheRendering
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_KickOffscreenCacheRendering
  name: KickOffscreenCacheRendering()
  nameWithType: DrawnView.KickOffscreenCacheRendering()
  fullName: DrawnUi.Views.DrawnView.KickOffscreenCacheRendering()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.KickOffscreenCacheRendering
    name: KickOffscreenCacheRendering
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_KickOffscreenCacheRendering
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.KickOffscreenCacheRendering
    name: KickOffscreenCacheRendering
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_KickOffscreenCacheRendering
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.PushToOffscreenRendering(DrawnUi.Draw.SkiaControl,System.Threading.CancellationToken)
  commentId: M:DrawnUi.Views.DrawnView.PushToOffscreenRendering(DrawnUi.Draw.SkiaControl,System.Threading.CancellationToken)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PushToOffscreenRendering_DrawnUi_Draw_SkiaControl_System_Threading_CancellationToken_
  name: PushToOffscreenRendering(SkiaControl, CancellationToken)
  nameWithType: DrawnView.PushToOffscreenRendering(SkiaControl, CancellationToken)
  fullName: DrawnUi.Views.DrawnView.PushToOffscreenRendering(DrawnUi.Draw.SkiaControl, System.Threading.CancellationToken)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.PushToOffscreenRendering(DrawnUi.Draw.SkiaControl,System.Threading.CancellationToken)
    name: PushToOffscreenRendering
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PushToOffscreenRendering_DrawnUi_Draw_SkiaControl_System_Threading_CancellationToken_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: ','
  - name: " "
  - uid: System.Threading.CancellationToken
    name: CancellationToken
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.PushToOffscreenRendering(DrawnUi.Draw.SkiaControl,System.Threading.CancellationToken)
    name: PushToOffscreenRendering
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PushToOffscreenRendering_DrawnUi_Draw_SkiaControl_System_Threading_CancellationToken_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: ','
  - name: " "
  - uid: System.Threading.CancellationToken
    name: CancellationToken
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken
  - name: )
- uid: DrawnUi.Views.DrawnView.ProcessOffscreenCacheRenderingAsync
  commentId: M:DrawnUi.Views.DrawnView.ProcessOffscreenCacheRenderingAsync
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ProcessOffscreenCacheRenderingAsync
  name: ProcessOffscreenCacheRenderingAsync()
  nameWithType: DrawnView.ProcessOffscreenCacheRenderingAsync()
  fullName: DrawnUi.Views.DrawnView.ProcessOffscreenCacheRenderingAsync()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.ProcessOffscreenCacheRenderingAsync
    name: ProcessOffscreenCacheRenderingAsync
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ProcessOffscreenCacheRenderingAsync
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.ProcessOffscreenCacheRenderingAsync
    name: ProcessOffscreenCacheRenderingAsync
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ProcessOffscreenCacheRenderingAsync
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.RenderingScaleProperty
  commentId: F:DrawnUi.Views.DrawnView.RenderingScaleProperty
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RenderingScaleProperty
  name: RenderingScaleProperty
  nameWithType: DrawnView.RenderingScaleProperty
  fullName: DrawnUi.Views.DrawnView.RenderingScaleProperty
- uid: DrawnUi.Views.DrawnView.OnDensityChanged
  commentId: M:DrawnUi.Views.DrawnView.OnDensityChanged
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnDensityChanged
  name: OnDensityChanged()
  nameWithType: DrawnView.OnDensityChanged()
  fullName: DrawnUi.Views.DrawnView.OnDensityChanged()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.OnDensityChanged
    name: OnDensityChanged
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnDensityChanged
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.OnDensityChanged
    name: OnDensityChanged
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnDensityChanged
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.RenderingScale
  commentId: P:DrawnUi.Views.DrawnView.RenderingScale
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RenderingScale
  name: RenderingScale
  nameWithType: DrawnView.RenderingScale
  fullName: DrawnUi.Views.DrawnView.RenderingScale
- uid: DrawnUi.Views.DrawnView.RenderingModeProperty
  commentId: F:DrawnUi.Views.DrawnView.RenderingModeProperty
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RenderingModeProperty
  name: RenderingModeProperty
  nameWithType: DrawnView.RenderingModeProperty
  fullName: DrawnUi.Views.DrawnView.RenderingModeProperty
- uid: DrawnUi.Views.DrawnView.RenderingMode
  commentId: P:DrawnUi.Views.DrawnView.RenderingMode
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RenderingMode
  name: RenderingMode
  nameWithType: DrawnView.RenderingMode
  fullName: DrawnUi.Views.DrawnView.RenderingMode
- uid: DrawnUi.Views.DrawnView.CanRenderOffScreenProperty
  commentId: F:DrawnUi.Views.DrawnView.CanRenderOffScreenProperty
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CanRenderOffScreenProperty
  name: CanRenderOffScreenProperty
  nameWithType: DrawnView.CanRenderOffScreenProperty
  fullName: DrawnUi.Views.DrawnView.CanRenderOffScreenProperty
- uid: DrawnUi.Views.DrawnView.CanRenderOffScreen
  commentId: P:DrawnUi.Views.DrawnView.CanRenderOffScreen
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CanRenderOffScreen
  name: CanRenderOffScreen
  nameWithType: DrawnView.CanRenderOffScreen
  fullName: DrawnUi.Views.DrawnView.CanRenderOffScreen
- uid: DrawnUi.Views.DrawnView.CanDraw
  commentId: P:DrawnUi.Views.DrawnView.CanDraw
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CanDraw
  name: CanDraw
  nameWithType: DrawnView.CanDraw
  fullName: DrawnUi.Views.DrawnView.CanDraw
- uid: DrawnUi.Views.DrawnView.IsHiddenInViewTree
  commentId: P:DrawnUi.Views.DrawnView.IsHiddenInViewTree
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_IsHiddenInViewTree
  name: IsHiddenInViewTree
  nameWithType: DrawnView.IsHiddenInViewTree
  fullName: DrawnUi.Views.DrawnView.IsHiddenInViewTree
- uid: DrawnUi.Views.DrawnView.NeedCheckParentVisibility
  commentId: P:DrawnUi.Views.DrawnView.NeedCheckParentVisibility
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_NeedCheckParentVisibility
  name: NeedCheckParentVisibility
  nameWithType: DrawnView.NeedCheckParentVisibility
  fullName: DrawnUi.Views.DrawnView.NeedCheckParentVisibility
- uid: DrawnUi.Views.DrawnView.StreamFromString(System.String)
  commentId: M:DrawnUi.Views.DrawnView.StreamFromString(System.String)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_StreamFromString_System_String_
  name: StreamFromString(string)
  nameWithType: DrawnView.StreamFromString(string)
  fullName: DrawnUi.Views.DrawnView.StreamFromString(string)
  nameWithType.vb: DrawnView.StreamFromString(String)
  fullName.vb: DrawnUi.Views.DrawnView.StreamFromString(String)
  name.vb: StreamFromString(String)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.StreamFromString(System.String)
    name: StreamFromString
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_StreamFromString_System_String_
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.StreamFromString(System.String)
    name: StreamFromString
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_StreamFromString_System_String_
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: DrawnUi.Views.DrawnView.PaintSystem
  commentId: P:DrawnUi.Views.DrawnView.PaintSystem
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PaintSystem
  name: PaintSystem
  nameWithType: DrawnView.PaintSystem
  fullName: DrawnUi.Views.DrawnView.PaintSystem
- uid: DrawnUi.Views.DrawnView.Destination
  commentId: P:DrawnUi.Views.DrawnView.Destination
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Destination
  name: Destination
  nameWithType: DrawnView.Destination
  fullName: DrawnUi.Views.DrawnView.Destination
- uid: DrawnUi.Views.DrawnView.PaintTintBackground(SkiaSharp.SKCanvas)
  commentId: M:DrawnUi.Views.DrawnView.PaintTintBackground(SkiaSharp.SKCanvas)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PaintTintBackground_SkiaSharp_SKCanvas_
  name: PaintTintBackground(SKCanvas)
  nameWithType: DrawnView.PaintTintBackground(SKCanvas)
  fullName: DrawnUi.Views.DrawnView.PaintTintBackground(SkiaSharp.SKCanvas)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.PaintTintBackground(SkiaSharp.SKCanvas)
    name: PaintTintBackground
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PaintTintBackground_SkiaSharp_SKCanvas_
  - name: (
  - uid: SkiaSharp.SKCanvas
    name: SKCanvas
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skcanvas
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.PaintTintBackground(SkiaSharp.SKCanvas)
    name: PaintTintBackground
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_PaintTintBackground_SkiaSharp_SKCanvas_
  - name: (
  - uid: SkiaSharp.SKCanvas
    name: SKCanvas
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skcanvas
  - name: )
- uid: DrawnUi.Views.DrawnView.RedrawCanvas(Microsoft.Maui.Controls.BindableObject,System.Object,System.Object)
  commentId: M:DrawnUi.Views.DrawnView.RedrawCanvas(Microsoft.Maui.Controls.BindableObject,System.Object,System.Object)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RedrawCanvas_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_
  name: RedrawCanvas(BindableObject, object, object)
  nameWithType: DrawnView.RedrawCanvas(BindableObject, object, object)
  fullName: DrawnUi.Views.DrawnView.RedrawCanvas(Microsoft.Maui.Controls.BindableObject, object, object)
  nameWithType.vb: DrawnView.RedrawCanvas(BindableObject, Object, Object)
  fullName.vb: DrawnUi.Views.DrawnView.RedrawCanvas(Microsoft.Maui.Controls.BindableObject, Object, Object)
  name.vb: RedrawCanvas(BindableObject, Object, Object)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.RedrawCanvas(Microsoft.Maui.Controls.BindableObject,System.Object,System.Object)
    name: RedrawCanvas
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RedrawCanvas_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_
  - name: (
  - uid: Microsoft.Maui.Controls.BindableObject
    name: BindableObject
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.RedrawCanvas(Microsoft.Maui.Controls.BindableObject,System.Object,System.Object)
    name: RedrawCanvas
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RedrawCanvas_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_
  - name: (
  - uid: Microsoft.Maui.Controls.BindableObject
    name: BindableObject
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Views.DrawnView.OnBindingContextChanged
  commentId: M:DrawnUi.Views.DrawnView.OnBindingContextChanged
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnBindingContextChanged
  name: OnBindingContextChanged()
  nameWithType: DrawnView.OnBindingContextChanged()
  fullName: DrawnUi.Views.DrawnView.OnBindingContextChanged()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.OnBindingContextChanged
    name: OnBindingContextChanged
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnBindingContextChanged
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.OnBindingContextChanged
    name: OnBindingContextChanged
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnBindingContextChanged
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.Views
  commentId: P:DrawnUi.Views.DrawnView.Views
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Views
  name: Views
  nameWithType: DrawnView.Views
  fullName: DrawnUi.Views.DrawnView.Views
- uid: DrawnUi.Views.DrawnView.ClearChildren
  commentId: M:DrawnUi.Views.DrawnView.ClearChildren
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ClearChildren
  name: ClearChildren()
  nameWithType: DrawnView.ClearChildren()
  fullName: DrawnUi.Views.DrawnView.ClearChildren()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.ClearChildren
    name: ClearChildren
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ClearChildren
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.ClearChildren
    name: ClearChildren
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ClearChildren
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.AddSubView(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Views.DrawnView.AddSubView(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_AddSubView_DrawnUi_Draw_SkiaControl_
  name: AddSubView(SkiaControl)
  nameWithType: DrawnView.AddSubView(SkiaControl)
  fullName: DrawnUi.Views.DrawnView.AddSubView(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.AddSubView(DrawnUi.Draw.SkiaControl)
    name: AddSubView
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_AddSubView_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.AddSubView(DrawnUi.Draw.SkiaControl)
    name: AddSubView
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_AddSubView_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: DrawnUi.Views.DrawnView.ReportHotreloadChildAdded(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Views.DrawnView.ReportHotreloadChildAdded(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ReportHotreloadChildAdded_DrawnUi_Draw_SkiaControl_
  name: ReportHotreloadChildAdded(SkiaControl)
  nameWithType: DrawnView.ReportHotreloadChildAdded(SkiaControl)
  fullName: DrawnUi.Views.DrawnView.ReportHotreloadChildAdded(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.ReportHotreloadChildAdded(DrawnUi.Draw.SkiaControl)
    name: ReportHotreloadChildAdded
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ReportHotreloadChildAdded_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.ReportHotreloadChildAdded(DrawnUi.Draw.SkiaControl)
    name: ReportHotreloadChildAdded
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ReportHotreloadChildAdded_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: DrawnUi.Views.DrawnView.RemoveSubView(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Views.DrawnView.RemoveSubView(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RemoveSubView_DrawnUi_Draw_SkiaControl_
  name: RemoveSubView(SkiaControl)
  nameWithType: DrawnView.RemoveSubView(SkiaControl)
  fullName: DrawnUi.Views.DrawnView.RemoveSubView(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.RemoveSubView(DrawnUi.Draw.SkiaControl)
    name: RemoveSubView
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RemoveSubView_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.RemoveSubView(DrawnUi.Draw.SkiaControl)
    name: RemoveSubView
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_RemoveSubView_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: DrawnUi.Views.DrawnView.ReportHotreloadChildRemoved(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Views.DrawnView.ReportHotreloadChildRemoved(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ReportHotreloadChildRemoved_DrawnUi_Draw_SkiaControl_
  name: ReportHotreloadChildRemoved(SkiaControl)
  nameWithType: DrawnView.ReportHotreloadChildRemoved(SkiaControl)
  fullName: DrawnUi.Views.DrawnView.ReportHotreloadChildRemoved(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.ReportHotreloadChildRemoved(DrawnUi.Draw.SkiaControl)
    name: ReportHotreloadChildRemoved
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ReportHotreloadChildRemoved_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.ReportHotreloadChildRemoved(DrawnUi.Draw.SkiaControl)
    name: ReportHotreloadChildRemoved
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ReportHotreloadChildRemoved_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: DrawnUi.Views.DrawnView.OnChildRemoved(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Views.DrawnView.OnChildRemoved(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnChildRemoved_DrawnUi_Draw_SkiaControl_
  name: OnChildRemoved(SkiaControl)
  nameWithType: DrawnView.OnChildRemoved(SkiaControl)
  fullName: DrawnUi.Views.DrawnView.OnChildRemoved(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.OnChildRemoved(DrawnUi.Draw.SkiaControl)
    name: OnChildRemoved
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnChildRemoved_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.OnChildRemoved(DrawnUi.Draw.SkiaControl)
    name: OnChildRemoved
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnChildRemoved_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: DrawnUi.Views.DrawnView.ChildrenProperty
  commentId: F:DrawnUi.Views.DrawnView.ChildrenProperty
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ChildrenProperty
  name: ChildrenProperty
  nameWithType: DrawnView.ChildrenProperty
  fullName: DrawnUi.Views.DrawnView.ChildrenProperty
- uid: DrawnUi.Views.DrawnView.Children
  commentId: P:DrawnUi.Views.DrawnView.Children
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Children
  name: Children
  nameWithType: DrawnView.Children
  fullName: DrawnUi.Views.DrawnView.Children
- uid: DrawnUi.Views.DrawnView.AddOrRemoveView(DrawnUi.Draw.SkiaControl,System.Boolean)
  commentId: M:DrawnUi.Views.DrawnView.AddOrRemoveView(DrawnUi.Draw.SkiaControl,System.Boolean)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_AddOrRemoveView_DrawnUi_Draw_SkiaControl_System_Boolean_
  name: AddOrRemoveView(SkiaControl, bool)
  nameWithType: DrawnView.AddOrRemoveView(SkiaControl, bool)
  fullName: DrawnUi.Views.DrawnView.AddOrRemoveView(DrawnUi.Draw.SkiaControl, bool)
  nameWithType.vb: DrawnView.AddOrRemoveView(SkiaControl, Boolean)
  fullName.vb: DrawnUi.Views.DrawnView.AddOrRemoveView(DrawnUi.Draw.SkiaControl, Boolean)
  name.vb: AddOrRemoveView(SkiaControl, Boolean)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.AddOrRemoveView(DrawnUi.Draw.SkiaControl,System.Boolean)
    name: AddOrRemoveView
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_AddOrRemoveView_DrawnUi_Draw_SkiaControl_System_Boolean_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.AddOrRemoveView(DrawnUi.Draw.SkiaControl,System.Boolean)
    name: AddOrRemoveView
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_AddOrRemoveView_DrawnUi_Draw_SkiaControl_System_Boolean_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: DrawnUi.Views.DrawnView.UpdateModeProperty
  commentId: F:DrawnUi.Views.DrawnView.UpdateModeProperty
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UpdateModeProperty
  name: UpdateModeProperty
  nameWithType: DrawnView.UpdateModeProperty
  fullName: DrawnUi.Views.DrawnView.UpdateModeProperty
- uid: DrawnUi.Views.DrawnView.UpdateMode
  commentId: P:DrawnUi.Views.DrawnView.UpdateMode
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_UpdateMode
  name: UpdateMode
  nameWithType: DrawnView.UpdateMode
  fullName: DrawnUi.Views.DrawnView.UpdateMode
- uid: DrawnUi.Views.DrawnView.ClipEffectsProperty
  commentId: F:DrawnUi.Views.DrawnView.ClipEffectsProperty
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ClipEffectsProperty
  name: ClipEffectsProperty
  nameWithType: DrawnView.ClipEffectsProperty
  fullName: DrawnUi.Views.DrawnView.ClipEffectsProperty
- uid: DrawnUi.Views.DrawnView.ClipEffects
  commentId: P:DrawnUi.Views.DrawnView.ClipEffects
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ClipEffects
  name: ClipEffects
  nameWithType: DrawnView.ClipEffects
  fullName: DrawnUi.Views.DrawnView.ClipEffects
- uid: DrawnUi.Views.DrawnView.Value1Property
  commentId: F:DrawnUi.Views.DrawnView.Value1Property
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Value1Property
  name: Value1Property
  nameWithType: DrawnView.Value1Property
  fullName: DrawnUi.Views.DrawnView.Value1Property
- uid: DrawnUi.Views.DrawnView.Value1
  commentId: P:DrawnUi.Views.DrawnView.Value1
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Value1
  name: Value1
  nameWithType: DrawnView.Value1
  fullName: DrawnUi.Views.DrawnView.Value1
- uid: DrawnUi.Views.DrawnView.Value2Property
  commentId: F:DrawnUi.Views.DrawnView.Value2Property
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Value2Property
  name: Value2Property
  nameWithType: DrawnView.Value2Property
  fullName: DrawnUi.Views.DrawnView.Value2Property
- uid: DrawnUi.Views.DrawnView.Value2
  commentId: P:DrawnUi.Views.DrawnView.Value2
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Value2
  name: Value2
  nameWithType: DrawnView.Value2
  fullName: DrawnUi.Views.DrawnView.Value2
- uid: DrawnUi.Views.DrawnView.Value3Property
  commentId: F:DrawnUi.Views.DrawnView.Value3Property
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Value3Property
  name: Value3Property
  nameWithType: DrawnView.Value3Property
  fullName: DrawnUi.Views.DrawnView.Value3Property
- uid: DrawnUi.Views.DrawnView.Value3
  commentId: P:DrawnUi.Views.DrawnView.Value3
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Value3
  name: Value3
  nameWithType: DrawnView.Value3
  fullName: DrawnUi.Views.DrawnView.Value3
- uid: DrawnUi.Views.DrawnView.Value4Property
  commentId: F:DrawnUi.Views.DrawnView.Value4Property
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Value4Property
  name: Value4Property
  nameWithType: DrawnView.Value4Property
  fullName: DrawnUi.Views.DrawnView.Value4Property
- uid: DrawnUi.Views.DrawnView.Value4
  commentId: P:DrawnUi.Views.DrawnView.Value4
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Value4
  name: Value4
  nameWithType: DrawnView.Value4
  fullName: DrawnUi.Views.DrawnView.Value4
- uid: DrawnUi.Views.DrawnView.FocusLocked
  commentId: P:DrawnUi.Views.DrawnView.FocusLocked
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_FocusLocked
  name: FocusLocked
  nameWithType: DrawnView.FocusLocked
  fullName: DrawnUi.Views.DrawnView.FocusLocked
- uid: DrawnUi.Views.DrawnView.FocusedItemChanged
  commentId: E:DrawnUi.Views.DrawnView.FocusedItemChanged
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_FocusedItemChanged
  name: FocusedItemChanged
  nameWithType: DrawnView.FocusedItemChanged
  fullName: DrawnUi.Views.DrawnView.FocusedItemChanged
- uid: DrawnUi.Views.DrawnView.ReportFocus(DrawnUi.Draw.ISkiaGestureListener,DrawnUi.Draw.ISkiaGestureListener)
  commentId: M:DrawnUi.Views.DrawnView.ReportFocus(DrawnUi.Draw.ISkiaGestureListener,DrawnUi.Draw.ISkiaGestureListener)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ReportFocus_DrawnUi_Draw_ISkiaGestureListener_DrawnUi_Draw_ISkiaGestureListener_
  name: ReportFocus(ISkiaGestureListener, ISkiaGestureListener)
  nameWithType: DrawnView.ReportFocus(ISkiaGestureListener, ISkiaGestureListener)
  fullName: DrawnUi.Views.DrawnView.ReportFocus(DrawnUi.Draw.ISkiaGestureListener, DrawnUi.Draw.ISkiaGestureListener)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.ReportFocus(DrawnUi.Draw.ISkiaGestureListener,DrawnUi.Draw.ISkiaGestureListener)
    name: ReportFocus
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ReportFocus_DrawnUi_Draw_ISkiaGestureListener_DrawnUi_Draw_ISkiaGestureListener_
  - name: (
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.ReportFocus(DrawnUi.Draw.ISkiaGestureListener,DrawnUi.Draw.ISkiaGestureListener)
    name: ReportFocus
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ReportFocus_DrawnUi_Draw_ISkiaGestureListener_DrawnUi_Draw_ISkiaGestureListener_
  - name: (
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: )
- uid: DrawnUi.Views.DrawnView.ResetFocusWithDelay(System.Int32)
  commentId: M:DrawnUi.Views.DrawnView.ResetFocusWithDelay(System.Int32)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ResetFocusWithDelay_System_Int32_
  name: ResetFocusWithDelay(int)
  nameWithType: DrawnView.ResetFocusWithDelay(int)
  fullName: DrawnUi.Views.DrawnView.ResetFocusWithDelay(int)
  nameWithType.vb: DrawnView.ResetFocusWithDelay(Integer)
  fullName.vb: DrawnUi.Views.DrawnView.ResetFocusWithDelay(Integer)
  name.vb: ResetFocusWithDelay(Integer)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.ResetFocusWithDelay(System.Int32)
    name: ResetFocusWithDelay
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ResetFocusWithDelay_System_Int32_
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.ResetFocusWithDelay(System.Int32)
    name: ResetFocusWithDelay
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ResetFocusWithDelay_System_Int32_
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: DrawnUi.Views.DrawnView.FocusedChild
  commentId: P:DrawnUi.Views.DrawnView.FocusedChild
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_FocusedChild
  name: FocusedChild
  nameWithType: DrawnView.FocusedChild
  fullName: DrawnUi.Views.DrawnView.FocusedChild
- uid: DrawnUi.Views.DrawnView.InvalidateCanvas
  commentId: M:DrawnUi.Views.DrawnView.InvalidateCanvas
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidateCanvas
  name: InvalidateCanvas()
  nameWithType: DrawnView.InvalidateCanvas()
  fullName: DrawnUi.Views.DrawnView.InvalidateCanvas()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.InvalidateCanvas
    name: InvalidateCanvas
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidateCanvas
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.InvalidateCanvas
    name: InvalidateCanvas
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InvalidateCanvas
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.OnParentSet
  commentId: M:DrawnUi.Views.DrawnView.OnParentSet
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnParentSet
  name: OnParentSet()
  nameWithType: DrawnView.OnParentSet()
  fullName: DrawnUi.Views.DrawnView.OnParentSet()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.OnParentSet
    name: OnParentSet
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnParentSet
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.OnParentSet
    name: OnParentSet
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnParentSet
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.GetIsVisibleWithParent(Microsoft.Maui.Controls.VisualElement)
  commentId: M:DrawnUi.Views.DrawnView.GetIsVisibleWithParent(Microsoft.Maui.Controls.VisualElement)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GetIsVisibleWithParent_Microsoft_Maui_Controls_VisualElement_
  name: GetIsVisibleWithParent(VisualElement)
  nameWithType: DrawnView.GetIsVisibleWithParent(VisualElement)
  fullName: DrawnUi.Views.DrawnView.GetIsVisibleWithParent(Microsoft.Maui.Controls.VisualElement)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.GetIsVisibleWithParent(Microsoft.Maui.Controls.VisualElement)
    name: GetIsVisibleWithParent
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GetIsVisibleWithParent_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.GetIsVisibleWithParent(Microsoft.Maui.Controls.VisualElement)
    name: GetIsVisibleWithParent
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_GetIsVisibleWithParent_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
- uid: DrawnUi.Views.DrawnView.CheckElementVisibility(Microsoft.Maui.Controls.VisualElement)
  commentId: M:DrawnUi.Views.DrawnView.CheckElementVisibility(Microsoft.Maui.Controls.VisualElement)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CheckElementVisibility_Microsoft_Maui_Controls_VisualElement_
  name: CheckElementVisibility(VisualElement)
  nameWithType: DrawnView.CheckElementVisibility(VisualElement)
  fullName: DrawnUi.Views.DrawnView.CheckElementVisibility(Microsoft.Maui.Controls.VisualElement)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.CheckElementVisibility(Microsoft.Maui.Controls.VisualElement)
    name: CheckElementVisibility
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CheckElementVisibility_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.CheckElementVisibility(Microsoft.Maui.Controls.VisualElement)
    name: CheckElementVisibility
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_CheckElementVisibility_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
- uid: DrawnUi.Views.DrawnView.ClipSmart(SkiaSharp.SKCanvas,SkiaSharp.SKPath,SkiaSharp.SKClipOperation)
  commentId: M:DrawnUi.Views.DrawnView.ClipSmart(SkiaSharp.SKCanvas,SkiaSharp.SKPath,SkiaSharp.SKClipOperation)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ClipSmart_SkiaSharp_SKCanvas_SkiaSharp_SKPath_SkiaSharp_SKClipOperation_
  name: ClipSmart(SKCanvas, SKPath, SKClipOperation)
  nameWithType: DrawnView.ClipSmart(SKCanvas, SKPath, SKClipOperation)
  fullName: DrawnUi.Views.DrawnView.ClipSmart(SkiaSharp.SKCanvas, SkiaSharp.SKPath, SkiaSharp.SKClipOperation)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.ClipSmart(SkiaSharp.SKCanvas,SkiaSharp.SKPath,SkiaSharp.SKClipOperation)
    name: ClipSmart
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ClipSmart_SkiaSharp_SKCanvas_SkiaSharp_SKPath_SkiaSharp_SKClipOperation_
  - name: (
  - uid: SkiaSharp.SKCanvas
    name: SKCanvas
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skcanvas
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKPath
    name: SKPath
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skpath
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKClipOperation
    name: SKClipOperation
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skclipoperation
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.ClipSmart(SkiaSharp.SKCanvas,SkiaSharp.SKPath,SkiaSharp.SKClipOperation)
    name: ClipSmart
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_ClipSmart_SkiaSharp_SKCanvas_SkiaSharp_SKPath_SkiaSharp_SKClipOperation_
  - name: (
  - uid: SkiaSharp.SKCanvas
    name: SKCanvas
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skcanvas
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKPath
    name: SKPath
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skpath
  - name: ','
  - name: " "
  - uid: SkiaSharp.SKClipOperation
    name: SKClipOperation
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skclipoperation
  - name: )
- uid: DrawnUi.Views.DrawnView.OnHotReload
  commentId: M:DrawnUi.Views.DrawnView.OnHotReload
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnHotReload
  name: OnHotReload()
  nameWithType: DrawnView.OnHotReload()
  fullName: DrawnUi.Views.DrawnView.OnHotReload()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.OnHotReload
    name: OnHotReload
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnHotReload
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.OnHotReload
    name: OnHotReload
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnHotReload
  - name: (
  - name: )
- uid: DrawnUi.Views.DrawnView.InitFramework(System.Boolean)
  commentId: M:DrawnUi.Views.DrawnView.InitFramework(System.Boolean)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InitFramework_System_Boolean_
  name: InitFramework(bool)
  nameWithType: DrawnView.InitFramework(bool)
  fullName: DrawnUi.Views.DrawnView.InitFramework(bool)
  nameWithType.vb: DrawnView.InitFramework(Boolean)
  fullName.vb: DrawnUi.Views.DrawnView.InitFramework(Boolean)
  name.vb: InitFramework(Boolean)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.InitFramework(System.Boolean)
    name: InitFramework
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InitFramework_System_Boolean_
  - name: (
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.InitFramework(System.Boolean)
    name: InitFramework
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_InitFramework_System_Boolean_
  - name: (
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: Microsoft.Maui.Controls.TemplatedView.ControlTemplateProperty
  commentId: F:Microsoft.Maui.Controls.TemplatedView.ControlTemplateProperty
  parent: Microsoft.Maui.Controls.TemplatedView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.controltemplateproperty
  name: ControlTemplateProperty
  nameWithType: TemplatedView.ControlTemplateProperty
  fullName: Microsoft.Maui.Controls.TemplatedView.ControlTemplateProperty
- uid: Microsoft.Maui.Controls.TemplatedView.LayoutChildren(System.Double,System.Double,System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.TemplatedView.LayoutChildren(System.Double,System.Double,System.Double,System.Double)
  parent: Microsoft.Maui.Controls.TemplatedView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.layoutchildren
  name: LayoutChildren(double, double, double, double)
  nameWithType: TemplatedView.LayoutChildren(double, double, double, double)
  fullName: Microsoft.Maui.Controls.TemplatedView.LayoutChildren(double, double, double, double)
  nameWithType.vb: TemplatedView.LayoutChildren(Double, Double, Double, Double)
  fullName.vb: Microsoft.Maui.Controls.TemplatedView.LayoutChildren(Double, Double, Double, Double)
  name.vb: LayoutChildren(Double, Double, Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.TemplatedView.LayoutChildren(System.Double,System.Double,System.Double,System.Double)
    name: LayoutChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.layoutchildren
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.TemplatedView.LayoutChildren(System.Double,System.Double,System.Double,System.Double)
    name: LayoutChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.layoutchildren
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.TemplatedView.OnMeasure(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.TemplatedView.OnMeasure(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.TemplatedView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.onmeasure
  name: OnMeasure(double, double)
  nameWithType: TemplatedView.OnMeasure(double, double)
  fullName: Microsoft.Maui.Controls.TemplatedView.OnMeasure(double, double)
  nameWithType.vb: TemplatedView.OnMeasure(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.TemplatedView.OnMeasure(Double, Double)
  name.vb: OnMeasure(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.TemplatedView.OnMeasure(System.Double,System.Double)
    name: OnMeasure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.onmeasure
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.TemplatedView.OnMeasure(System.Double,System.Double)
    name: OnMeasure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.onmeasure
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.TemplatedView.OnApplyTemplate
  commentId: M:Microsoft.Maui.Controls.TemplatedView.OnApplyTemplate
  parent: Microsoft.Maui.Controls.TemplatedView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.onapplytemplate
  name: OnApplyTemplate()
  nameWithType: TemplatedView.OnApplyTemplate()
  fullName: Microsoft.Maui.Controls.TemplatedView.OnApplyTemplate()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.TemplatedView.OnApplyTemplate
    name: OnApplyTemplate
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.onapplytemplate
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.TemplatedView.OnApplyTemplate
    name: OnApplyTemplate
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.onapplytemplate
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.TemplatedView.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
  commentId: M:Microsoft.Maui.Controls.TemplatedView.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
  parent: Microsoft.Maui.Controls.TemplatedView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.onchildremoved
  name: OnChildRemoved(Element, int)
  nameWithType: TemplatedView.OnChildRemoved(Element, int)
  fullName: Microsoft.Maui.Controls.TemplatedView.OnChildRemoved(Microsoft.Maui.Controls.Element, int)
  nameWithType.vb: TemplatedView.OnChildRemoved(Element, Integer)
  fullName.vb: Microsoft.Maui.Controls.TemplatedView.OnChildRemoved(Microsoft.Maui.Controls.Element, Integer)
  name.vb: OnChildRemoved(Element, Integer)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.TemplatedView.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
    name: OnChildRemoved
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.onchildremoved
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.TemplatedView.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
    name: OnChildRemoved
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.onchildremoved
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: Microsoft.Maui.Controls.TemplatedView.GetTemplateChild(System.String)
  commentId: M:Microsoft.Maui.Controls.TemplatedView.GetTemplateChild(System.String)
  parent: Microsoft.Maui.Controls.TemplatedView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.gettemplatechild
  name: GetTemplateChild(string)
  nameWithType: TemplatedView.GetTemplateChild(string)
  fullName: Microsoft.Maui.Controls.TemplatedView.GetTemplateChild(string)
  nameWithType.vb: TemplatedView.GetTemplateChild(String)
  fullName.vb: Microsoft.Maui.Controls.TemplatedView.GetTemplateChild(String)
  name.vb: GetTemplateChild(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.TemplatedView.GetTemplateChild(System.String)
    name: GetTemplateChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.gettemplatechild
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.TemplatedView.GetTemplateChild(System.String)
    name: GetTemplateChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.gettemplatechild
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.TemplatedView.ResolveControlTemplate
  commentId: M:Microsoft.Maui.Controls.TemplatedView.ResolveControlTemplate
  parent: Microsoft.Maui.Controls.TemplatedView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.resolvecontroltemplate
  name: ResolveControlTemplate()
  nameWithType: TemplatedView.ResolveControlTemplate()
  fullName: Microsoft.Maui.Controls.TemplatedView.ResolveControlTemplate()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.TemplatedView.ResolveControlTemplate
    name: ResolveControlTemplate
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.resolvecontroltemplate
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.TemplatedView.ResolveControlTemplate
    name: ResolveControlTemplate
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.resolvecontroltemplate
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.TemplatedView.ControlTemplate
  commentId: P:Microsoft.Maui.Controls.TemplatedView.ControlTemplate
  parent: Microsoft.Maui.Controls.TemplatedView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.controltemplate
  name: ControlTemplate
  nameWithType: TemplatedView.ControlTemplate
  fullName: Microsoft.Maui.Controls.TemplatedView.ControlTemplate
- uid: Microsoft.Maui.Controls.Compatibility.Layout.IsClippedToBoundsProperty
  commentId: F:Microsoft.Maui.Controls.Compatibility.Layout.IsClippedToBoundsProperty
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.isclippedtoboundsproperty
  name: IsClippedToBoundsProperty
  nameWithType: Layout.IsClippedToBoundsProperty
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.IsClippedToBoundsProperty
- uid: Microsoft.Maui.Controls.Compatibility.Layout.CascadeInputTransparentProperty
  commentId: F:Microsoft.Maui.Controls.Compatibility.Layout.CascadeInputTransparentProperty
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.cascadeinputtransparentproperty
  name: CascadeInputTransparentProperty
  nameWithType: Layout.CascadeInputTransparentProperty
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.CascadeInputTransparentProperty
- uid: Microsoft.Maui.Controls.Compatibility.Layout.PaddingProperty
  commentId: F:Microsoft.Maui.Controls.Compatibility.Layout.PaddingProperty
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.paddingproperty
  name: PaddingProperty
  nameWithType: Layout.PaddingProperty
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.PaddingProperty
- uid: Microsoft.Maui.Controls.Compatibility.Layout.ForceLayout
  commentId: M:Microsoft.Maui.Controls.Compatibility.Layout.ForceLayout
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.forcelayout
  name: ForceLayout()
  nameWithType: Layout.ForceLayout()
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.ForceLayout()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.ForceLayout
    name: ForceLayout
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.forcelayout
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.ForceLayout
    name: ForceLayout
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.forcelayout
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Compatibility.Layout.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
  commentId: M:Microsoft.Maui.Controls.Compatibility.Layout.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.measure
  name: Measure(double, double, MeasureFlags)
  nameWithType: Layout.Measure(double, double, MeasureFlags)
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.Measure(double, double, Microsoft.Maui.Controls.MeasureFlags)
  nameWithType.vb: Layout.Measure(Double, Double, MeasureFlags)
  fullName.vb: Microsoft.Maui.Controls.Compatibility.Layout.Measure(Double, Double, Microsoft.Maui.Controls.MeasureFlags)
  name.vb: Measure(Double, Double, MeasureFlags)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
    name: Measure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.measure
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.MeasureFlags
    name: MeasureFlags
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.measureflags
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.Measure(System.Double,System.Double,Microsoft.Maui.Controls.MeasureFlags)
    name: Measure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.measure
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.MeasureFlags
    name: MeasureFlags
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.measureflags
  - name: )
- uid: Microsoft.Maui.Controls.Compatibility.Layout.LayoutChildIntoBoundingRegion(Microsoft.Maui.Controls.VisualElement,Microsoft.Maui.Graphics.Rect)
  commentId: M:Microsoft.Maui.Controls.Compatibility.Layout.LayoutChildIntoBoundingRegion(Microsoft.Maui.Controls.VisualElement,Microsoft.Maui.Graphics.Rect)
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.layoutchildintoboundingregion
  name: LayoutChildIntoBoundingRegion(VisualElement, Rect)
  nameWithType: Layout.LayoutChildIntoBoundingRegion(VisualElement, Rect)
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.LayoutChildIntoBoundingRegion(Microsoft.Maui.Controls.VisualElement, Microsoft.Maui.Graphics.Rect)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.LayoutChildIntoBoundingRegion(Microsoft.Maui.Controls.VisualElement,Microsoft.Maui.Graphics.Rect)
    name: LayoutChildIntoBoundingRegion
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.layoutchildintoboundingregion
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.LayoutChildIntoBoundingRegion(Microsoft.Maui.Controls.VisualElement,Microsoft.Maui.Graphics.Rect)
    name: LayoutChildIntoBoundingRegion
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.layoutchildintoboundingregion
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
- uid: Microsoft.Maui.Controls.Compatibility.Layout.LowerChild(Microsoft.Maui.Controls.View)
  commentId: M:Microsoft.Maui.Controls.Compatibility.Layout.LowerChild(Microsoft.Maui.Controls.View)
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.lowerchild
  name: LowerChild(View)
  nameWithType: Layout.LowerChild(View)
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.LowerChild(Microsoft.Maui.Controls.View)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.LowerChild(Microsoft.Maui.Controls.View)
    name: LowerChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.lowerchild
  - name: (
  - uid: Microsoft.Maui.Controls.View
    name: View
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.LowerChild(Microsoft.Maui.Controls.View)
    name: LowerChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.lowerchild
  - name: (
  - uid: Microsoft.Maui.Controls.View
    name: View
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view
  - name: )
- uid: Microsoft.Maui.Controls.Compatibility.Layout.RaiseChild(Microsoft.Maui.Controls.View)
  commentId: M:Microsoft.Maui.Controls.Compatibility.Layout.RaiseChild(Microsoft.Maui.Controls.View)
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.raisechild
  name: RaiseChild(View)
  nameWithType: Layout.RaiseChild(View)
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.RaiseChild(Microsoft.Maui.Controls.View)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.RaiseChild(Microsoft.Maui.Controls.View)
    name: RaiseChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.raisechild
  - name: (
  - uid: Microsoft.Maui.Controls.View
    name: View
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.RaiseChild(Microsoft.Maui.Controls.View)
    name: RaiseChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.raisechild
  - name: (
  - uid: Microsoft.Maui.Controls.View
    name: View
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view
  - name: )
- uid: Microsoft.Maui.Controls.Compatibility.Layout.InvalidateLayout
  commentId: M:Microsoft.Maui.Controls.Compatibility.Layout.InvalidateLayout
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.invalidatelayout
  name: InvalidateLayout()
  nameWithType: Layout.InvalidateLayout()
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.InvalidateLayout()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.InvalidateLayout
    name: InvalidateLayout
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.invalidatelayout
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.InvalidateLayout
    name: InvalidateLayout
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.invalidatelayout
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Compatibility.Layout.OnChildMeasureInvalidated(System.Object,System.EventArgs)
  commentId: M:Microsoft.Maui.Controls.Compatibility.Layout.OnChildMeasureInvalidated(System.Object,System.EventArgs)
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.onchildmeasureinvalidated#microsoft-maui-controls-compatibility-layout-onchildmeasureinvalidated(system-object-system-eventargs)
  name: OnChildMeasureInvalidated(object, EventArgs)
  nameWithType: Layout.OnChildMeasureInvalidated(object, EventArgs)
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.OnChildMeasureInvalidated(object, System.EventArgs)
  nameWithType.vb: Layout.OnChildMeasureInvalidated(Object, EventArgs)
  fullName.vb: Microsoft.Maui.Controls.Compatibility.Layout.OnChildMeasureInvalidated(Object, System.EventArgs)
  name.vb: OnChildMeasureInvalidated(Object, EventArgs)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.OnChildMeasureInvalidated(System.Object,System.EventArgs)
    name: OnChildMeasureInvalidated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.onchildmeasureinvalidated#microsoft-maui-controls-compatibility-layout-onchildmeasureinvalidated(system-object-system-eventargs)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.EventArgs
    name: EventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.eventargs
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.OnChildMeasureInvalidated(System.Object,System.EventArgs)
    name: OnChildMeasureInvalidated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.onchildmeasureinvalidated#microsoft-maui-controls-compatibility-layout-onchildmeasureinvalidated(system-object-system-eventargs)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.EventArgs
    name: EventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.eventargs
  - name: )
- uid: Microsoft.Maui.Controls.Compatibility.Layout.OnChildMeasureInvalidated
  commentId: M:Microsoft.Maui.Controls.Compatibility.Layout.OnChildMeasureInvalidated
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.onchildmeasureinvalidated#microsoft-maui-controls-compatibility-layout-onchildmeasureinvalidated
  name: OnChildMeasureInvalidated()
  nameWithType: Layout.OnChildMeasureInvalidated()
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.OnChildMeasureInvalidated()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.OnChildMeasureInvalidated
    name: OnChildMeasureInvalidated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.onchildmeasureinvalidated#microsoft-maui-controls-compatibility-layout-onchildmeasureinvalidated
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.OnChildMeasureInvalidated
    name: OnChildMeasureInvalidated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.onchildmeasureinvalidated#microsoft-maui-controls-compatibility-layout-onchildmeasureinvalidated
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Compatibility.Layout.OnSizeAllocated(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.Compatibility.Layout.OnSizeAllocated(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.onsizeallocated
  name: OnSizeAllocated(double, double)
  nameWithType: Layout.OnSizeAllocated(double, double)
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.OnSizeAllocated(double, double)
  nameWithType.vb: Layout.OnSizeAllocated(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.Compatibility.Layout.OnSizeAllocated(Double, Double)
  name.vb: OnSizeAllocated(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.OnSizeAllocated(System.Double,System.Double)
    name: OnSizeAllocated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.onsizeallocated
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.OnSizeAllocated(System.Double,System.Double)
    name: OnSizeAllocated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.onsizeallocated
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.Compatibility.Layout.ShouldInvalidateOnChildAdded(Microsoft.Maui.Controls.View)
  commentId: M:Microsoft.Maui.Controls.Compatibility.Layout.ShouldInvalidateOnChildAdded(Microsoft.Maui.Controls.View)
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.shouldinvalidateonchildadded
  name: ShouldInvalidateOnChildAdded(View)
  nameWithType: Layout.ShouldInvalidateOnChildAdded(View)
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.ShouldInvalidateOnChildAdded(Microsoft.Maui.Controls.View)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.ShouldInvalidateOnChildAdded(Microsoft.Maui.Controls.View)
    name: ShouldInvalidateOnChildAdded
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.shouldinvalidateonchildadded
  - name: (
  - uid: Microsoft.Maui.Controls.View
    name: View
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.ShouldInvalidateOnChildAdded(Microsoft.Maui.Controls.View)
    name: ShouldInvalidateOnChildAdded
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.shouldinvalidateonchildadded
  - name: (
  - uid: Microsoft.Maui.Controls.View
    name: View
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view
  - name: )
- uid: Microsoft.Maui.Controls.Compatibility.Layout.ShouldInvalidateOnChildRemoved(Microsoft.Maui.Controls.View)
  commentId: M:Microsoft.Maui.Controls.Compatibility.Layout.ShouldInvalidateOnChildRemoved(Microsoft.Maui.Controls.View)
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.shouldinvalidateonchildremoved
  name: ShouldInvalidateOnChildRemoved(View)
  nameWithType: Layout.ShouldInvalidateOnChildRemoved(View)
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.ShouldInvalidateOnChildRemoved(Microsoft.Maui.Controls.View)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.ShouldInvalidateOnChildRemoved(Microsoft.Maui.Controls.View)
    name: ShouldInvalidateOnChildRemoved
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.shouldinvalidateonchildremoved
  - name: (
  - uid: Microsoft.Maui.Controls.View
    name: View
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.ShouldInvalidateOnChildRemoved(Microsoft.Maui.Controls.View)
    name: ShouldInvalidateOnChildRemoved
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.shouldinvalidateonchildremoved
  - name: (
  - uid: Microsoft.Maui.Controls.View
    name: View
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view
  - name: )
- uid: Microsoft.Maui.Controls.Compatibility.Layout.UpdateChildrenLayout
  commentId: M:Microsoft.Maui.Controls.Compatibility.Layout.UpdateChildrenLayout
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.updatechildrenlayout
  name: UpdateChildrenLayout()
  nameWithType: Layout.UpdateChildrenLayout()
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.UpdateChildrenLayout()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.UpdateChildrenLayout
    name: UpdateChildrenLayout
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.updatechildrenlayout
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.UpdateChildrenLayout
    name: UpdateChildrenLayout
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.updatechildrenlayout
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Compatibility.Layout.CrossPlatformMeasure(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.Compatibility.Layout.CrossPlatformMeasure(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.crossplatformmeasure
  name: CrossPlatformMeasure(double, double)
  nameWithType: Layout.CrossPlatformMeasure(double, double)
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.CrossPlatformMeasure(double, double)
  nameWithType.vb: Layout.CrossPlatformMeasure(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.Compatibility.Layout.CrossPlatformMeasure(Double, Double)
  name.vb: CrossPlatformMeasure(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.CrossPlatformMeasure(System.Double,System.Double)
    name: CrossPlatformMeasure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.crossplatformmeasure
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.CrossPlatformMeasure(System.Double,System.Double)
    name: CrossPlatformMeasure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.crossplatformmeasure
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.Compatibility.Layout.CrossPlatformArrange(Microsoft.Maui.Graphics.Rect)
  commentId: M:Microsoft.Maui.Controls.Compatibility.Layout.CrossPlatformArrange(Microsoft.Maui.Graphics.Rect)
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.crossplatformarrange
  name: CrossPlatformArrange(Rect)
  nameWithType: Layout.CrossPlatformArrange(Rect)
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.CrossPlatformArrange(Microsoft.Maui.Graphics.Rect)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.CrossPlatformArrange(Microsoft.Maui.Graphics.Rect)
    name: CrossPlatformArrange
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.crossplatformarrange
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Compatibility.Layout.CrossPlatformArrange(Microsoft.Maui.Graphics.Rect)
    name: CrossPlatformArrange
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.crossplatformarrange
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
- uid: Microsoft.Maui.Controls.Compatibility.Layout.IsClippedToBounds
  commentId: P:Microsoft.Maui.Controls.Compatibility.Layout.IsClippedToBounds
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.isclippedtobounds
  name: IsClippedToBounds
  nameWithType: Layout.IsClippedToBounds
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.IsClippedToBounds
- uid: Microsoft.Maui.Controls.Compatibility.Layout.Padding
  commentId: P:Microsoft.Maui.Controls.Compatibility.Layout.Padding
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.padding
  name: Padding
  nameWithType: Layout.Padding
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.Padding
- uid: Microsoft.Maui.Controls.Compatibility.Layout.CascadeInputTransparent
  commentId: P:Microsoft.Maui.Controls.Compatibility.Layout.CascadeInputTransparent
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.cascadeinputtransparent
  name: CascadeInputTransparent
  nameWithType: Layout.CascadeInputTransparent
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.CascadeInputTransparent
- uid: Microsoft.Maui.Controls.Compatibility.Layout.LayoutChanged
  commentId: E:Microsoft.Maui.Controls.Compatibility.Layout.LayoutChanged
  parent: Microsoft.Maui.Controls.Compatibility.Layout
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility.layout.layoutchanged
  name: LayoutChanged
  nameWithType: Layout.LayoutChanged
  fullName: Microsoft.Maui.Controls.Compatibility.Layout.LayoutChanged
- uid: Microsoft.Maui.Controls.View.VerticalOptionsProperty
  commentId: F:Microsoft.Maui.Controls.View.VerticalOptionsProperty
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.verticaloptionsproperty
  name: VerticalOptionsProperty
  nameWithType: View.VerticalOptionsProperty
  fullName: Microsoft.Maui.Controls.View.VerticalOptionsProperty
- uid: Microsoft.Maui.Controls.View.HorizontalOptionsProperty
  commentId: F:Microsoft.Maui.Controls.View.HorizontalOptionsProperty
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.horizontaloptionsproperty
  name: HorizontalOptionsProperty
  nameWithType: View.HorizontalOptionsProperty
  fullName: Microsoft.Maui.Controls.View.HorizontalOptionsProperty
- uid: Microsoft.Maui.Controls.View.MarginProperty
  commentId: F:Microsoft.Maui.Controls.View.MarginProperty
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.marginproperty
  name: MarginProperty
  nameWithType: View.MarginProperty
  fullName: Microsoft.Maui.Controls.View.MarginProperty
- uid: Microsoft.Maui.Controls.View.propertyMapper
  commentId: F:Microsoft.Maui.Controls.View.propertyMapper
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.propertymapper
  name: propertyMapper
  nameWithType: View.propertyMapper
  fullName: Microsoft.Maui.Controls.View.propertyMapper
- uid: Microsoft.Maui.Controls.View.ChangeVisualState
  commentId: M:Microsoft.Maui.Controls.View.ChangeVisualState
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.changevisualstate
  name: ChangeVisualState()
  nameWithType: View.ChangeVisualState()
  fullName: Microsoft.Maui.Controls.View.ChangeVisualState()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.View.ChangeVisualState
    name: ChangeVisualState
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.changevisualstate
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.View.ChangeVisualState
    name: ChangeVisualState
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.changevisualstate
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.View.GetChildElements(Microsoft.Maui.Graphics.Point)
  commentId: M:Microsoft.Maui.Controls.View.GetChildElements(Microsoft.Maui.Graphics.Point)
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getchildelements
  name: GetChildElements(Point)
  nameWithType: View.GetChildElements(Point)
  fullName: Microsoft.Maui.Controls.View.GetChildElements(Microsoft.Maui.Graphics.Point)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.View.GetChildElements(Microsoft.Maui.Graphics.Point)
    name: GetChildElements
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getchildelements
  - name: (
  - uid: Microsoft.Maui.Graphics.Point
    name: Point
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.point
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.View.GetChildElements(Microsoft.Maui.Graphics.Point)
    name: GetChildElements
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getchildelements
  - name: (
  - uid: Microsoft.Maui.Graphics.Point
    name: Point
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.point
  - name: )
- uid: Microsoft.Maui.Controls.View.GetRendererOverrides``1
  commentId: M:Microsoft.Maui.Controls.View.GetRendererOverrides``1
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getrendereroverrides
  name: GetRendererOverrides<T>()
  nameWithType: View.GetRendererOverrides<T>()
  fullName: Microsoft.Maui.Controls.View.GetRendererOverrides<T>()
  nameWithType.vb: View.GetRendererOverrides(Of T)()
  fullName.vb: Microsoft.Maui.Controls.View.GetRendererOverrides(Of T)()
  name.vb: GetRendererOverrides(Of T)()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.View.GetRendererOverrides``1
    name: GetRendererOverrides
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getrendereroverrides
  - name: <
  - name: T
  - name: '>'
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.View.GetRendererOverrides``1
    name: GetRendererOverrides
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.getrendereroverrides
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.View.GestureController
  commentId: P:Microsoft.Maui.Controls.View.GestureController
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.gesturecontroller
  name: GestureController
  nameWithType: View.GestureController
  fullName: Microsoft.Maui.Controls.View.GestureController
- uid: Microsoft.Maui.Controls.View.GestureRecognizers
  commentId: P:Microsoft.Maui.Controls.View.GestureRecognizers
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.gesturerecognizers
  name: GestureRecognizers
  nameWithType: View.GestureRecognizers
  fullName: Microsoft.Maui.Controls.View.GestureRecognizers
- uid: Microsoft.Maui.Controls.View.HorizontalOptions
  commentId: P:Microsoft.Maui.Controls.View.HorizontalOptions
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.horizontaloptions
  name: HorizontalOptions
  nameWithType: View.HorizontalOptions
  fullName: Microsoft.Maui.Controls.View.HorizontalOptions
- uid: Microsoft.Maui.Controls.View.Margin
  commentId: P:Microsoft.Maui.Controls.View.Margin
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.margin
  name: Margin
  nameWithType: View.Margin
  fullName: Microsoft.Maui.Controls.View.Margin
- uid: Microsoft.Maui.Controls.View.VerticalOptions
  commentId: P:Microsoft.Maui.Controls.View.VerticalOptions
  parent: Microsoft.Maui.Controls.View
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.view.verticaloptions
  name: VerticalOptions
  nameWithType: View.VerticalOptions
  fullName: Microsoft.Maui.Controls.View.VerticalOptions
- uid: Microsoft.Maui.Controls.VisualElement.NavigationProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.NavigationProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.navigationproperty
  name: NavigationProperty
  nameWithType: VisualElement.NavigationProperty
  fullName: Microsoft.Maui.Controls.VisualElement.NavigationProperty
- uid: Microsoft.Maui.Controls.VisualElement.StyleProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.StyleProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.styleproperty
  name: StyleProperty
  nameWithType: VisualElement.StyleProperty
  fullName: Microsoft.Maui.Controls.VisualElement.StyleProperty
- uid: Microsoft.Maui.Controls.VisualElement.InputTransparentProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.InputTransparentProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.inputtransparentproperty
  name: InputTransparentProperty
  nameWithType: VisualElement.InputTransparentProperty
  fullName: Microsoft.Maui.Controls.VisualElement.InputTransparentProperty
- uid: Microsoft.Maui.Controls.VisualElement.IsEnabledProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.IsEnabledProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabledproperty
  name: IsEnabledProperty
  nameWithType: VisualElement.IsEnabledProperty
  fullName: Microsoft.Maui.Controls.VisualElement.IsEnabledProperty
- uid: Microsoft.Maui.Controls.VisualElement.XProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.XProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.xproperty
  name: XProperty
  nameWithType: VisualElement.XProperty
  fullName: Microsoft.Maui.Controls.VisualElement.XProperty
- uid: Microsoft.Maui.Controls.VisualElement.YProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.YProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.yproperty
  name: YProperty
  nameWithType: VisualElement.YProperty
  fullName: Microsoft.Maui.Controls.VisualElement.YProperty
- uid: Microsoft.Maui.Controls.VisualElement.AnchorXProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.AnchorXProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchorxproperty
  name: AnchorXProperty
  nameWithType: VisualElement.AnchorXProperty
  fullName: Microsoft.Maui.Controls.VisualElement.AnchorXProperty
- uid: Microsoft.Maui.Controls.VisualElement.AnchorYProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.AnchorYProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchoryproperty
  name: AnchorYProperty
  nameWithType: VisualElement.AnchorYProperty
  fullName: Microsoft.Maui.Controls.VisualElement.AnchorYProperty
- uid: Microsoft.Maui.Controls.VisualElement.TranslationXProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.TranslationXProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationxproperty
  name: TranslationXProperty
  nameWithType: VisualElement.TranslationXProperty
  fullName: Microsoft.Maui.Controls.VisualElement.TranslationXProperty
- uid: Microsoft.Maui.Controls.VisualElement.TranslationYProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.TranslationYProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationyproperty
  name: TranslationYProperty
  nameWithType: VisualElement.TranslationYProperty
  fullName: Microsoft.Maui.Controls.VisualElement.TranslationYProperty
- uid: Microsoft.Maui.Controls.VisualElement.WidthProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.WidthProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthproperty
  name: WidthProperty
  nameWithType: VisualElement.WidthProperty
  fullName: Microsoft.Maui.Controls.VisualElement.WidthProperty
- uid: Microsoft.Maui.Controls.VisualElement.HeightProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.HeightProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightproperty
  name: HeightProperty
  nameWithType: VisualElement.HeightProperty
  fullName: Microsoft.Maui.Controls.VisualElement.HeightProperty
- uid: Microsoft.Maui.Controls.VisualElement.RotationProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.RotationProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationproperty
  name: RotationProperty
  nameWithType: VisualElement.RotationProperty
  fullName: Microsoft.Maui.Controls.VisualElement.RotationProperty
- uid: Microsoft.Maui.Controls.VisualElement.RotationXProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.RotationXProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationxproperty
  name: RotationXProperty
  nameWithType: VisualElement.RotationXProperty
  fullName: Microsoft.Maui.Controls.VisualElement.RotationXProperty
- uid: Microsoft.Maui.Controls.VisualElement.RotationYProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.RotationYProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationyproperty
  name: RotationYProperty
  nameWithType: VisualElement.RotationYProperty
  fullName: Microsoft.Maui.Controls.VisualElement.RotationYProperty
- uid: Microsoft.Maui.Controls.VisualElement.ScaleProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ScaleProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaleproperty
  name: ScaleProperty
  nameWithType: VisualElement.ScaleProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleProperty
- uid: Microsoft.Maui.Controls.VisualElement.ScaleXProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ScaleXProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scalexproperty
  name: ScaleXProperty
  nameWithType: VisualElement.ScaleXProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleXProperty
- uid: Microsoft.Maui.Controls.VisualElement.ScaleYProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ScaleYProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaleyproperty
  name: ScaleYProperty
  nameWithType: VisualElement.ScaleYProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleYProperty
- uid: Microsoft.Maui.Controls.VisualElement.ClipProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ClipProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.clipproperty
  name: ClipProperty
  nameWithType: VisualElement.ClipProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ClipProperty
- uid: Microsoft.Maui.Controls.VisualElement.VisualProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.VisualProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.visualproperty
  name: VisualProperty
  nameWithType: VisualElement.VisualProperty
  fullName: Microsoft.Maui.Controls.VisualElement.VisualProperty
- uid: Microsoft.Maui.Controls.VisualElement.IsVisibleProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.IsVisibleProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isvisibleproperty
  name: IsVisibleProperty
  nameWithType: VisualElement.IsVisibleProperty
  fullName: Microsoft.Maui.Controls.VisualElement.IsVisibleProperty
- uid: Microsoft.Maui.Controls.VisualElement.OpacityProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.OpacityProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.opacityproperty
  name: OpacityProperty
  nameWithType: VisualElement.OpacityProperty
  fullName: Microsoft.Maui.Controls.VisualElement.OpacityProperty
- uid: Microsoft.Maui.Controls.VisualElement.BackgroundColorProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.BackgroundColorProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundcolorproperty
  name: BackgroundColorProperty
  nameWithType: VisualElement.BackgroundColorProperty
  fullName: Microsoft.Maui.Controls.VisualElement.BackgroundColorProperty
- uid: Microsoft.Maui.Controls.VisualElement.BackgroundProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.BackgroundProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundproperty
  name: BackgroundProperty
  nameWithType: VisualElement.BackgroundProperty
  fullName: Microsoft.Maui.Controls.VisualElement.BackgroundProperty
- uid: Microsoft.Maui.Controls.VisualElement.BehaviorsProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.BehaviorsProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.behaviorsproperty
  name: BehaviorsProperty
  nameWithType: VisualElement.BehaviorsProperty
  fullName: Microsoft.Maui.Controls.VisualElement.BehaviorsProperty
- uid: Microsoft.Maui.Controls.VisualElement.TriggersProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.TriggersProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.triggersproperty
  name: TriggersProperty
  nameWithType: VisualElement.TriggersProperty
  fullName: Microsoft.Maui.Controls.VisualElement.TriggersProperty
- uid: Microsoft.Maui.Controls.VisualElement.WidthRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.WidthRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthrequestproperty
  name: WidthRequestProperty
  nameWithType: VisualElement.WidthRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.WidthRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.HeightRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.HeightRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightrequestproperty
  name: HeightRequestProperty
  nameWithType: VisualElement.HeightRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.HeightRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.MinimumWidthRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.MinimumWidthRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumwidthrequestproperty
  name: MinimumWidthRequestProperty
  nameWithType: VisualElement.MinimumWidthRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.MinimumWidthRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.MinimumHeightRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.MinimumHeightRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumheightrequestproperty
  name: MinimumHeightRequestProperty
  nameWithType: VisualElement.MinimumHeightRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.MinimumHeightRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.MaximumWidthRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.MaximumWidthRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumwidthrequestproperty
  name: MaximumWidthRequestProperty
  nameWithType: VisualElement.MaximumWidthRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.MaximumWidthRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.MaximumHeightRequestProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.MaximumHeightRequestProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumheightrequestproperty
  name: MaximumHeightRequestProperty
  nameWithType: VisualElement.MaximumHeightRequestProperty
  fullName: Microsoft.Maui.Controls.VisualElement.MaximumHeightRequestProperty
- uid: Microsoft.Maui.Controls.VisualElement.IsFocusedProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.IsFocusedProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isfocusedproperty
  name: IsFocusedProperty
  nameWithType: VisualElement.IsFocusedProperty
  fullName: Microsoft.Maui.Controls.VisualElement.IsFocusedProperty
- uid: Microsoft.Maui.Controls.VisualElement.FlowDirectionProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.FlowDirectionProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.flowdirectionproperty
  name: FlowDirectionProperty
  nameWithType: VisualElement.FlowDirectionProperty
  fullName: Microsoft.Maui.Controls.VisualElement.FlowDirectionProperty
- uid: Microsoft.Maui.Controls.VisualElement.WindowProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.WindowProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.windowproperty
  name: WindowProperty
  nameWithType: VisualElement.WindowProperty
  fullName: Microsoft.Maui.Controls.VisualElement.WindowProperty
- uid: Microsoft.Maui.Controls.VisualElement.ShadowProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ShadowProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.shadowproperty
  name: ShadowProperty
  nameWithType: VisualElement.ShadowProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ShadowProperty
- uid: Microsoft.Maui.Controls.VisualElement.ZIndexProperty
  commentId: F:Microsoft.Maui.Controls.VisualElement.ZIndexProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.zindexproperty
  name: ZIndexProperty
  nameWithType: VisualElement.ZIndexProperty
  fullName: Microsoft.Maui.Controls.VisualElement.ZIndexProperty
- uid: Microsoft.Maui.Controls.VisualElement.BatchBegin
  commentId: M:Microsoft.Maui.Controls.VisualElement.BatchBegin
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchbegin
  name: BatchBegin()
  nameWithType: VisualElement.BatchBegin()
  fullName: Microsoft.Maui.Controls.VisualElement.BatchBegin()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.BatchBegin
    name: BatchBegin
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchbegin
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.BatchBegin
    name: BatchBegin
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchbegin
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.BatchCommit
  commentId: M:Microsoft.Maui.Controls.VisualElement.BatchCommit
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchcommit
  name: BatchCommit()
  nameWithType: VisualElement.BatchCommit()
  fullName: Microsoft.Maui.Controls.VisualElement.BatchCommit()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.BatchCommit
    name: BatchCommit
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchcommit
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.BatchCommit
    name: BatchCommit
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.batchcommit
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Focus
  commentId: M:Microsoft.Maui.Controls.VisualElement.Focus
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focus
  name: Focus()
  nameWithType: VisualElement.Focus()
  fullName: Microsoft.Maui.Controls.VisualElement.Focus()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Focus
    name: Focus
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focus
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Focus
    name: Focus
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focus
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double)
  name: Measure(double, double)
  nameWithType: VisualElement.Measure(double, double)
  fullName: Microsoft.Maui.Controls.VisualElement.Measure(double, double)
  nameWithType.vb: VisualElement.Measure(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.Measure(Double, Double)
  name.vb: Measure(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
    name: Measure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double)
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Measure(System.Double,System.Double)
    name: Measure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measure#microsoft-maui-controls-visualelement-measure(system-double-system-double)
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Unfocus
  commentId: M:Microsoft.Maui.Controls.VisualElement.Unfocus
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocus
  name: Unfocus()
  nameWithType: VisualElement.Unfocus()
  fullName: Microsoft.Maui.Controls.VisualElement.Unfocus()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Unfocus
    name: Unfocus
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocus
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Unfocus
    name: Unfocus
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocus
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildadded
  name: OnChildAdded(Element)
  nameWithType: VisualElement.OnChildAdded(Element)
  fullName: Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
    name: OnChildAdded
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildadded
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildAdded(Microsoft.Maui.Controls.Element)
    name: OnChildAdded
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildadded
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
  commentId: M:Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildrenreordered
  name: OnChildrenReordered()
  nameWithType: VisualElement.OnChildrenReordered()
  fullName: Microsoft.Maui.Controls.VisualElement.OnChildrenReordered()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
    name: OnChildrenReordered
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildrenreordered
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.OnChildrenReordered
    name: OnChildrenReordered
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.onchildrenreordered
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizeallocated
  name: SizeAllocated(double, double)
  nameWithType: VisualElement.SizeAllocated(double, double)
  fullName: Microsoft.Maui.Controls.VisualElement.SizeAllocated(double, double)
  nameWithType.vb: VisualElement.SizeAllocated(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.VisualElement.SizeAllocated(Double, Double)
  name.vb: SizeAllocated(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
    name: SizeAllocated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizeallocated
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.SizeAllocated(System.Double,System.Double)
    name: SizeAllocated
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizeallocated
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
  commentId: M:Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.refreshisenabledproperty
  name: RefreshIsEnabledProperty()
  nameWithType: VisualElement.RefreshIsEnabledProperty()
  fullName: Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
    name: RefreshIsEnabledProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.refreshisenabledproperty
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.RefreshIsEnabledProperty
    name: RefreshIsEnabledProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.refreshisenabledproperty
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
  commentId: M:Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrange
  name: Arrange(Rect)
  nameWithType: VisualElement.Arrange(Rect)
  fullName: Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
    name: Arrange
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrange
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Arrange(Microsoft.Maui.Graphics.Rect)
    name: Arrange
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.arrange
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
  commentId: M:Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.layout
  name: Layout(Rect)
  nameWithType: VisualElement.Layout(Rect)
  fullName: Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
    name: Layout
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.layout
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.Layout(Microsoft.Maui.Graphics.Rect)
    name: Layout
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.layout
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasureOverride
  commentId: M:Microsoft.Maui.Controls.VisualElement.InvalidateMeasureOverride
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasureoverride
  name: InvalidateMeasureOverride()
  nameWithType: VisualElement.InvalidateMeasureOverride()
  fullName: Microsoft.Maui.Controls.VisualElement.InvalidateMeasureOverride()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasureOverride
    name: InvalidateMeasureOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasureoverride
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasureOverride
    name: InvalidateMeasureOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasureoverride
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  commentId: M:Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundcolor
  name: MapBackgroundColor(IViewHandler, IView)
  nameWithType: VisualElement.MapBackgroundColor(IViewHandler, IView)
  fullName: Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler, Microsoft.Maui.IView)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
    name: MapBackgroundColor
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundcolor
  - name: (
  - uid: Microsoft.Maui.IViewHandler
    name: IViewHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iviewhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundColor(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
    name: MapBackgroundColor
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundcolor
  - name: (
  - uid: Microsoft.Maui.IViewHandler
    name: IViewHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iviewhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  commentId: M:Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundimagesource
  name: MapBackgroundImageSource(IViewHandler, IView)
  nameWithType: VisualElement.MapBackgroundImageSource(IViewHandler, IView)
  fullName: Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler, Microsoft.Maui.IView)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
    name: MapBackgroundImageSource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundimagesource
  - name: (
  - uid: Microsoft.Maui.IViewHandler
    name: IViewHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iviewhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.MapBackgroundImageSource(Microsoft.Maui.IViewHandler,Microsoft.Maui.IView)
    name: MapBackgroundImageSource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.mapbackgroundimagesource
  - name: (
  - uid: Microsoft.Maui.IViewHandler
    name: IViewHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iviewhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.Visual
  commentId: P:Microsoft.Maui.Controls.VisualElement.Visual
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.visual
  name: Visual
  nameWithType: VisualElement.Visual
  fullName: Microsoft.Maui.Controls.VisualElement.Visual
- uid: Microsoft.Maui.Controls.VisualElement.FlowDirection
  commentId: P:Microsoft.Maui.Controls.VisualElement.FlowDirection
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.flowdirection
  name: FlowDirection
  nameWithType: VisualElement.FlowDirection
  fullName: Microsoft.Maui.Controls.VisualElement.FlowDirection
- uid: Microsoft.Maui.Controls.VisualElement.Window
  commentId: P:Microsoft.Maui.Controls.VisualElement.Window
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.window
  name: Window
  nameWithType: VisualElement.Window
  fullName: Microsoft.Maui.Controls.VisualElement.Window
- uid: Microsoft.Maui.Controls.VisualElement.AnchorX
  commentId: P:Microsoft.Maui.Controls.VisualElement.AnchorX
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchorx
  name: AnchorX
  nameWithType: VisualElement.AnchorX
  fullName: Microsoft.Maui.Controls.VisualElement.AnchorX
- uid: Microsoft.Maui.Controls.VisualElement.AnchorY
  commentId: P:Microsoft.Maui.Controls.VisualElement.AnchorY
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.anchory
  name: AnchorY
  nameWithType: VisualElement.AnchorY
  fullName: Microsoft.Maui.Controls.VisualElement.AnchorY
- uid: Microsoft.Maui.Controls.VisualElement.BackgroundColor
  commentId: P:Microsoft.Maui.Controls.VisualElement.BackgroundColor
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.backgroundcolor
  name: BackgroundColor
  nameWithType: VisualElement.BackgroundColor
  fullName: Microsoft.Maui.Controls.VisualElement.BackgroundColor
- uid: Microsoft.Maui.Controls.VisualElement.Background
  commentId: P:Microsoft.Maui.Controls.VisualElement.Background
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.background
  name: Background
  nameWithType: VisualElement.Background
  fullName: Microsoft.Maui.Controls.VisualElement.Background
- uid: Microsoft.Maui.Controls.VisualElement.Behaviors
  commentId: P:Microsoft.Maui.Controls.VisualElement.Behaviors
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.behaviors
  name: Behaviors
  nameWithType: VisualElement.Behaviors
  fullName: Microsoft.Maui.Controls.VisualElement.Behaviors
- uid: Microsoft.Maui.Controls.VisualElement.Bounds
  commentId: P:Microsoft.Maui.Controls.VisualElement.Bounds
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.bounds
  name: Bounds
  nameWithType: VisualElement.Bounds
  fullName: Microsoft.Maui.Controls.VisualElement.Bounds
- uid: Microsoft.Maui.Controls.VisualElement.Height
  commentId: P:Microsoft.Maui.Controls.VisualElement.Height
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.height
  name: Height
  nameWithType: VisualElement.Height
  fullName: Microsoft.Maui.Controls.VisualElement.Height
- uid: Microsoft.Maui.Controls.VisualElement.HeightRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.HeightRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.heightrequest
  name: HeightRequest
  nameWithType: VisualElement.HeightRequest
  fullName: Microsoft.Maui.Controls.VisualElement.HeightRequest
- uid: Microsoft.Maui.Controls.VisualElement.InputTransparent
  commentId: P:Microsoft.Maui.Controls.VisualElement.InputTransparent
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.inputtransparent
  name: InputTransparent
  nameWithType: VisualElement.InputTransparent
  fullName: Microsoft.Maui.Controls.VisualElement.InputTransparent
- uid: Microsoft.Maui.Controls.VisualElement.IsEnabled
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsEnabled
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabled
  name: IsEnabled
  nameWithType: VisualElement.IsEnabled
  fullName: Microsoft.Maui.Controls.VisualElement.IsEnabled
- uid: Microsoft.Maui.Controls.VisualElement.IsEnabledCore
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsEnabledCore
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isenabledcore
  name: IsEnabledCore
  nameWithType: VisualElement.IsEnabledCore
  fullName: Microsoft.Maui.Controls.VisualElement.IsEnabledCore
- uid: Microsoft.Maui.Controls.VisualElement.IsFocused
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsFocused
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isfocused
  name: IsFocused
  nameWithType: VisualElement.IsFocused
  fullName: Microsoft.Maui.Controls.VisualElement.IsFocused
- uid: Microsoft.Maui.Controls.VisualElement.IsVisible
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsVisible
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isvisible
  name: IsVisible
  nameWithType: VisualElement.IsVisible
  fullName: Microsoft.Maui.Controls.VisualElement.IsVisible
- uid: Microsoft.Maui.Controls.VisualElement.MinimumHeightRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.MinimumHeightRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumheightrequest
  name: MinimumHeightRequest
  nameWithType: VisualElement.MinimumHeightRequest
  fullName: Microsoft.Maui.Controls.VisualElement.MinimumHeightRequest
- uid: Microsoft.Maui.Controls.VisualElement.MinimumWidthRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.MinimumWidthRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.minimumwidthrequest
  name: MinimumWidthRequest
  nameWithType: VisualElement.MinimumWidthRequest
  fullName: Microsoft.Maui.Controls.VisualElement.MinimumWidthRequest
- uid: Microsoft.Maui.Controls.VisualElement.MaximumHeightRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.MaximumHeightRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumheightrequest
  name: MaximumHeightRequest
  nameWithType: VisualElement.MaximumHeightRequest
  fullName: Microsoft.Maui.Controls.VisualElement.MaximumHeightRequest
- uid: Microsoft.Maui.Controls.VisualElement.MaximumWidthRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.MaximumWidthRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.maximumwidthrequest
  name: MaximumWidthRequest
  nameWithType: VisualElement.MaximumWidthRequest
  fullName: Microsoft.Maui.Controls.VisualElement.MaximumWidthRequest
- uid: Microsoft.Maui.Controls.VisualElement.Opacity
  commentId: P:Microsoft.Maui.Controls.VisualElement.Opacity
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.opacity
  name: Opacity
  nameWithType: VisualElement.Opacity
  fullName: Microsoft.Maui.Controls.VisualElement.Opacity
- uid: Microsoft.Maui.Controls.VisualElement.Rotation
  commentId: P:Microsoft.Maui.Controls.VisualElement.Rotation
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotation
  name: Rotation
  nameWithType: VisualElement.Rotation
  fullName: Microsoft.Maui.Controls.VisualElement.Rotation
- uid: Microsoft.Maui.Controls.VisualElement.RotationX
  commentId: P:Microsoft.Maui.Controls.VisualElement.RotationX
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationx
  name: RotationX
  nameWithType: VisualElement.RotationX
  fullName: Microsoft.Maui.Controls.VisualElement.RotationX
- uid: Microsoft.Maui.Controls.VisualElement.RotationY
  commentId: P:Microsoft.Maui.Controls.VisualElement.RotationY
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.rotationy
  name: RotationY
  nameWithType: VisualElement.RotationY
  fullName: Microsoft.Maui.Controls.VisualElement.RotationY
- uid: Microsoft.Maui.Controls.VisualElement.Scale
  commentId: P:Microsoft.Maui.Controls.VisualElement.Scale
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scale
  name: Scale
  nameWithType: VisualElement.Scale
  fullName: Microsoft.Maui.Controls.VisualElement.Scale
- uid: Microsoft.Maui.Controls.VisualElement.ScaleX
  commentId: P:Microsoft.Maui.Controls.VisualElement.ScaleX
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scalex
  name: ScaleX
  nameWithType: VisualElement.ScaleX
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleX
- uid: Microsoft.Maui.Controls.VisualElement.ScaleY
  commentId: P:Microsoft.Maui.Controls.VisualElement.ScaleY
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.scaley
  name: ScaleY
  nameWithType: VisualElement.ScaleY
  fullName: Microsoft.Maui.Controls.VisualElement.ScaleY
- uid: Microsoft.Maui.Controls.VisualElement.TranslationX
  commentId: P:Microsoft.Maui.Controls.VisualElement.TranslationX
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationx
  name: TranslationX
  nameWithType: VisualElement.TranslationX
  fullName: Microsoft.Maui.Controls.VisualElement.TranslationX
- uid: Microsoft.Maui.Controls.VisualElement.TranslationY
  commentId: P:Microsoft.Maui.Controls.VisualElement.TranslationY
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.translationy
  name: TranslationY
  nameWithType: VisualElement.TranslationY
  fullName: Microsoft.Maui.Controls.VisualElement.TranslationY
- uid: Microsoft.Maui.Controls.VisualElement.Triggers
  commentId: P:Microsoft.Maui.Controls.VisualElement.Triggers
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.triggers
  name: Triggers
  nameWithType: VisualElement.Triggers
  fullName: Microsoft.Maui.Controls.VisualElement.Triggers
- uid: Microsoft.Maui.Controls.VisualElement.Width
  commentId: P:Microsoft.Maui.Controls.VisualElement.Width
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.width
  name: Width
  nameWithType: VisualElement.Width
  fullName: Microsoft.Maui.Controls.VisualElement.Width
- uid: Microsoft.Maui.Controls.VisualElement.WidthRequest
  commentId: P:Microsoft.Maui.Controls.VisualElement.WidthRequest
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.widthrequest
  name: WidthRequest
  nameWithType: VisualElement.WidthRequest
  fullName: Microsoft.Maui.Controls.VisualElement.WidthRequest
- uid: Microsoft.Maui.Controls.VisualElement.X
  commentId: P:Microsoft.Maui.Controls.VisualElement.X
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.x
  name: X
  nameWithType: VisualElement.X
  fullName: Microsoft.Maui.Controls.VisualElement.X
- uid: Microsoft.Maui.Controls.VisualElement.Y
  commentId: P:Microsoft.Maui.Controls.VisualElement.Y
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.y
  name: Y
  nameWithType: VisualElement.Y
  fullName: Microsoft.Maui.Controls.VisualElement.Y
- uid: Microsoft.Maui.Controls.VisualElement.Clip
  commentId: P:Microsoft.Maui.Controls.VisualElement.Clip
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.clip
  name: Clip
  nameWithType: VisualElement.Clip
  fullName: Microsoft.Maui.Controls.VisualElement.Clip
- uid: Microsoft.Maui.Controls.VisualElement.Resources
  commentId: P:Microsoft.Maui.Controls.VisualElement.Resources
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.resources
  name: Resources
  nameWithType: VisualElement.Resources
  fullName: Microsoft.Maui.Controls.VisualElement.Resources
- uid: Microsoft.Maui.Controls.VisualElement.Frame
  commentId: P:Microsoft.Maui.Controls.VisualElement.Frame
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.frame
  name: Frame
  nameWithType: VisualElement.Frame
  fullName: Microsoft.Maui.Controls.VisualElement.Frame
- uid: Microsoft.Maui.Controls.VisualElement.Handler
  commentId: P:Microsoft.Maui.Controls.VisualElement.Handler
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.handler
  name: Handler
  nameWithType: VisualElement.Handler
  fullName: Microsoft.Maui.Controls.VisualElement.Handler
- uid: Microsoft.Maui.Controls.VisualElement.Shadow
  commentId: P:Microsoft.Maui.Controls.VisualElement.Shadow
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.shadow
  name: Shadow
  nameWithType: VisualElement.Shadow
  fullName: Microsoft.Maui.Controls.VisualElement.Shadow
- uid: Microsoft.Maui.Controls.VisualElement.ZIndex
  commentId: P:Microsoft.Maui.Controls.VisualElement.ZIndex
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.zindex
  name: ZIndex
  nameWithType: VisualElement.ZIndex
  fullName: Microsoft.Maui.Controls.VisualElement.ZIndex
- uid: Microsoft.Maui.Controls.VisualElement.DesiredSize
  commentId: P:Microsoft.Maui.Controls.VisualElement.DesiredSize
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.desiredsize
  name: DesiredSize
  nameWithType: VisualElement.DesiredSize
  fullName: Microsoft.Maui.Controls.VisualElement.DesiredSize
- uid: Microsoft.Maui.Controls.VisualElement.IsLoaded
  commentId: P:Microsoft.Maui.Controls.VisualElement.IsLoaded
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.isloaded
  name: IsLoaded
  nameWithType: VisualElement.IsLoaded
  fullName: Microsoft.Maui.Controls.VisualElement.IsLoaded
- uid: Microsoft.Maui.Controls.VisualElement.ChildrenReordered
  commentId: E:Microsoft.Maui.Controls.VisualElement.ChildrenReordered
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.childrenreordered
  name: ChildrenReordered
  nameWithType: VisualElement.ChildrenReordered
  fullName: Microsoft.Maui.Controls.VisualElement.ChildrenReordered
- uid: Microsoft.Maui.Controls.VisualElement.Focused
  commentId: E:Microsoft.Maui.Controls.VisualElement.Focused
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.focused
  name: Focused
  nameWithType: VisualElement.Focused
  fullName: Microsoft.Maui.Controls.VisualElement.Focused
- uid: Microsoft.Maui.Controls.VisualElement.MeasureInvalidated
  commentId: E:Microsoft.Maui.Controls.VisualElement.MeasureInvalidated
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.measureinvalidated
  name: MeasureInvalidated
  nameWithType: VisualElement.MeasureInvalidated
  fullName: Microsoft.Maui.Controls.VisualElement.MeasureInvalidated
- uid: Microsoft.Maui.Controls.VisualElement.SizeChanged
  commentId: E:Microsoft.Maui.Controls.VisualElement.SizeChanged
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.sizechanged
  name: SizeChanged
  nameWithType: VisualElement.SizeChanged
  fullName: Microsoft.Maui.Controls.VisualElement.SizeChanged
- uid: Microsoft.Maui.Controls.VisualElement.Unfocused
  commentId: E:Microsoft.Maui.Controls.VisualElement.Unfocused
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unfocused
  name: Unfocused
  nameWithType: VisualElement.Unfocused
  fullName: Microsoft.Maui.Controls.VisualElement.Unfocused
- uid: Microsoft.Maui.Controls.VisualElement.Loaded
  commentId: E:Microsoft.Maui.Controls.VisualElement.Loaded
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.loaded
  name: Loaded
  nameWithType: VisualElement.Loaded
  fullName: Microsoft.Maui.Controls.VisualElement.Loaded
- uid: Microsoft.Maui.Controls.VisualElement.Unloaded
  commentId: E:Microsoft.Maui.Controls.VisualElement.Unloaded
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.unloaded
  name: Unloaded
  nameWithType: VisualElement.Unloaded
  fullName: Microsoft.Maui.Controls.VisualElement.Unloaded
- uid: Microsoft.Maui.Controls.NavigableElement.Navigation
  commentId: P:Microsoft.Maui.Controls.NavigableElement.Navigation
  parent: Microsoft.Maui.Controls.NavigableElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.navigableelement.navigation
  name: Navigation
  nameWithType: NavigableElement.Navigation
  fullName: Microsoft.Maui.Controls.NavigableElement.Navigation
- uid: Microsoft.Maui.Controls.StyleableElement.Style
  commentId: P:Microsoft.Maui.Controls.StyleableElement.Style
  parent: Microsoft.Maui.Controls.StyleableElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.style
  name: Style
  nameWithType: StyleableElement.Style
  fullName: Microsoft.Maui.Controls.StyleableElement.Style
- uid: Microsoft.Maui.Controls.StyleableElement.StyleClass
  commentId: P:Microsoft.Maui.Controls.StyleableElement.StyleClass
  parent: Microsoft.Maui.Controls.StyleableElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.styleclass
  name: StyleClass
  nameWithType: StyleableElement.StyleClass
  fullName: Microsoft.Maui.Controls.StyleableElement.StyleClass
- uid: Microsoft.Maui.Controls.StyleableElement.class
  commentId: P:Microsoft.Maui.Controls.StyleableElement.class
  parent: Microsoft.Maui.Controls.StyleableElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.styleableelement.class
  name: class
  nameWithType: StyleableElement.class
  fullName: Microsoft.Maui.Controls.StyleableElement.class
- uid: Microsoft.Maui.Controls.Element.AutomationIdProperty
  commentId: F:Microsoft.Maui.Controls.Element.AutomationIdProperty
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationidproperty
  name: AutomationIdProperty
  nameWithType: Element.AutomationIdProperty
  fullName: Microsoft.Maui.Controls.Element.AutomationIdProperty
- uid: Microsoft.Maui.Controls.Element.ClassIdProperty
  commentId: F:Microsoft.Maui.Controls.Element.ClassIdProperty
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classidproperty
  name: ClassIdProperty
  nameWithType: Element.ClassIdProperty
  fullName: Microsoft.Maui.Controls.Element.ClassIdProperty
- uid: Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild
  name: InsertLogicalChild(int, Element)
  nameWithType: Element.InsertLogicalChild(int, Element)
  fullName: Microsoft.Maui.Controls.Element.InsertLogicalChild(int, Microsoft.Maui.Controls.Element)
  nameWithType.vb: Element.InsertLogicalChild(Integer, Element)
  fullName.vb: Microsoft.Maui.Controls.Element.InsertLogicalChild(Integer, Microsoft.Maui.Controls.Element)
  name.vb: InsertLogicalChild(Integer, Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
    name: InsertLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
    name: InsertLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild
  name: AddLogicalChild(Element)
  nameWithType: Element.AddLogicalChild(Element)
  fullName: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
    name: AddLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
    name: AddLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild
  name: RemoveLogicalChild(Element)
  nameWithType: Element.RemoveLogicalChild(Element)
  fullName: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
    name: RemoveLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
    name: RemoveLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.ClearLogicalChildren
  commentId: M:Microsoft.Maui.Controls.Element.ClearLogicalChildren
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren
  name: ClearLogicalChildren()
  nameWithType: Element.ClearLogicalChildren()
  fullName: Microsoft.Maui.Controls.Element.ClearLogicalChildren()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.ClearLogicalChildren
    name: ClearLogicalChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.ClearLogicalChildren
    name: ClearLogicalChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.FindByName(System.String)
  commentId: M:Microsoft.Maui.Controls.Element.FindByName(System.String)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname
  name: FindByName(string)
  nameWithType: Element.FindByName(string)
  fullName: Microsoft.Maui.Controls.Element.FindByName(string)
  nameWithType.vb: Element.FindByName(String)
  fullName.vb: Microsoft.Maui.Controls.Element.FindByName(String)
  name.vb: FindByName(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.FindByName(System.String)
    name: FindByName
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.FindByName(System.String)
    name: FindByName
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource
  name: RemoveDynamicResource(BindableProperty)
  nameWithType: Element.RemoveDynamicResource(BindableProperty)
  fullName: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
  commentId: M:Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource
  name: SetDynamicResource(BindableProperty, string)
  nameWithType: Element.SetDynamicResource(BindableProperty, string)
  fullName: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty, string)
  nameWithType.vb: Element.SetDynamicResource(BindableProperty, String)
  fullName.vb: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty, String)
  name.vb: SetDynamicResource(BindableProperty, String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
    name: SetDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
    name: SetDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  commentId: M:Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging
  name: OnParentChanging(ParentChangingEventArgs)
  nameWithType: Element.OnParentChanging(ParentChangingEventArgs)
  fullName: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
    name: OnParentChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging
  - name: (
  - uid: Microsoft.Maui.Controls.ParentChangingEventArgs
    name: ParentChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.parentchangingeventargs
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
    name: OnParentChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging
  - name: (
  - uid: Microsoft.Maui.Controls.ParentChangingEventArgs
    name: ParentChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.parentchangingeventargs
  - name: )
- uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree
  name: MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)
  nameWithType: Element.MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)
  fullName: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler, Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesIsInAccessibleTree
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesIsInAccessibleTree
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren
  name: MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)
  nameWithType: Element.MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)
  fullName: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler, Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesExcludedWithChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesExcludedWithChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.AutomationId
  commentId: P:Microsoft.Maui.Controls.Element.AutomationId
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationid
  name: AutomationId
  nameWithType: Element.AutomationId
  fullName: Microsoft.Maui.Controls.Element.AutomationId
- uid: Microsoft.Maui.Controls.Element.ClassId
  commentId: P:Microsoft.Maui.Controls.Element.ClassId
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classid
  name: ClassId
  nameWithType: Element.ClassId
  fullName: Microsoft.Maui.Controls.Element.ClassId
- uid: Microsoft.Maui.Controls.Element.Effects
  commentId: P:Microsoft.Maui.Controls.Element.Effects
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.effects
  name: Effects
  nameWithType: Element.Effects
  fullName: Microsoft.Maui.Controls.Element.Effects
- uid: Microsoft.Maui.Controls.Element.Id
  commentId: P:Microsoft.Maui.Controls.Element.Id
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.id
  name: Id
  nameWithType: Element.Id
  fullName: Microsoft.Maui.Controls.Element.Id
- uid: Microsoft.Maui.Controls.Element.StyleId
  commentId: P:Microsoft.Maui.Controls.Element.StyleId
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.styleid
  name: StyleId
  nameWithType: Element.StyleId
  fullName: Microsoft.Maui.Controls.Element.StyleId
- uid: Microsoft.Maui.Controls.Element.Parent
  commentId: P:Microsoft.Maui.Controls.Element.Parent
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parent
  name: Parent
  nameWithType: Element.Parent
  fullName: Microsoft.Maui.Controls.Element.Parent
- uid: Microsoft.Maui.Controls.Element.ChildAdded
  commentId: E:Microsoft.Maui.Controls.Element.ChildAdded
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childadded
  name: ChildAdded
  nameWithType: Element.ChildAdded
  fullName: Microsoft.Maui.Controls.Element.ChildAdded
- uid: Microsoft.Maui.Controls.Element.ChildRemoved
  commentId: E:Microsoft.Maui.Controls.Element.ChildRemoved
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childremoved
  name: ChildRemoved
  nameWithType: Element.ChildRemoved
  fullName: Microsoft.Maui.Controls.Element.ChildRemoved
- uid: Microsoft.Maui.Controls.Element.DescendantAdded
  commentId: E:Microsoft.Maui.Controls.Element.DescendantAdded
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantadded
  name: DescendantAdded
  nameWithType: Element.DescendantAdded
  fullName: Microsoft.Maui.Controls.Element.DescendantAdded
- uid: Microsoft.Maui.Controls.Element.DescendantRemoved
  commentId: E:Microsoft.Maui.Controls.Element.DescendantRemoved
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantremoved
  name: DescendantRemoved
  nameWithType: Element.DescendantRemoved
  fullName: Microsoft.Maui.Controls.Element.DescendantRemoved
- uid: Microsoft.Maui.Controls.Element.ParentChanging
  commentId: E:Microsoft.Maui.Controls.Element.ParentChanging
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanging
  name: ParentChanging
  nameWithType: Element.ParentChanging
  fullName: Microsoft.Maui.Controls.Element.ParentChanging
- uid: Microsoft.Maui.Controls.Element.ParentChanged
  commentId: E:Microsoft.Maui.Controls.Element.ParentChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanged
  name: ParentChanged
  nameWithType: Element.ParentChanged
  fullName: Microsoft.Maui.Controls.Element.ParentChanged
- uid: Microsoft.Maui.Controls.Element.HandlerChanging
  commentId: E:Microsoft.Maui.Controls.Element.HandlerChanging
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanging
  name: HandlerChanging
  nameWithType: Element.HandlerChanging
  fullName: Microsoft.Maui.Controls.Element.HandlerChanging
- uid: Microsoft.Maui.Controls.Element.HandlerChanged
  commentId: E:Microsoft.Maui.Controls.Element.HandlerChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanged
  name: HandlerChanged
  nameWithType: Element.HandlerChanged
  fullName: Microsoft.Maui.Controls.Element.HandlerChanged
- uid: Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  commentId: F:Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextproperty
  name: BindingContextProperty
  nameWithType: BindableObject.BindingContextProperty
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContextProperty
- uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  name: ClearValue(BindableProperty)
  nameWithType: BindableObject.ClearValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  commentId: M:Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  name: ClearValue(BindablePropertyKey)
  nameWithType: BindableObject.ClearValue(BindablePropertyKey)
  fullName: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  name: GetValue(BindableProperty)
  nameWithType: BindableObject.GetValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
    name: GetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
    name: GetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  name: IsSet(BindableProperty)
  nameWithType: BindableObject.IsSet(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
    name: IsSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
    name: IsSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  name: RemoveBinding(BindableProperty)
  nameWithType: BindableObject.RemoveBinding(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  name: SetBinding(BindableProperty, BindingBase)
  nameWithType: BindableObject.SetBinding(BindableProperty, BindingBase)
  fullName: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty, Microsoft.Maui.Controls.BindingBase)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
    name: SetBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindingBase
    name: BindingBase
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingbase
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
    name: SetBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindingBase
    name: BindingBase
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingbase
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
  commentId: M:Microsoft.Maui.Controls.BindableObject.ApplyBindings
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  name: ApplyBindings()
  nameWithType: BindableObject.ApplyBindings()
  fullName: Microsoft.Maui.Controls.BindableObject.ApplyBindings()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
    name: ApplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
    name: ApplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  commentId: M:Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  name: OnPropertyChanging(string)
  nameWithType: BindableObject.OnPropertyChanging(string)
  fullName: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(string)
  nameWithType.vb: BindableObject.OnPropertyChanging(String)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(String)
  name.vb: OnPropertyChanging(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
    name: OnPropertyChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
    name: OnPropertyChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  commentId: M:Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  name: UnapplyBindings()
  nameWithType: BindableObject.UnapplyBindings()
  fullName: Microsoft.Maui.Controls.BindableObject.UnapplyBindings()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
    name: UnapplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
    name: UnapplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  name: SetValue(BindableProperty, object)
  nameWithType: BindableObject.SetValue(BindableProperty, object)
  fullName: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty, object)
  nameWithType.vb: BindableObject.SetValue(BindableProperty, Object)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty, Object)
  name.vb: SetValue(BindableProperty, Object)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  name: SetValue(BindablePropertyKey, object)
  nameWithType: BindableObject.SetValue(BindablePropertyKey, object)
  fullName: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey, object)
  nameWithType.vb: BindableObject.SetValue(BindablePropertyKey, Object)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey, Object)
  name.vb: SetValue(BindablePropertyKey, Object)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  name: CoerceValue(BindableProperty)
  nameWithType: BindableObject.CoerceValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  commentId: M:Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  name: CoerceValue(BindablePropertyKey)
  nameWithType: BindableObject.CoerceValue(BindablePropertyKey)
  fullName: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.Dispatcher
  commentId: P:Microsoft.Maui.Controls.BindableObject.Dispatcher
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.dispatcher
  name: Dispatcher
  nameWithType: BindableObject.Dispatcher
  fullName: Microsoft.Maui.Controls.BindableObject.Dispatcher
- uid: Microsoft.Maui.Controls.BindableObject.BindingContext
  commentId: P:Microsoft.Maui.Controls.BindableObject.BindingContext
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontext
  name: BindingContext
  nameWithType: BindableObject.BindingContext
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContext
- uid: Microsoft.Maui.Controls.BindableObject.PropertyChanged
  commentId: E:Microsoft.Maui.Controls.BindableObject.PropertyChanged
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanged
  name: PropertyChanged
  nameWithType: BindableObject.PropertyChanged
  fullName: Microsoft.Maui.Controls.BindableObject.PropertyChanged
- uid: Microsoft.Maui.Controls.BindableObject.PropertyChanging
  commentId: E:Microsoft.Maui.Controls.BindableObject.PropertyChanging
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanging
  name: PropertyChanging
  nameWithType: BindableObject.PropertyChanging
  fullName: Microsoft.Maui.Controls.BindableObject.PropertyChanging
- uid: Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  commentId: E:Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextchanged
  name: BindingContextChanged
  nameWithType: BindableObject.BindingContextChanged
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContextChanged
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: DrawnUi.Draw.IDrawnBase.DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.ISkiaControl)
  commentId: M:DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
  parent: DrawnUi.Draw.DrawnExtensions
  definition: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
  href: DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_
  name: GetVelocityRatioForChild(IDrawnBase, ISkiaControl)
  nameWithType: DrawnExtensions.GetVelocityRatioForChild(IDrawnBase, ISkiaControl)
  fullName: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase, DrawnUi.Draw.ISkiaControl)
  spec.csharp:
  - uid: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
    name: GetVelocityRatioForChild
    href: DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_
  - name: (
  - uid: DrawnUi.Draw.IDrawnBase
    name: IDrawnBase
    href: DrawnUi.Draw.IDrawnBase.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ISkiaControl
    name: ISkiaControl
    href: DrawnUi.Draw.ISkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
    name: GetVelocityRatioForChild
    href: DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_
  - name: (
  - uid: DrawnUi.Draw.IDrawnBase
    name: IDrawnBase
    href: DrawnUi.Draw.IDrawnBase.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ISkiaControl
    name: ISkiaControl
    href: DrawnUi.Draw.ISkiaControl.html
  - name: )
- uid: DrawnUi.Views.Canvas.DrawnUi.Draw.FluentExtensions.AssignNative``1(DrawnUi.Views.Canvas@)
  commentId: M:DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  parent: DrawnUi.Draw.FluentExtensions
  definition: DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  name: AssignNative<Canvas>(Canvas, out Canvas)
  nameWithType: FluentExtensions.AssignNative<Canvas>(Canvas, out Canvas)
  fullName: DrawnUi.Draw.FluentExtensions.AssignNative<DrawnUi.Views.Canvas>(DrawnUi.Views.Canvas, out DrawnUi.Views.Canvas)
  nameWithType.vb: FluentExtensions.AssignNative(Of Canvas)(Canvas, Canvas)
  fullName.vb: DrawnUi.Draw.FluentExtensions.AssignNative(Of DrawnUi.Views.Canvas)(DrawnUi.Views.Canvas, DrawnUi.Views.Canvas)
  name.vb: AssignNative(Of Canvas)(Canvas, Canvas)
  spec.csharp:
  - uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(DrawnUi.Views.Canvas,DrawnUi.Views.Canvas@)
    name: AssignNative
    href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  - name: <
  - uid: DrawnUi.Views.Canvas
    name: Canvas
    href: DrawnUi.Views.Canvas.html
  - name: '>'
  - name: (
  - uid: DrawnUi.Views.Canvas
    name: Canvas
    href: DrawnUi.Views.Canvas.html
  - name: ','
  - name: " "
  - name: out
  - name: " "
  - uid: DrawnUi.Views.Canvas
    name: Canvas
    href: DrawnUi.Views.Canvas.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(DrawnUi.Views.Canvas,DrawnUi.Views.Canvas@)
    name: AssignNative
    href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Views.Canvas
    name: Canvas
    href: DrawnUi.Views.Canvas.html
  - name: )
  - name: (
  - uid: DrawnUi.Views.Canvas
    name: Canvas
    href: DrawnUi.Views.Canvas.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Views.Canvas
    name: Canvas
    href: DrawnUi.Views.Canvas.html
  - name: )
- uid: Microsoft.Maui.Controls.Element.DrawnUi.Draw.StaticResourcesExtensions.FindParent``1
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  parent: DrawnUi.Draw.StaticResourcesExtensions
  definition: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  name: FindParent<T>(Element)
  nameWithType: StaticResourcesExtensions.FindParent<T>(Element)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.FindParent<T>(Microsoft.Maui.Controls.Element)
  nameWithType.vb: StaticResourcesExtensions.FindParent(Of T)(Element)
  fullName.vb: DrawnUi.Draw.StaticResourcesExtensions.FindParent(Of T)(Microsoft.Maui.Controls.Element)
  name.vb: FindParent(Of T)(Element)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: <
  - name: T
  - name: '>'
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.FindMauiContext(System.Boolean)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  name: FindMauiContext(Element, bool)
  nameWithType: InternalExtensions.FindMauiContext(Element, bool)
  fullName: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, bool)
  nameWithType.vb: InternalExtensions.FindMauiContext(Element, Boolean)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, Boolean)
  name.vb: FindMauiContext(Element, Boolean)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.GetParentsPath
  commentId: M:DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  name: GetParentsPath(Element)
  nameWithType: InternalExtensions.GetParentsPath(Element)
  fullName: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.VisualElement.DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  parent: DrawnUi.Draw.StaticResourcesExtensions
  definition: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  name: GetAllWithMyselfParents(VisualElement)
  nameWithType: StaticResourcesExtensions.GetAllWithMyselfParents(VisualElement)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
    name: GetAllWithMyselfParents
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
    name: GetAllWithMyselfParents
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
- uid: Microsoft.Maui.IView.DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren
  commentId: M:DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  name: DisposeControlAndChildren(IView)
  nameWithType: InternalExtensions.DisposeControlAndChildren(IView)
  fullName: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
    name: DisposeControlAndChildren
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  - name: (
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
    name: DisposeControlAndChildren
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  - name: (
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: Microsoft.Maui.Controls
  commentId: N:Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls
  nameWithType: Microsoft.Maui.Controls
  fullName: Microsoft.Maui.Controls
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
- uid: Microsoft.Maui.Controls.Compatibility
  commentId: N:Microsoft.Maui.Controls.Compatibility
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls.Compatibility
  nameWithType: Microsoft.Maui.Controls.Compatibility
  fullName: Microsoft.Maui.Controls.Compatibility
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  - name: .
  - uid: Microsoft.Maui.Controls.Compatibility
    name: Compatibility
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  - name: .
  - uid: Microsoft.Maui.Controls.Compatibility
    name: Compatibility
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.compatibility
- uid: System.ComponentModel
  commentId: N:System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.ComponentModel
  nameWithType: System.ComponentModel
  fullName: System.ComponentModel
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
- uid: Microsoft.Maui
  commentId: N:Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui
  nameWithType: Microsoft.Maui
  fullName: Microsoft.Maui
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
- uid: Microsoft.Maui.Controls.Internals
  commentId: N:Microsoft.Maui.Controls.Internals
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls.Internals
  nameWithType: Microsoft.Maui.Controls.Internals
  fullName: Microsoft.Maui.Controls.Internals
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  - name: .
  - uid: Microsoft.Maui.Controls.Internals
    name: Internals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.internals
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  - name: .
  - uid: Microsoft.Maui.Controls.Internals
    name: Internals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.internals
- uid: Microsoft.Maui.HotReload
  commentId: N:Microsoft.Maui.HotReload
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.HotReload
  nameWithType: Microsoft.Maui.HotReload
  fullName: Microsoft.Maui.HotReload
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.HotReload
    name: HotReload
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.HotReload
    name: HotReload
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.hotreload
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: AppoMobi.Maui.Gestures
  commentId: N:AppoMobi.Maui.Gestures
  isExternal: true
  name: AppoMobi.Maui.Gestures
  nameWithType: AppoMobi.Maui.Gestures
  fullName: AppoMobi.Maui.Gestures
  spec.csharp:
  - uid: AppoMobi
    name: AppoMobi
    isExternal: true
  - name: .
  - uid: AppoMobi.Maui
    name: Maui
    isExternal: true
  - name: .
  - uid: AppoMobi.Maui.Gestures
    name: Gestures
    isExternal: true
  spec.vb:
  - uid: AppoMobi
    name: AppoMobi
    isExternal: true
  - name: .
  - uid: AppoMobi.Maui
    name: Maui
    isExternal: true
  - name: .
  - uid: AppoMobi.Maui.Gestures
    name: Gestures
    isExternal: true
- uid: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
  commentId: M:DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
  href: DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_
  name: GetVelocityRatioForChild(IDrawnBase, ISkiaControl)
  nameWithType: DrawnExtensions.GetVelocityRatioForChild(IDrawnBase, ISkiaControl)
  fullName: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase, DrawnUi.Draw.ISkiaControl)
  spec.csharp:
  - uid: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
    name: GetVelocityRatioForChild
    href: DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_
  - name: (
  - uid: DrawnUi.Draw.IDrawnBase
    name: IDrawnBase
    href: DrawnUi.Draw.IDrawnBase.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ISkiaControl
    name: ISkiaControl
    href: DrawnUi.Draw.ISkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DrawnExtensions.GetVelocityRatioForChild(DrawnUi.Draw.IDrawnBase,DrawnUi.Draw.ISkiaControl)
    name: GetVelocityRatioForChild
    href: DrawnUi.Draw.DrawnExtensions.html#DrawnUi_Draw_DrawnExtensions_GetVelocityRatioForChild_DrawnUi_Draw_IDrawnBase_DrawnUi_Draw_ISkiaControl_
  - name: (
  - uid: DrawnUi.Draw.IDrawnBase
    name: IDrawnBase
    href: DrawnUi.Draw.IDrawnBase.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ISkiaControl
    name: ISkiaControl
    href: DrawnUi.Draw.ISkiaControl.html
  - name: )
- uid: DrawnUi.Draw.DrawnExtensions
  commentId: T:DrawnUi.Draw.DrawnExtensions
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawnExtensions.html
  name: DrawnExtensions
  nameWithType: DrawnExtensions
  fullName: DrawnUi.Draw.DrawnExtensions
- uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  commentId: M:DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  name: AssignNative<T>(T, out T)
  nameWithType: FluentExtensions.AssignNative<T>(T, out T)
  fullName: DrawnUi.Draw.FluentExtensions.AssignNative<T>(T, out T)
  nameWithType.vb: FluentExtensions.AssignNative(Of T)(T, T)
  fullName.vb: DrawnUi.Draw.FluentExtensions.AssignNative(Of T)(T, T)
  name.vb: AssignNative(Of T)(T, T)
  spec.csharp:
  - uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
    name: AssignNative
    href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  - name: <
  - name: T
  - name: '>'
  - name: (
  - name: T
  - name: ','
  - name: " "
  - name: out
  - name: " "
  - name: T
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
    name: AssignNative
    href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - name: T
  - name: ','
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Draw.FluentExtensions
  commentId: T:DrawnUi.Draw.FluentExtensions
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.FluentExtensions.html
  name: FluentExtensions
  nameWithType: FluentExtensions
  fullName: DrawnUi.Draw.FluentExtensions
- uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  isExternal: true
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  name: FindParent<T>(Element)
  nameWithType: StaticResourcesExtensions.FindParent<T>(Element)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.FindParent<T>(Microsoft.Maui.Controls.Element)
  nameWithType.vb: StaticResourcesExtensions.FindParent(Of T)(Element)
  fullName.vb: DrawnUi.Draw.StaticResourcesExtensions.FindParent(Of T)(Microsoft.Maui.Controls.Element)
  name.vb: FindParent(Of T)(Element)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: <
  - name: T
  - name: '>'
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: DrawnUi.Draw.StaticResourcesExtensions
  commentId: T:DrawnUi.Draw.StaticResourcesExtensions
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.StaticResourcesExtensions.html
  name: StaticResourcesExtensions
  nameWithType: StaticResourcesExtensions
  fullName: DrawnUi.Draw.StaticResourcesExtensions
- uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  name: FindMauiContext(Element, bool)
  nameWithType: InternalExtensions.FindMauiContext(Element, bool)
  fullName: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, bool)
  nameWithType.vb: InternalExtensions.FindMauiContext(Element, Boolean)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, Boolean)
  name.vb: FindMauiContext(Element, Boolean)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  commentId: M:DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  name: GetParentsPath(Element)
  nameWithType: InternalExtensions.GetParentsPath(Element)
  fullName: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  isExternal: true
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  name: GetAllWithMyselfParents(VisualElement)
  nameWithType: StaticResourcesExtensions.GetAllWithMyselfParents(VisualElement)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
    name: GetAllWithMyselfParents
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.GetAllWithMyselfParents(Microsoft.Maui.Controls.VisualElement)
    name: GetAllWithMyselfParents
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_GetAllWithMyselfParents_Microsoft_Maui_Controls_VisualElement_
  - name: (
  - uid: Microsoft.Maui.Controls.VisualElement
    name: VisualElement
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  commentId: M:DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  name: DisposeControlAndChildren(IView)
  nameWithType: InternalExtensions.DisposeControlAndChildren(IView)
  fullName: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
    name: DisposeControlAndChildren
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  - name: (
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.DisposeControlAndChildren(Microsoft.Maui.IView)
    name: DisposeControlAndChildren
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_DisposeControlAndChildren_Microsoft_Maui_IView_
  - name: (
  - uid: Microsoft.Maui.IView
    name: IView
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.iview
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Views.DrawnView.SetChildren(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})
  commentId: M:DrawnUi.Views.DrawnView.SetChildren(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SetChildren_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__
  name: SetChildren(IEnumerable<SkiaControl>)
  nameWithType: DrawnView.SetChildren(IEnumerable<SkiaControl>)
  fullName: DrawnUi.Views.DrawnView.SetChildren(System.Collections.Generic.IEnumerable<DrawnUi.Draw.SkiaControl>)
  nameWithType.vb: DrawnView.SetChildren(IEnumerable(Of SkiaControl))
  fullName.vb: DrawnUi.Views.DrawnView.SetChildren(System.Collections.Generic.IEnumerable(Of DrawnUi.Draw.SkiaControl))
  name.vb: SetChildren(IEnumerable(Of SkiaControl))
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.SetChildren(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})
    name: SetChildren
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SetChildren_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__
  - name: (
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: <
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: '>'
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.SetChildren(System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl})
    name: SetChildren
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_SetChildren_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__
  - name: (
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  - name: )
- uid: DrawnUi.Views.Canvas.SetChildren*
  commentId: Overload:DrawnUi.Views.Canvas.SetChildren
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_SetChildren_System_Collections_Generic_IEnumerable_DrawnUi_Draw_SkiaControl__
  name: SetChildren
  nameWithType: Canvas.SetChildren
  fullName: DrawnUi.Views.Canvas.SetChildren
- uid: System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}
  commentId: T:System.Collections.Generic.IEnumerable{DrawnUi.Draw.SkiaControl}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IEnumerable`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  name: IEnumerable<SkiaControl>
  nameWithType: IEnumerable<SkiaControl>
  fullName: System.Collections.Generic.IEnumerable<DrawnUi.Draw.SkiaControl>
  nameWithType.vb: IEnumerable(Of SkiaControl)
  fullName.vb: System.Collections.Generic.IEnumerable(Of DrawnUi.Draw.SkiaControl)
  name.vb: IEnumerable(Of SkiaControl)
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: <
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.Generic.IEnumerable`1
  commentId: T:System.Collections.Generic.IEnumerable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  name: IEnumerable<T>
  nameWithType: IEnumerable<T>
  fullName: System.Collections.Generic.IEnumerable<T>
  nameWithType.vb: IEnumerable(Of T)
  fullName.vb: System.Collections.Generic.IEnumerable(Of T)
  name.vb: IEnumerable(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: DrawnUi.Views.Canvas.DumpDebug*
  commentId: Overload:DrawnUi.Views.Canvas.DumpDebug
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_DumpDebug
  name: DumpDebug
  nameWithType: Canvas.DumpDebug
  fullName: DrawnUi.Views.Canvas.DumpDebug
- uid: DrawnUi.Views.DrawnView.OnChildAdded(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Views.DrawnView.OnChildAdded(DrawnUi.Draw.SkiaControl)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnChildAdded_DrawnUi_Draw_SkiaControl_
  name: OnChildAdded(SkiaControl)
  nameWithType: DrawnView.OnChildAdded(SkiaControl)
  fullName: DrawnUi.Views.DrawnView.OnChildAdded(DrawnUi.Draw.SkiaControl)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.OnChildAdded(DrawnUi.Draw.SkiaControl)
    name: OnChildAdded
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnChildAdded_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.OnChildAdded(DrawnUi.Draw.SkiaControl)
    name: OnChildAdded
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnChildAdded_DrawnUi_Draw_SkiaControl_
  - name: (
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: DrawnUi.Views.Canvas.OnChildAdded*
  commentId: Overload:DrawnUi.Views.Canvas.OnChildAdded
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_OnChildAdded_DrawnUi_Draw_SkiaControl_
  name: OnChildAdded
  nameWithType: Canvas.OnChildAdded
  fullName: DrawnUi.Views.Canvas.OnChildAdded
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: DrawnUi.Views.Canvas.SetContent*
  commentId: Overload:DrawnUi.Views.Canvas.SetContent
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_SetContent_DrawnUi_Draw_SkiaControl_
  name: SetContent
  nameWithType: Canvas.SetContent
  fullName: DrawnUi.Views.Canvas.SetContent
- uid: DrawnUi.Views.DrawnView.OnSizeChanged
  commentId: M:DrawnUi.Views.DrawnView.OnSizeChanged
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnSizeChanged
  name: OnSizeChanged()
  nameWithType: DrawnView.OnSizeChanged()
  fullName: DrawnUi.Views.DrawnView.OnSizeChanged()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.OnSizeChanged
    name: OnSizeChanged
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnSizeChanged
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.OnSizeChanged
    name: OnSizeChanged
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnSizeChanged
  - name: (
  - name: )
- uid: DrawnUi.Views.Canvas.OnSizeChanged*
  commentId: Overload:DrawnUi.Views.Canvas.OnSizeChanged
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_OnSizeChanged
  name: OnSizeChanged
  nameWithType: Canvas.OnSizeChanged
  fullName: DrawnUi.Views.Canvas.OnSizeChanged
- uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
  commentId: M:Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
  parent: Microsoft.Maui.Controls.VisualElement
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasure
  name: InvalidateMeasure()
  nameWithType: VisualElement.InvalidateMeasure()
  fullName: Microsoft.Maui.Controls.VisualElement.InvalidateMeasure()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
    name: InvalidateMeasure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasure
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.VisualElement.InvalidateMeasure
    name: InvalidateMeasure
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.visualelement.invalidatemeasure
  - name: (
  - name: )
- uid: DrawnUi.Views.Canvas.InvalidateMeasure*
  commentId: Overload:DrawnUi.Views.Canvas.InvalidateMeasure
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_InvalidateMeasure
  name: InvalidateMeasure
  nameWithType: Canvas.InvalidateMeasure
  fullName: DrawnUi.Views.Canvas.InvalidateMeasure
- uid: DrawnUi.Views.Canvas.AdaptSizeToContentIfNeeded*
  commentId: Overload:DrawnUi.Views.Canvas.AdaptSizeToContentIfNeeded
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_AdaptSizeToContentIfNeeded_System_Double_System_Double_System_Boolean_
  name: AdaptSizeToContentIfNeeded
  nameWithType: Canvas.AdaptSizeToContentIfNeeded
  fullName: DrawnUi.Views.Canvas.AdaptSizeToContentIfNeeded
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: Microsoft.Maui.Graphics.Size
  commentId: T:Microsoft.Maui.Graphics.Size
  parent: Microsoft.Maui.Graphics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.size
  name: Size
  nameWithType: Size
  fullName: Microsoft.Maui.Graphics.Size
- uid: Microsoft.Maui.Graphics
  commentId: N:Microsoft.Maui.Graphics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Graphics
  nameWithType: Microsoft.Maui.Graphics
  fullName: Microsoft.Maui.Graphics
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Graphics
    name: Graphics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Graphics
    name: Graphics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics
- uid: Microsoft.Maui.Controls.TemplatedView.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  commentId: M:Microsoft.Maui.Controls.TemplatedView.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  parent: Microsoft.Maui.Controls.TemplatedView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.arrangeoverride
  name: ArrangeOverride(Rect)
  nameWithType: TemplatedView.ArrangeOverride(Rect)
  fullName: Microsoft.Maui.Controls.TemplatedView.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.TemplatedView.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
    name: ArrangeOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.arrangeoverride
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.TemplatedView.ArrangeOverride(Microsoft.Maui.Graphics.Rect)
    name: ArrangeOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.arrangeoverride
  - name: (
  - uid: Microsoft.Maui.Graphics.Rect
    name: Rect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  - name: )
- uid: DrawnUi.Views.Canvas.ArrangeOverride*
  commentId: Overload:DrawnUi.Views.Canvas.ArrangeOverride
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_ArrangeOverride_Microsoft_Maui_Graphics_Rect_
  name: ArrangeOverride
  nameWithType: Canvas.ArrangeOverride
  fullName: DrawnUi.Views.Canvas.ArrangeOverride
- uid: Microsoft.Maui.Graphics.Rect
  commentId: T:Microsoft.Maui.Graphics.Rect
  parent: Microsoft.Maui.Graphics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.rect
  name: Rect
  nameWithType: Rect
  fullName: Microsoft.Maui.Graphics.Rect
- uid: Microsoft.Maui.Controls.TemplatedView.MeasureOverride(System.Double,System.Double)
  commentId: M:Microsoft.Maui.Controls.TemplatedView.MeasureOverride(System.Double,System.Double)
  parent: Microsoft.Maui.Controls.TemplatedView
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.measureoverride
  name: MeasureOverride(double, double)
  nameWithType: TemplatedView.MeasureOverride(double, double)
  fullName: Microsoft.Maui.Controls.TemplatedView.MeasureOverride(double, double)
  nameWithType.vb: TemplatedView.MeasureOverride(Double, Double)
  fullName.vb: Microsoft.Maui.Controls.TemplatedView.MeasureOverride(Double, Double)
  name.vb: MeasureOverride(Double, Double)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.TemplatedView.MeasureOverride(System.Double,System.Double)
    name: MeasureOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.measureoverride
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.TemplatedView.MeasureOverride(System.Double,System.Double)
    name: MeasureOverride
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.templatedview.measureoverride
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: ','
  - name: " "
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Views.Canvas.MeasureOverride*
  commentId: Overload:DrawnUi.Views.Canvas.MeasureOverride
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_MeasureOverride_System_Double_System_Double_
  name: MeasureOverride
  nameWithType: Canvas.MeasureOverride
  fullName: DrawnUi.Views.Canvas.MeasureOverride
- uid: DrawnUi.Views.DrawnView.Invalidate
  commentId: M:DrawnUi.Views.DrawnView.Invalidate
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Invalidate
  name: Invalidate()
  nameWithType: DrawnView.Invalidate()
  fullName: DrawnUi.Views.DrawnView.Invalidate()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.Invalidate
    name: Invalidate
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Invalidate
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.Invalidate
    name: Invalidate
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Invalidate
  - name: (
  - name: )
- uid: DrawnUi.Views.Canvas.Invalidate*
  commentId: Overload:DrawnUi.Views.Canvas.Invalidate
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_Invalidate
  name: Invalidate
  nameWithType: Canvas.Invalidate
  fullName: DrawnUi.Views.Canvas.Invalidate
- uid: DrawnUi.Views.Canvas.AdaptWidthContraintToRequest*
  commentId: Overload:DrawnUi.Views.Canvas.AdaptWidthContraintToRequest
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_AdaptWidthContraintToRequest_System_Double_
  name: AdaptWidthContraintToRequest
  nameWithType: Canvas.AdaptWidthContraintToRequest
  fullName: DrawnUi.Views.Canvas.AdaptWidthContraintToRequest
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Views.Canvas.AdaptHeightContraintToRequest*
  commentId: Overload:DrawnUi.Views.Canvas.AdaptHeightContraintToRequest
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_AdaptHeightContraintToRequest_System_Double_
  name: AdaptHeightContraintToRequest
  nameWithType: Canvas.AdaptHeightContraintToRequest
  fullName: DrawnUi.Views.Canvas.AdaptHeightContraintToRequest
- uid: DrawnUi.Views.DrawnView.Measure(System.Single,System.Single)
  commentId: M:DrawnUi.Views.DrawnView.Measure(System.Single,System.Single)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Measure_System_Single_System_Single_
  name: Measure(float, float)
  nameWithType: DrawnView.Measure(float, float)
  fullName: DrawnUi.Views.DrawnView.Measure(float, float)
  nameWithType.vb: DrawnView.Measure(Single, Single)
  fullName.vb: DrawnUi.Views.DrawnView.Measure(Single, Single)
  name.vb: Measure(Single, Single)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.Measure(System.Single,System.Single)
    name: Measure
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Measure_System_Single_System_Single_
  - name: (
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: ','
  - name: " "
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.Measure(System.Single,System.Single)
    name: Measure
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Measure_System_Single_System_Single_
  - name: (
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: ','
  - name: " "
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
- uid: DrawnUi.Views.Canvas.Measure*
  commentId: Overload:DrawnUi.Views.Canvas.Measure
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_Measure_System_Single_System_Single_
  name: Measure
  nameWithType: Canvas.Measure
  fullName: DrawnUi.Views.Canvas.Measure
- uid: DrawnUi.Draw.ScaledSize
  commentId: T:DrawnUi.Draw.ScaledSize
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ScaledSize.html
  name: ScaledSize
  nameWithType: ScaledSize
  fullName: DrawnUi.Draw.ScaledSize
- uid: DrawnUi.Views.Canvas.GetMeasuringRectForChildren*
  commentId: Overload:DrawnUi.Views.Canvas.GetMeasuringRectForChildren
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_GetMeasuringRectForChildren_System_Single_System_Single_System_Single_
  name: GetMeasuringRectForChildren
  nameWithType: Canvas.GetMeasuringRectForChildren
  fullName: DrawnUi.Views.Canvas.GetMeasuringRectForChildren
- uid: SkiaSharp.SKRect
  commentId: T:SkiaSharp.SKRect
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  name: SKRect
  nameWithType: SKRect
  fullName: SkiaSharp.SKRect
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Views.Canvas.AdaptWidthContraintToContentRequest*
  commentId: Overload:DrawnUi.Views.Canvas.AdaptWidthContraintToContentRequest
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_AdaptWidthContraintToContentRequest_System_Single_DrawnUi_Draw_ScaledSize_System_Double_
  name: AdaptWidthContraintToContentRequest
  nameWithType: Canvas.AdaptWidthContraintToContentRequest
  fullName: DrawnUi.Views.Canvas.AdaptWidthContraintToContentRequest
- uid: DrawnUi.Views.Canvas.AdaptHeightContraintToContentRequest*
  commentId: Overload:DrawnUi.Views.Canvas.AdaptHeightContraintToContentRequest
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_AdaptHeightContraintToContentRequest_System_Single_DrawnUi_Draw_ScaledSize_System_Double_
  name: AdaptHeightContraintToContentRequest
  nameWithType: Canvas.AdaptHeightContraintToContentRequest
  fullName: DrawnUi.Views.Canvas.AdaptHeightContraintToContentRequest
- uid: DrawnUi.Views.Canvas.AdaptSizeRequestToContent*
  commentId: Overload:DrawnUi.Views.Canvas.AdaptSizeRequestToContent
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_AdaptSizeRequestToContent_System_Double_System_Double_
  name: AdaptSizeRequestToContent
  nameWithType: Canvas.AdaptSizeRequestToContent
  fullName: DrawnUi.Views.Canvas.AdaptSizeRequestToContent
- uid: DrawnUi.Views.Canvas.ContentSize*
  commentId: Overload:DrawnUi.Views.Canvas.ContentSize
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_ContentSize
  name: ContentSize
  nameWithType: Canvas.ContentSize
  fullName: DrawnUi.Views.Canvas.ContentSize
- uid: DrawnUi.Views.Canvas.MeasureChild*
  commentId: Overload:DrawnUi.Views.Canvas.MeasureChild
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_MeasureChild_DrawnUi_Draw_SkiaControl_System_Double_System_Double_System_Double_
  name: MeasureChild
  nameWithType: Canvas.MeasureChild
  fullName: DrawnUi.Views.Canvas.MeasureChild
- uid: DrawnUi.Views.DrawnView.OnHandlerChanged
  commentId: M:DrawnUi.Views.DrawnView.OnHandlerChanged
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnHandlerChanged
  name: OnHandlerChanged()
  nameWithType: DrawnView.OnHandlerChanged()
  fullName: DrawnUi.Views.DrawnView.OnHandlerChanged()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.OnHandlerChanged
    name: OnHandlerChanged
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnHandlerChanged
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.OnHandlerChanged
    name: OnHandlerChanged
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnHandlerChanged
  - name: (
  - name: )
- uid: DrawnUi.Views.Canvas.OnHandlerChanged*
  commentId: Overload:DrawnUi.Views.Canvas.OnHandlerChanged
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_OnHandlerChanged
  name: OnHandlerChanged
  nameWithType: Canvas.OnHandlerChanged
  fullName: DrawnUi.Views.Canvas.OnHandlerChanged
- uid: DrawnUi.Views.Canvas.OnGesturesAttachChanged*
  commentId: Overload:DrawnUi.Views.Canvas.OnGesturesAttachChanged
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_OnGesturesAttachChanged
  name: OnGesturesAttachChanged
  nameWithType: Canvas.OnGesturesAttachChanged
  fullName: DrawnUi.Views.Canvas.OnGesturesAttachChanged
- uid: DrawnUi.Views.Canvas.CreateDebugPointer*
  commentId: Overload:DrawnUi.Views.Canvas.CreateDebugPointer
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_CreateDebugPointer
  name: CreateDebugPointer
  nameWithType: Canvas.CreateDebugPointer
  fullName: DrawnUi.Views.Canvas.CreateDebugPointer
- uid: DrawnUi.Draw.SkiaSvg
  commentId: T:DrawnUi.Draw.SkiaSvg
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaSvg.html
  name: SkiaSvg
  nameWithType: SkiaSvg
  fullName: DrawnUi.Draw.SkiaSvg
- uid: DrawnUi.Views.Canvas.DebugPointer*
  commentId: Overload:DrawnUi.Views.Canvas.DebugPointer
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_DebugPointer
  name: DebugPointer
  nameWithType: Canvas.DebugPointer
  fullName: DrawnUi.Views.Canvas.DebugPointer
- uid: DrawnUi.Views.DrawnView.OnDisposing
  commentId: M:DrawnUi.Views.DrawnView.OnDisposing
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnDisposing
  name: OnDisposing()
  nameWithType: DrawnView.OnDisposing()
  fullName: DrawnUi.Views.DrawnView.OnDisposing()
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.OnDisposing
    name: OnDisposing
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnDisposing
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.OnDisposing
    name: OnDisposing
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnDisposing
  - name: (
  - name: )
- uid: DrawnUi.Views.Canvas.OnDisposing*
  commentId: Overload:DrawnUi.Views.Canvas.OnDisposing
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_OnDisposing
  name: OnDisposing
  nameWithType: Canvas.OnDisposing
  fullName: DrawnUi.Views.Canvas.OnDisposing
- uid: DrawnUi.Draw.DrawingContext
  commentId: T:DrawnUi.Draw.DrawingContext
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawingContext.html
  name: DrawingContext
  nameWithType: DrawingContext
  fullName: DrawnUi.Draw.DrawingContext
- uid: DrawnUi.Views.Canvas.ReceivedInput*
  commentId: Overload:DrawnUi.Views.Canvas.ReceivedInput
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_ReceivedInput
  name: ReceivedInput
  nameWithType: Canvas.ReceivedInput
  fullName: DrawnUi.Views.Canvas.ReceivedInput
- uid: System.Collections.Concurrent.ConcurrentBag{DrawnUi.Draw.ISkiaGestureListener}
  commentId: T:System.Collections.Concurrent.ConcurrentBag{DrawnUi.Draw.ISkiaGestureListener}
  parent: System.Collections.Concurrent
  definition: System.Collections.Concurrent.ConcurrentBag`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.concurrent.concurrentbag-1
  name: ConcurrentBag<ISkiaGestureListener>
  nameWithType: ConcurrentBag<ISkiaGestureListener>
  fullName: System.Collections.Concurrent.ConcurrentBag<DrawnUi.Draw.ISkiaGestureListener>
  nameWithType.vb: ConcurrentBag(Of ISkiaGestureListener)
  fullName.vb: System.Collections.Concurrent.ConcurrentBag(Of DrawnUi.Draw.ISkiaGestureListener)
  name.vb: ConcurrentBag(Of ISkiaGestureListener)
  spec.csharp:
  - uid: System.Collections.Concurrent.ConcurrentBag`1
    name: ConcurrentBag
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.concurrent.concurrentbag-1
  - name: <
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Concurrent.ConcurrentBag`1
    name: ConcurrentBag
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.concurrent.concurrentbag-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: )
- uid: System.Collections.Concurrent.ConcurrentBag`1
  commentId: T:System.Collections.Concurrent.ConcurrentBag`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.concurrent.concurrentbag-1
  name: ConcurrentBag<T>
  nameWithType: ConcurrentBag<T>
  fullName: System.Collections.Concurrent.ConcurrentBag<T>
  nameWithType.vb: ConcurrentBag(Of T)
  fullName.vb: System.Collections.Concurrent.ConcurrentBag(Of T)
  name.vb: ConcurrentBag(Of T)
  spec.csharp:
  - uid: System.Collections.Concurrent.ConcurrentBag`1
    name: ConcurrentBag
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.concurrent.concurrentbag-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Concurrent.ConcurrentBag`1
    name: ConcurrentBag
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.concurrent.concurrentbag-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Concurrent
  commentId: N:System.Collections.Concurrent
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Concurrent
  nameWithType: System.Collections.Concurrent
  fullName: System.Collections.Concurrent
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Concurrent
    name: Concurrent
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.concurrent
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Concurrent
    name: Concurrent
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.concurrent
- uid: DrawnUi.Views.Canvas.HadInput*
  commentId: Overload:DrawnUi.Views.Canvas.HadInput
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_HadInput
  name: HadInput
  nameWithType: Canvas.HadInput
  fullName: DrawnUi.Views.Canvas.HadInput
- uid: System.Collections.Generic.Dictionary{System.Guid,DrawnUi.Draw.ISkiaGestureListener}
  commentId: T:System.Collections.Generic.Dictionary{System.Guid,DrawnUi.Draw.ISkiaGestureListener}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.Dictionary`2
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  name: Dictionary<Guid, ISkiaGestureListener>
  nameWithType: Dictionary<Guid, ISkiaGestureListener>
  fullName: System.Collections.Generic.Dictionary<System.Guid, DrawnUi.Draw.ISkiaGestureListener>
  nameWithType.vb: Dictionary(Of Guid, ISkiaGestureListener)
  fullName.vb: System.Collections.Generic.Dictionary(Of System.Guid, DrawnUi.Draw.ISkiaGestureListener)
  name.vb: Dictionary(Of Guid, ISkiaGestureListener)
  spec.csharp:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: <
  - uid: System.Guid
    name: Guid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.guid
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: (
  - name: Of
  - name: " "
  - uid: System.Guid
    name: Guid
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.guid
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: )
- uid: System.Collections.Generic.Dictionary`2
  commentId: T:System.Collections.Generic.Dictionary`2
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  name: Dictionary<TKey, TValue>
  nameWithType: Dictionary<TKey, TValue>
  fullName: System.Collections.Generic.Dictionary<TKey, TValue>
  nameWithType.vb: Dictionary(Of TKey, TValue)
  fullName.vb: System.Collections.Generic.Dictionary(Of TKey, TValue)
  name.vb: Dictionary(Of TKey, TValue)
  spec.csharp:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: <
  - name: TKey
  - name: ','
  - name: " "
  - name: TValue
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.Dictionary`2
    name: Dictionary
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2
  - name: (
  - name: Of
  - name: " "
  - name: TKey
  - name: ','
  - name: " "
  - name: TValue
  - name: )
- uid: DrawnUi.Views.Canvas.IsSavedGesture*
  commentId: Overload:DrawnUi.Views.Canvas.IsSavedGesture
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_IsSavedGesture_AppoMobi_Maui_Gestures_TouchActionResult_
  name: IsSavedGesture
  nameWithType: Canvas.IsSavedGesture
  fullName: DrawnUi.Views.Canvas.IsSavedGesture
- uid: AppoMobi.Maui.Gestures.TouchActionResult
  commentId: T:AppoMobi.Maui.Gestures.TouchActionResult
  parent: AppoMobi.Maui.Gestures
  isExternal: true
  name: TouchActionResult
  nameWithType: TouchActionResult
  fullName: AppoMobi.Maui.Gestures.TouchActionResult
- uid: DrawnUi.Views.Canvas.ProcessGestures*
  commentId: Overload:DrawnUi.Views.Canvas.ProcessGestures
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_ProcessGestures_DrawnUi_Draw_SkiaGesturesParameters_
  name: ProcessGestures
  nameWithType: Canvas.ProcessGestures
  fullName: DrawnUi.Views.Canvas.ProcessGestures
- uid: DrawnUi.Draw.SkiaGesturesParameters
  commentId: T:DrawnUi.Draw.SkiaGesturesParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaGesturesParameters.html
  name: SkiaGesturesParameters
  nameWithType: SkiaGesturesParameters
  fullName: DrawnUi.Draw.SkiaGesturesParameters
- uid: DrawnUi.Views.Canvas.SignalInput*
  commentId: Overload:DrawnUi.Views.Canvas.SignalInput
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_SignalInput_DrawnUi_Draw_ISkiaGestureListener_AppoMobi_Maui_Gestures_TouchActionResult_
  name: SignalInput
  nameWithType: Canvas.SignalInput
  fullName: DrawnUi.Views.Canvas.SignalInput
- uid: DrawnUi.Draw.ISkiaGestureListener
  commentId: T:DrawnUi.Draw.ISkiaGestureListener
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaGestureListener.html
  name: ISkiaGestureListener
  nameWithType: ISkiaGestureListener
  fullName: DrawnUi.Draw.ISkiaGestureListener
- uid: System.EventHandler
  commentId: T:System.EventHandler
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.eventhandler
  name: EventHandler
  nameWithType: EventHandler
  fullName: System.EventHandler
- uid: DrawnUi.Views.Canvas.OnGestureEvent*
  commentId: Overload:DrawnUi.Views.Canvas.OnGestureEvent
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_OnGestureEvent_AppoMobi_Maui_Gestures_TouchActionType_AppoMobi_Maui_Gestures_TouchActionEventArgs_AppoMobi_Maui_Gestures_TouchActionResult_
  name: OnGestureEvent
  nameWithType: Canvas.OnGestureEvent
  fullName: DrawnUi.Views.Canvas.OnGestureEvent
- uid: AppoMobi.Maui.Gestures.IGestureListener.OnGestureEvent(AppoMobi.Maui.Gestures.TouchActionType,AppoMobi.Maui.Gestures.TouchActionEventArgs,AppoMobi.Maui.Gestures.TouchActionResult)
  commentId: M:AppoMobi.Maui.Gestures.IGestureListener.OnGestureEvent(AppoMobi.Maui.Gestures.TouchActionType,AppoMobi.Maui.Gestures.TouchActionEventArgs,AppoMobi.Maui.Gestures.TouchActionResult)
  parent: AppoMobi.Maui.Gestures.IGestureListener
  isExternal: true
  name: OnGestureEvent(TouchActionType, TouchActionEventArgs, TouchActionResult)
  nameWithType: IGestureListener.OnGestureEvent(TouchActionType, TouchActionEventArgs, TouchActionResult)
  fullName: AppoMobi.Maui.Gestures.IGestureListener.OnGestureEvent(AppoMobi.Maui.Gestures.TouchActionType, AppoMobi.Maui.Gestures.TouchActionEventArgs, AppoMobi.Maui.Gestures.TouchActionResult)
  spec.csharp:
  - uid: AppoMobi.Maui.Gestures.IGestureListener.OnGestureEvent(AppoMobi.Maui.Gestures.TouchActionType,AppoMobi.Maui.Gestures.TouchActionEventArgs,AppoMobi.Maui.Gestures.TouchActionResult)
    name: OnGestureEvent
    isExternal: true
  - name: (
  - uid: AppoMobi.Maui.Gestures.TouchActionType
    name: TouchActionType
    isExternal: true
  - name: ','
  - name: " "
  - uid: AppoMobi.Maui.Gestures.TouchActionEventArgs
    name: TouchActionEventArgs
    isExternal: true
  - name: ','
  - name: " "
  - uid: AppoMobi.Maui.Gestures.TouchActionResult
    name: TouchActionResult
    isExternal: true
  - name: )
  spec.vb:
  - uid: AppoMobi.Maui.Gestures.IGestureListener.OnGestureEvent(AppoMobi.Maui.Gestures.TouchActionType,AppoMobi.Maui.Gestures.TouchActionEventArgs,AppoMobi.Maui.Gestures.TouchActionResult)
    name: OnGestureEvent
    isExternal: true
  - name: (
  - uid: AppoMobi.Maui.Gestures.TouchActionType
    name: TouchActionType
    isExternal: true
  - name: ','
  - name: " "
  - uid: AppoMobi.Maui.Gestures.TouchActionEventArgs
    name: TouchActionEventArgs
    isExternal: true
  - name: ','
  - name: " "
  - uid: AppoMobi.Maui.Gestures.TouchActionResult
    name: TouchActionResult
    isExternal: true
  - name: )
- uid: AppoMobi.Maui.Gestures.TouchActionType
  commentId: T:AppoMobi.Maui.Gestures.TouchActionType
  parent: AppoMobi.Maui.Gestures
  isExternal: true
  name: TouchActionType
  nameWithType: TouchActionType
  fullName: AppoMobi.Maui.Gestures.TouchActionType
- uid: AppoMobi.Maui.Gestures.TouchActionEventArgs
  commentId: T:AppoMobi.Maui.Gestures.TouchActionEventArgs
  parent: AppoMobi.Maui.Gestures
  isExternal: true
  name: TouchActionEventArgs
  nameWithType: TouchActionEventArgs
  fullName: AppoMobi.Maui.Gestures.TouchActionEventArgs
- uid: DrawnUi.Views.Canvas.BreakLine*
  commentId: Overload:DrawnUi.Views.Canvas.BreakLine
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_BreakLine
  name: BreakLine
  nameWithType: Canvas.BreakLine
  fullName: DrawnUi.Views.Canvas.BreakLine
- uid: System.Collections.Generic.List{System.Int32}
  commentId: T:System.Collections.Generic.List{System.Int32}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.List`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<int>
  nameWithType: List<int>
  fullName: System.Collections.Generic.List<int>
  nameWithType.vb: List(Of Integer)
  fullName.vb: System.Collections.Generic.List(Of Integer)
  name.vb: List(Of Integer)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Collections.Generic.List`1
  commentId: T:System.Collections.Generic.List`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<T>
  nameWithType: List<T>
  fullName: System.Collections.Generic.List<T>
  nameWithType.vb: List(Of T)
  fullName.vb: System.Collections.Generic.List(Of T)
  name.vb: List(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Views.Canvas.#ctor*
  commentId: Overload:DrawnUi.Views.Canvas.#ctor
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas__ctor
  name: Canvas
  nameWithType: Canvas.Canvas
  fullName: DrawnUi.Views.Canvas.Canvas
  nameWithType.vb: Canvas.New
  fullName.vb: DrawnUi.Views.Canvas.New
  name.vb: New
- uid: DrawnUi.Views.Canvas.Clear*
  commentId: Overload:DrawnUi.Views.Canvas.Clear
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_Clear
  name: Clear
  nameWithType: Canvas.Clear
  fullName: DrawnUi.Views.Canvas.Clear
- uid: DrawnUi.Views.Canvas.PlayRippleAnimation*
  commentId: Overload:DrawnUi.Views.Canvas.PlayRippleAnimation
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_PlayRippleAnimation_Microsoft_Maui_Graphics_Color_System_Double_System_Double_System_Boolean_
  name: PlayRippleAnimation
  nameWithType: Canvas.PlayRippleAnimation
  fullName: DrawnUi.Views.Canvas.PlayRippleAnimation
- uid: Microsoft.Maui.Graphics.Color
  commentId: T:Microsoft.Maui.Graphics.Color
  parent: Microsoft.Maui.Graphics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color
  name: Color
  nameWithType: Color
  fullName: Microsoft.Maui.Graphics.Color
- uid: DrawnUi.Views.Canvas.PlayShimmerAnimation*
  commentId: Overload:DrawnUi.Views.Canvas.PlayShimmerAnimation
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_PlayShimmerAnimation_Microsoft_Maui_Graphics_Color_System_Single_System_Single_System_Int32_System_Boolean_
  name: PlayShimmerAnimation
  nameWithType: Canvas.PlayShimmerAnimation
  fullName: DrawnUi.Views.Canvas.PlayShimmerAnimation
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Views.Canvas.TimeLastGC*
  commentId: Overload:DrawnUi.Views.Canvas.TimeLastGC
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_TimeLastGC
  name: TimeLastGC
  nameWithType: Canvas.TimeLastGC
  fullName: DrawnUi.Views.Canvas.TimeLastGC
- uid: System.Int64
  commentId: T:System.Int64
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int64
  name: long
  nameWithType: long
  fullName: long
  nameWithType.vb: Long
  fullName.vb: Long
  name.vb: Long
- uid: DrawnUi.Views.Canvas.DelayNanosBetweenGC*
  commentId: Overload:DrawnUi.Views.Canvas.DelayNanosBetweenGC
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_DelayNanosBetweenGC
  name: DelayNanosBetweenGC
  nameWithType: Canvas.DelayNanosBetweenGC
  fullName: DrawnUi.Views.Canvas.DelayNanosBetweenGC
- uid: DrawnUi.Views.Canvas.CollectGarbage*
  commentId: Overload:DrawnUi.Views.Canvas.CollectGarbage
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_CollectGarbage_System_Int64_
  name: CollectGarbage
  nameWithType: Canvas.CollectGarbage
  fullName: DrawnUi.Views.Canvas.CollectGarbage
- uid: DrawnUi.Views.DrawnView.OnPropertyChanged(System.String)
  commentId: M:DrawnUi.Views.DrawnView.OnPropertyChanged(System.String)
  parent: DrawnUi.Views.DrawnView
  isExternal: true
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnPropertyChanged_System_String_
  name: OnPropertyChanged(string)
  nameWithType: DrawnView.OnPropertyChanged(string)
  fullName: DrawnUi.Views.DrawnView.OnPropertyChanged(string)
  nameWithType.vb: DrawnView.OnPropertyChanged(String)
  fullName.vb: DrawnUi.Views.DrawnView.OnPropertyChanged(String)
  name.vb: OnPropertyChanged(String)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.OnPropertyChanged(System.String)
    name: OnPropertyChanged
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnPropertyChanged_System_String_
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.OnPropertyChanged(System.String)
    name: OnPropertyChanged
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_OnPropertyChanged_System_String_
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: DrawnUi.Views.Canvas.OnPropertyChanged*
  commentId: Overload:DrawnUi.Views.Canvas.OnPropertyChanged
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_OnPropertyChanged_System_String_
  name: OnPropertyChanged
  nameWithType: Canvas.OnPropertyChanged
  fullName: DrawnUi.Views.Canvas.OnPropertyChanged
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: Microsoft.Maui.Controls.BindableProperty
  commentId: T:Microsoft.Maui.Controls.BindableProperty
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  name: BindableProperty
  nameWithType: BindableProperty
  fullName: Microsoft.Maui.Controls.BindableProperty
- uid: DrawnUi.Views.Canvas.GesturesDebugColor*
  commentId: Overload:DrawnUi.Views.Canvas.GesturesDebugColor
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_GesturesDebugColor
  name: GesturesDebugColor
  nameWithType: Canvas.GesturesDebugColor
  fullName: DrawnUi.Views.Canvas.GesturesDebugColor
- uid: DrawnUi.Views.Canvas.ReserveSpaceAround*
  commentId: Overload:DrawnUi.Views.Canvas.ReserveSpaceAround
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_ReserveSpaceAround
  name: ReserveSpaceAround
  nameWithType: Canvas.ReserveSpaceAround
  fullName: DrawnUi.Views.Canvas.ReserveSpaceAround
- uid: Microsoft.Maui.Thickness
  commentId: T:Microsoft.Maui.Thickness
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.thickness
  name: Thickness
  nameWithType: Thickness
  fullName: Microsoft.Maui.Thickness
- uid: DrawnUi.Views.Canvas.Gestures*
  commentId: Overload:DrawnUi.Views.Canvas.Gestures
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_Gestures
  name: Gestures
  nameWithType: Canvas.Gestures
  fullName: DrawnUi.Views.Canvas.Gestures
- uid: DrawnUi.Draw.GesturesMode
  commentId: T:DrawnUi.Draw.GesturesMode
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.GesturesMode.html
  name: GesturesMode
  nameWithType: GesturesMode
  fullName: DrawnUi.Draw.GesturesMode
- uid: DrawnUi.Views.Canvas.Content*
  commentId: Overload:DrawnUi.Views.Canvas.Content
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_Content
  name: Content
  nameWithType: Canvas.Content
  fullName: DrawnUi.Views.Canvas.Content
- uid: DrawnUi.Draw.ISkiaControl
  commentId: T:DrawnUi.Draw.ISkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaControl.html
  name: ISkiaControl
  nameWithType: ISkiaControl
  fullName: DrawnUi.Draw.ISkiaControl
- uid: Microsoft.Maui.Controls.Element.OnParentChanged
  commentId: M:Microsoft.Maui.Controls.Element.OnParentChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged
  name: OnParentChanged()
  nameWithType: Element.OnParentChanged()
  fullName: Microsoft.Maui.Controls.Element.OnParentChanged()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanged
    name: OnParentChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanged
    name: OnParentChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged
  - name: (
  - name: )
- uid: DrawnUi.Views.Canvas.OnParentChanged*
  commentId: Overload:DrawnUi.Views.Canvas.OnParentChanged
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_OnParentChanged
  name: OnParentChanged
  nameWithType: Canvas.OnParentChanged
  fullName: DrawnUi.Views.Canvas.OnParentChanged
- uid: DrawnUi.Views.Canvas.EnableUpdates*
  commentId: Overload:DrawnUi.Views.Canvas.EnableUpdates
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_EnableUpdates
  name: EnableUpdates
  nameWithType: Canvas.EnableUpdates
  fullName: DrawnUi.Views.Canvas.EnableUpdates
- uid: DrawnUi.Views.Canvas.DisableUpdates*
  commentId: Overload:DrawnUi.Views.Canvas.DisableUpdates
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_DisableUpdates
  name: DisableUpdates
  nameWithType: Canvas.DisableUpdates
  fullName: DrawnUi.Views.Canvas.DisableUpdates
- uid: DrawnUi.Views.DrawnView.Draw(DrawnUi.Draw.DrawingContext)
  commentId: M:DrawnUi.Views.DrawnView.Draw(DrawnUi.Draw.DrawingContext)
  parent: DrawnUi.Views.DrawnView
  href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Draw_DrawnUi_Draw_DrawingContext_
  name: Draw(DrawingContext)
  nameWithType: DrawnView.Draw(DrawingContext)
  fullName: DrawnUi.Views.DrawnView.Draw(DrawnUi.Draw.DrawingContext)
  spec.csharp:
  - uid: DrawnUi.Views.DrawnView.Draw(DrawnUi.Draw.DrawingContext)
    name: Draw
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Draw_DrawnUi_Draw_DrawingContext_
  - name: (
  - uid: DrawnUi.Draw.DrawingContext
    name: DrawingContext
    href: DrawnUi.Draw.DrawingContext.html
  - name: )
  spec.vb:
  - uid: DrawnUi.Views.DrawnView.Draw(DrawnUi.Draw.DrawingContext)
    name: Draw
    href: DrawnUi.Views.DrawnView.html#DrawnUi_Views_DrawnView_Draw_DrawnUi_Draw_DrawingContext_
  - name: (
  - uid: DrawnUi.Draw.DrawingContext
    name: DrawingContext
    href: DrawnUi.Draw.DrawingContext.html
  - name: )
- uid: DrawnUi.Views.Canvas.Draw*
  commentId: Overload:DrawnUi.Views.Canvas.Draw
  href: DrawnUi.Views.Canvas.html#DrawnUi_Views_Canvas_Draw_DrawnUi_Draw_DrawingContext_
  name: Draw
  nameWithType: Canvas.Draw
  fullName: DrawnUi.Views.Canvas.Draw

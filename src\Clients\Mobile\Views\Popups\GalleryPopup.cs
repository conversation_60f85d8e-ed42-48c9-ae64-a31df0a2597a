﻿using System.Collections;
using System.Windows.Input;
using DrawnUi.Controls;

namespace AppoMobi.Mobile;

public interface IFullscreenGalleryManager
{
    public ICommand CommandCloseGallery { get; }
    public ICommand CommandPressingImage { get; }
    public int SelectedGalleryIndex { get; set; }
    public IList GalleryItems { get; }
}

public class GalleryPopup : AppPopup
{
    public GalleryPopup(IFullscreenGalleryManager vm)
    {
        this.Model = vm;
        BindingContext = vm;

        BlockGesturesBelow = true;

        //BackgroundColor = Color.Parse("#000000");

        Tapped += (s, a) =>
        {
            //Model.CommandCloseGallery.Execute(null);
            App.GoBack();
        };
        Children = new List<SkiaControl>()
        {
            new SkiaCarousel()
            {
                HorizontalOptions = LayoutOptions.Fill,
                VerticalOptions = LayoutOptions.Fill,
                ItemsSource = Model.GalleryItems,
                Spacing = 16,
                ItemTemplate = new DataTemplate(() =>
                {
                    var cell = new SkiaLayer()
                    {
                        UseCache = SkiaCacheType.Operations,
                        Children = new List<SkiaControl>()
                        {
                            //todo placeholder svg
                            
                            new SkiaImage()
                            {
                                Aspect = TransformAspect.AspectFitFill,
                                EraseChangedContent = true,
                                HorizontalOptions = LayoutOptions.Fill,
                                VerticalOptions = LayoutOptions.Center,
                                LoadSourceOnFirstDraw = false,
                                UseCache = SkiaCacheType.Image, //remove this if gonna ZOOM
                                RescalingQuality = SKFilterQuality.Low
                            }.OnBindingContextSet((me, vm) =>
                            {
                                var ctx = vm as GalleryImage;
                                if (ctx != null)
                                {
                                    me.Source = ctx.ImageMain.Large;
                                }
                            }),

                            new SkiaMarkdownLabel()
                            {
                                HorizontalOptions = LayoutOptions.Fill,
                                HorizontalTextAlignment = DrawTextAlignment.Center,
                                VerticalOptions = LayoutOptions.End,
                                BackgroundColor = Color.Parse("#33000000"),
                                UseCache = SkiaCacheType.Image,
                                TextColor = Colors.White,
                                Margin = new(0,0,0,50),
                            }.OnBindingContextSet((me, vm) =>
                            {
                                var ctx = vm as GalleryImage;
                                if (ctx != null)
                                {
                                    me.Text = ctx.Desc;
                                }
                            }),

                        }
                    };

                    return cell;
                })
            }.Adapt((me) =>
            {
                me.SelectedIndex = Model.SelectedGalleryIndex;
            })
        };
    }

    public override void OnDisposing()
    {
        base.OnDisposing();

        Model = null;
    }

    protected IFullscreenGalleryManager Model { get; set; }
}
# Progress Controls

DrawnUi provides progress controls for showing loading states and progress indicators.

## SkiaProgress

SkiaProgress is a control for displaying progress indicators and loading states.

### Basic Usage

```xml
<draw:SkiaProgress
    Progress="0.5"
    WidthRequest="200"
    HeightRequest="20"
    BackgroundColor="LightGray"
    ProgressColor="Blue" />
```

### Key Properties

| Property | Type | Description |
|----------|------|-------------|
| `Progress` | double | Progress value between 0.0 and 1.0 |
| `ProgressColor` | Color | Color of the progress bar |
| `BackgroundColor` | Color | Background color of the progress track |
| `IsIndeterminate` | bool | Whether to show indeterminate progress |

### Indeterminate Progress

For loading states where progress is unknown:

```xml
<draw:SkiaProgress
    IsIndeterminate="True"
    WidthRequest="200"
    HeightRequest="20"
    ProgressColor="Blue" />
```

## RefreshIndicator

RefreshIndicator can use Lottie animations and any content as an activity indicator for scroll refresh views.

### Basic Usage

```xml
<draw:RefreshIndicator
    IsRefreshing="{Binding IsLoading}"
    RefreshCommand="{Binding RefreshCommand}">
    
    <draw:SkiaLottie
        Source="loading.json"
        AutoPlay="True"
        Repeat="-1"
        WidthRequest="40"
        HeightRequest="40" />
        
</draw:RefreshIndicator>
```

### With SkiaScroll

```xml
<draw:SkiaScroll RefreshIndicator="{x:Reference MyRefreshIndicator}">
    <!-- Your scrollable content -->
    <draw:SkiaLayout Type="Column">
        <!-- Content items -->
    </draw:SkiaLayout>
</draw:SkiaScroll>

<draw:RefreshIndicator x:Name="MyRefreshIndicator"
    IsRefreshing="{Binding IsLoading}"
    RefreshCommand="{Binding RefreshCommand}">
    
    <draw:SkiaLabel Text="Pull to refresh..." />
    
</draw:RefreshIndicator>
```

### Key Properties

| Property | Type | Description |
|----------|------|-------------|
| `IsRefreshing` | bool | Whether refresh is currently active |
| `RefreshCommand` | ICommand | Command to execute when refresh is triggered |
| `Content` | SkiaControl | The visual content of the refresh indicator |

## Examples

### Animated Progress with Lottie

```xml
<draw:SkiaLayout Type="Column" Spacing="20">
    
    <!-- Traditional progress bar -->
    <draw:SkiaProgress
        Progress="{Binding DownloadProgress}"
        WidthRequest="300"
        HeightRequest="8"
        BackgroundColor="LightGray"
        ProgressColor="Green"
        CornerRadius="4" />
    
    <!-- Lottie-based loading indicator -->
    <draw:RefreshIndicator IsRefreshing="{Binding IsLoading}">
        <draw:SkiaLottie
            Source="spinner.json"
            AutoPlay="True"
            Repeat="-1"
            WidthRequest="60"
            HeightRequest="60"
            ColorTint="{StaticResource Primary}" />
    </draw:RefreshIndicator>
    
</draw:SkiaLayout>
```

### Code-Behind Progress Control

```csharp
public partial class ProgressPage : ContentPage
{
    private SkiaProgress progressBar;
    
    public ProgressPage()
    {
        InitializeComponent();
        CreateProgressBar();
        SimulateProgress();
    }
    
    private void CreateProgressBar()
    {
        progressBar = new SkiaProgress()
        {
            Progress = 0,
            WidthRequest = 300,
            HeightRequest = 20,
            BackgroundColor = Colors.LightGray,
            ProgressColor = Colors.Blue,
            CornerRadius = 10
        };
        
        MainLayout.Children.Add(progressBar);
    }
    
    private async void SimulateProgress()
    {
        for (int i = 0; i <= 100; i++)
        {
            progressBar.Progress = i / 100.0;
            await Task.Delay(50);
        }
    }
}
```

## Performance Tips

- Use `IsIndeterminate="True"` for unknown progress durations
- Combine with Lottie animations for engaging loading experiences
- Consider using RefreshIndicator with SkiaScroll for pull-to-refresh functionality
- Progress animations are optimized for smooth 60fps rendering

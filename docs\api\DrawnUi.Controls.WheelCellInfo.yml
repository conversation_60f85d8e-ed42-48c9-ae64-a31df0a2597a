### YamlMime:ManagedReference
items:
- uid: DrawnUi.Controls.WheelCellInfo
  commentId: T:DrawnUi.Controls.WheelCellInfo
  id: WheelCellInfo
  parent: DrawnUi.Controls
  children:
  - DrawnUi.Controls.WheelCellInfo.Destination
  - DrawnUi.Controls.WheelCellInfo.Index
  - DrawnUi.Controls.WheelCellInfo.IsSelected
  - DrawnUi.Controls.WheelCellInfo.Offset
  - DrawnUi.Controls.WheelCellInfo.Opacity
  - DrawnUi.Controls.WheelCellInfo.Transform
  - DrawnUi.Controls.WheelCellInfo.View
  - DrawnUi.Controls.WheelCellInfo.WasMeasured
  langs:
  - csharp
  - vb
  name: WheelCellInfo
  nameWithType: WheelCellInfo
  fullName: DrawnUi.Controls.WheelCellInfo
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PickerWheel/WheelCellInfo.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WheelCellInfo
    path: ../src/Maui/DrawnUi/Controls/PickerWheel/WheelCellInfo.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public class WheelCellInfo
    content.vb: Public Class WheelCellInfo
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Controls.WheelCellInfo.View
  commentId: P:DrawnUi.Controls.WheelCellInfo.View
  id: View
  parent: DrawnUi.Controls.WheelCellInfo
  langs:
  - csharp
  - vb
  name: View
  nameWithType: WheelCellInfo.View
  fullName: DrawnUi.Controls.WheelCellInfo.View
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PickerWheel/WheelCellInfo.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: View
    path: ../src/Maui/DrawnUi/Controls/PickerWheel/WheelCellInfo.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public SkiaControl View { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.SkiaControl
    content.vb: Public Property View As SkiaControl
  overload: DrawnUi.Controls.WheelCellInfo.View*
- uid: DrawnUi.Controls.WheelCellInfo.Index
  commentId: P:DrawnUi.Controls.WheelCellInfo.Index
  id: Index
  parent: DrawnUi.Controls.WheelCellInfo
  langs:
  - csharp
  - vb
  name: Index
  nameWithType: WheelCellInfo.Index
  fullName: DrawnUi.Controls.WheelCellInfo.Index
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PickerWheel/WheelCellInfo.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Index
    path: ../src/Maui/DrawnUi/Controls/PickerWheel/WheelCellInfo.cs
    startLine: 6
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public int Index { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property Index As Integer
  overload: DrawnUi.Controls.WheelCellInfo.Index*
- uid: DrawnUi.Controls.WheelCellInfo.WasMeasured
  commentId: P:DrawnUi.Controls.WheelCellInfo.WasMeasured
  id: WasMeasured
  parent: DrawnUi.Controls.WheelCellInfo
  langs:
  - csharp
  - vb
  name: WasMeasured
  nameWithType: WheelCellInfo.WasMeasured
  fullName: DrawnUi.Controls.WheelCellInfo.WasMeasured
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PickerWheel/WheelCellInfo.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WasMeasured
    path: ../src/Maui/DrawnUi/Controls/PickerWheel/WheelCellInfo.cs
    startLine: 8
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public bool WasMeasured { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property WasMeasured As Boolean
  overload: DrawnUi.Controls.WheelCellInfo.WasMeasured*
- uid: DrawnUi.Controls.WheelCellInfo.Destination
  commentId: P:DrawnUi.Controls.WheelCellInfo.Destination
  id: Destination
  parent: DrawnUi.Controls.WheelCellInfo
  langs:
  - csharp
  - vb
  name: Destination
  nameWithType: WheelCellInfo.Destination
  fullName: DrawnUi.Controls.WheelCellInfo.Destination
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PickerWheel/WheelCellInfo.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Destination
    path: ../src/Maui/DrawnUi/Controls/PickerWheel/WheelCellInfo.cs
    startLine: 10
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public SKRect Destination { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKRect
    content.vb: Public Property Destination As SKRect
  overload: DrawnUi.Controls.WheelCellInfo.Destination*
- uid: DrawnUi.Controls.WheelCellInfo.Offset
  commentId: P:DrawnUi.Controls.WheelCellInfo.Offset
  id: Offset
  parent: DrawnUi.Controls.WheelCellInfo
  langs:
  - csharp
  - vb
  name: Offset
  nameWithType: WheelCellInfo.Offset
  fullName: DrawnUi.Controls.WheelCellInfo.Offset
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PickerWheel/WheelCellInfo.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Offset
    path: ../src/Maui/DrawnUi/Controls/PickerWheel/WheelCellInfo.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public float Offset { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property Offset As Single
  overload: DrawnUi.Controls.WheelCellInfo.Offset*
- uid: DrawnUi.Controls.WheelCellInfo.Transform
  commentId: P:DrawnUi.Controls.WheelCellInfo.Transform
  id: Transform
  parent: DrawnUi.Controls.WheelCellInfo
  langs:
  - csharp
  - vb
  name: Transform
  nameWithType: WheelCellInfo.Transform
  fullName: DrawnUi.Controls.WheelCellInfo.Transform
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PickerWheel/WheelCellInfo.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Transform
    path: ../src/Maui/DrawnUi/Controls/PickerWheel/WheelCellInfo.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public SKMatrix Transform { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKMatrix
    content.vb: Public Property Transform As SKMatrix
  overload: DrawnUi.Controls.WheelCellInfo.Transform*
- uid: DrawnUi.Controls.WheelCellInfo.IsSelected
  commentId: P:DrawnUi.Controls.WheelCellInfo.IsSelected
  id: IsSelected
  parent: DrawnUi.Controls.WheelCellInfo
  langs:
  - csharp
  - vb
  name: IsSelected
  nameWithType: WheelCellInfo.IsSelected
  fullName: DrawnUi.Controls.WheelCellInfo.IsSelected
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PickerWheel/WheelCellInfo.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsSelected
    path: ../src/Maui/DrawnUi/Controls/PickerWheel/WheelCellInfo.cs
    startLine: 16
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public bool IsSelected { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsSelected As Boolean
  overload: DrawnUi.Controls.WheelCellInfo.IsSelected*
- uid: DrawnUi.Controls.WheelCellInfo.Opacity
  commentId: P:DrawnUi.Controls.WheelCellInfo.Opacity
  id: Opacity
  parent: DrawnUi.Controls.WheelCellInfo
  langs:
  - csharp
  - vb
  name: Opacity
  nameWithType: WheelCellInfo.Opacity
  fullName: DrawnUi.Controls.WheelCellInfo.Opacity
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PickerWheel/WheelCellInfo.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Opacity
    path: ../src/Maui/DrawnUi/Controls/PickerWheel/WheelCellInfo.cs
    startLine: 18
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public float Opacity { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property Opacity As Single
  overload: DrawnUi.Controls.WheelCellInfo.Opacity*
references:
- uid: DrawnUi.Controls
  commentId: N:DrawnUi.Controls
  href: DrawnUi.html
  name: DrawnUi.Controls
  nameWithType: DrawnUi.Controls
  fullName: DrawnUi.Controls
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Controls.WheelCellInfo.View*
  commentId: Overload:DrawnUi.Controls.WheelCellInfo.View
  href: DrawnUi.Controls.WheelCellInfo.html#DrawnUi_Controls_WheelCellInfo_View
  name: View
  nameWithType: WheelCellInfo.View
  fullName: DrawnUi.Controls.WheelCellInfo.View
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: DrawnUi.Controls.WheelCellInfo.Index*
  commentId: Overload:DrawnUi.Controls.WheelCellInfo.Index
  href: DrawnUi.Controls.WheelCellInfo.html#DrawnUi_Controls_WheelCellInfo_Index
  name: Index
  nameWithType: WheelCellInfo.Index
  fullName: DrawnUi.Controls.WheelCellInfo.Index
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Controls.WheelCellInfo.WasMeasured*
  commentId: Overload:DrawnUi.Controls.WheelCellInfo.WasMeasured
  href: DrawnUi.Controls.WheelCellInfo.html#DrawnUi_Controls_WheelCellInfo_WasMeasured
  name: WasMeasured
  nameWithType: WheelCellInfo.WasMeasured
  fullName: DrawnUi.Controls.WheelCellInfo.WasMeasured
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Controls.WheelCellInfo.Destination*
  commentId: Overload:DrawnUi.Controls.WheelCellInfo.Destination
  href: DrawnUi.Controls.WheelCellInfo.html#DrawnUi_Controls_WheelCellInfo_Destination
  name: Destination
  nameWithType: WheelCellInfo.Destination
  fullName: DrawnUi.Controls.WheelCellInfo.Destination
- uid: SkiaSharp.SKRect
  commentId: T:SkiaSharp.SKRect
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  name: SKRect
  nameWithType: SKRect
  fullName: SkiaSharp.SKRect
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Controls.WheelCellInfo.Offset*
  commentId: Overload:DrawnUi.Controls.WheelCellInfo.Offset
  href: DrawnUi.Controls.WheelCellInfo.html#DrawnUi_Controls_WheelCellInfo_Offset
  name: Offset
  nameWithType: WheelCellInfo.Offset
  fullName: DrawnUi.Controls.WheelCellInfo.Offset
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Controls.WheelCellInfo.Transform*
  commentId: Overload:DrawnUi.Controls.WheelCellInfo.Transform
  href: DrawnUi.Controls.WheelCellInfo.html#DrawnUi_Controls_WheelCellInfo_Transform
  name: Transform
  nameWithType: WheelCellInfo.Transform
  fullName: DrawnUi.Controls.WheelCellInfo.Transform
- uid: SkiaSharp.SKMatrix
  commentId: T:SkiaSharp.SKMatrix
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skmatrix
  name: SKMatrix
  nameWithType: SKMatrix
  fullName: SkiaSharp.SKMatrix
- uid: DrawnUi.Controls.WheelCellInfo.IsSelected*
  commentId: Overload:DrawnUi.Controls.WheelCellInfo.IsSelected
  href: DrawnUi.Controls.WheelCellInfo.html#DrawnUi_Controls_WheelCellInfo_IsSelected
  name: IsSelected
  nameWithType: WheelCellInfo.IsSelected
  fullName: DrawnUi.Controls.WheelCellInfo.IsSelected
- uid: DrawnUi.Controls.WheelCellInfo.Opacity*
  commentId: Overload:DrawnUi.Controls.WheelCellInfo.Opacity
  href: DrawnUi.Controls.WheelCellInfo.html#DrawnUi_Controls_WheelCellInfo_Opacity
  name: Opacity
  nameWithType: WheelCellInfo.Opacity
  fullName: DrawnUi.Controls.WheelCellInfo.Opacity

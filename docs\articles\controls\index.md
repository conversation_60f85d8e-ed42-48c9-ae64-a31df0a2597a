# Controls Overview

DrawnUi positions itself as an engine providing a toolset to create and use custom drawn controls. Out-of-the-box it provides you with base controls that can be used as lego-bricks to composite custom controls, and proposes some useful pre-made custom controls.

The main spirit is to have all controls subclassable and customizable at the maximum possible extent.

DrawnUi provides a comprehensive set of UI controls rendered with SkiaSharp for optimal performance. All controls support platform-specific styling and extensive customization options.

## Base Drawn Controls

These are the fundamental building blocks for creating any custom control:

- **SkiaControl**: Your lego brick to create anything
- **[SkiaShape](shapes.md)**: Path, Rectangle, Circle, Ellipse, Gauge etc, can wrap other elements to be clipped inside
- **[SkiaLabel](text.md#skialabel)**: Multiline with many options like dropshadow, gradients etc
- **[SkiaImage](images.md#skiaimage)**: With many options and filters
- **[SkiaSvg](images.md#skiasvg)**: SVG rendering with many options
- **[SkiaLayout](layouts.md#skialayout)**: (Absolute, Grid, Vertical stack, Horizontal stack) with templates support
- **[SkiaScroll](scroll.md)**: (Horizontal, Vertical, Both) with header, footer, zoom support and adjustable inertia, bounce, snap and much more
- **[SkiaHotspot](input.md#skiahotspot)**: To handle gestures in a lazy way
- **SkiaBackdrop**: To apply effects to background below, like blur etc
- **[SkiaMauiElement](native-integration.md)**: To embed MAUI controls in your canvas

## Custom Controls

These are specialized controls derived from the base ones:

### Text and Content
- **[SkiaMarkdownLabel](text.md#skiamarkdownlabel)**: Will find an installed font for any unicode text and auto-create spans for markdown

### Buttons and Interaction
- **[SkiaButton](buttons.md)**: Include anything inside, text, images etc
- **SkiaRadioButton**: Select something unique from options

### Toggle Controls
- **[SkiaSwitch](switches.md#skiaswitch)**: To be able to toggle anything
- **[SkiaCheckbox](switches.md#skiacheckbox)**: Platform-styled checkbox
- **[SkiaToggle](switches.md#skiatoggle)**: Base toggle class for custom toggles

### Progress and Input
- **[SkiaProgress](progress.md)**: To show that you are actually doing something
- **[SkiaSlider](input.md#skiaslider)**: Including range selection capability
- **[SkiaWheelPicker](input.md#skiawheelpicker)**: Your iOS-look picker wheel
- **[SkiaHotspot](input.md#skiahotspot)**: Handle gestures in a lazy way

### Media and Animation
- **[SkiaLottie](animations.md#skialottie)**: With tint customization
- **[SkiaGif](animations.md#skiagif)**: A dedicated lightweight GIF-player with playback properties
- **[SkiaSprite](sprites.md)**: High-performance sprite sheet animations
- **SkiaMediaImage**: A subclassed `SkiaImage` for displaying any kind of images (image/animated gif/more..)
- **SkiaCamera**: That day we draw it on the canvas has finally come

### Layout and Navigation
- **[SkiaCarousel](carousels.md)**: Swipe and slide controls inside a carousel
- **[SkiaDrawer](drawers.md)**: To swipe in and out your controls
- **SkiaScrollLooped**: A subclassed `SkiaScroll` for neverending scrolls
- **[SkiaDecoratedGrid](layouts.md)**: To draw shapes between rows and columns
- **[SkiaShell](shell.md)**: For navigation inside a drawn app
- **SkiaViewSwitcher**: Switch your views, pop, push and slide

### Specialized Controls
- **[RefreshIndicator](progress.md#refreshindicator)**: Can use Lottie and anything as ActivityIndicator or for your scroll RefreshView
- **SkiaSpinner**: To test your luck
- **SkiaHoverMask**: To overlay a clipping shape
- **SkiaTabsSelector**: Create top and bottom tabs
- **SkiaLabelFps**: For development

## Control Aliases

These are controls that are aliases for other controls, to make porting existing native apps easier:

- **SkiaFrame**: Alias for SkiaShape of Rectangle type (MAUI Frame)
- **SkiaStack**: For SkiaLayout type Column with default horizontal Fill (MAUI VerticalStackLayout)
- **SkiaRow**: For SkiaLayout type Row (MAUI HorizontalStackLayout)
- **SkiaLayer**: For SkiaLayout type Absolute with default horizontal Fill (MAUI Grid with 1 col/row used for layering)
- **SkiaGrid**: For SkiaLayout type Grid with default horizontal Fill (MAUI Grid)
- **SkiaWrap**: For SkiaLayout type Wrap with default horizontal Fill (similar to MAUI FlexLayout)

## Getting Started

To start using DrawnUi controls, wrap them inside a `Canvas` view in your MAUI app:

```xml
<draw:Canvas>
    <draw:SkiaLayout Type="Column">
        <draw:SkiaLabel Text="Hello DrawnUi!" />
        <draw:SkiaButton Text="Click Me" />
    </draw:SkiaLayout>
</draw:Canvas>
```

For more examples and detailed documentation, explore the individual control pages linked above.

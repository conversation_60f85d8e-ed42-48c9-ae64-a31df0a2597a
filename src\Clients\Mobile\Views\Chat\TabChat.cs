﻿using AppoMobi.Maui.Gestures;

namespace AppoMobi.Mobile.Views;

/*
    <views:FastCellGroupChatMessage
       CommandEmptySpaceTapped="{Binding Source={RelativeSource AncestorType={x:Type viewModels:ChatViewModel}}, Path=CommandCancelReply}"
       CommandAttachedMessageTapped="{Binding Source={RelativeSource AncestorType={x:Type viewModels:ChatViewModel}}, Path=CommandScrollToMessage}"
       CommandAvatarTapped="{Binding Source={RelativeSource AncestorType={x:Type viewModels:ChatViewModel}}, Path=CommandSelectPlayer}"
       CommandLongPressing="{Binding Source={RelativeSource AncestorType={x:Type viewModels:ChatViewModel}}, Path=CommandMessageOptions}"
       CommandAvatarLongPressing="{Binding Source={RelativeSource AncestorType={x:Type viewModels:ChatViewModel}}, Path=CommandPlayerOptions}"
       CommandTapped="{Binding Source={RelativeSource AncestorType={x:Type viewModels:ChatViewModel}}, Path=CommandSelectedItem}"
       ContainerWidth="{Binding Source={x:Reference MainScroll}, Path=Width}"
       HorizontalOptions="Fill"
       Rotation="180"
       VerticalOptions="Start" />
 */

public class TabChat : AppScreen
{
    public readonly TabChatViewModel Model;

    private CardChatUnreadMessage _lastUnreadMessage;

    public TabChat(TabChatViewModel vm)
    {
        Model = vm;
        BindingContext = Model;

        Children = new List<SkiaControl>()
        {
            //new SkiaSvg()
            //{
            //    SvgString = App.Current.Resources.Get<string>("SvgConstruction"),
            //    VerticalOptions = LayoutOptions.Center,
            //    HorizontalOptions = LayoutOptions.Center,
            //    HeightRequest = 150,
            //    LockRatio = 1,
            //    TintColor = Color.Parse("#22000000")
            //},

            new ScreenVerticalStack()
                {
                    UseCache = SkiaCacheType.Image,
                    Padding = new Thickness(0, 24, 0, 16),
                    HorizontalOptions = LayoutOptions.Fill,
                    Spacing = 0,
                    Type = LayoutType.Column,
                    Children = new List<SkiaControl>
                    {
                        // Navbar padding for fullscreen version
                        new StatusBarPlaceholder(),

                        // Title 
                        new SkiaLayout()
                        {
                            UseCache = SkiaCacheType.Image,
                            Margin = new(24, 0, 24, 24),
                            HorizontalOptions = LayoutOptions.Fill,
                            Children = new List<SkiaControl>()
                            {
                                new LabelScreenTitle
                                {
                                    Text = ResStrings.Support,
                                },
                            }
                        },

                        new SkiaLabel(ResStrings.ChatOperatorHint)
                        {
                            UseCache = SkiaCacheType.Operations,
                            HorizontalOptions = LayoutOptions.Center,
                            HorizontalTextAlignment = DrawTextAlignment.Center,
                            Margin = new Thickness(24, 32, 24, 24)
                        },

                        new CardChatUnreadMessage(null)
                        {
                            UseCache = SkiaCacheType.Image,
                            HorizontalOptions = LayoutOptions.Center,
                            Margin = new Thickness(24, 8, 24, 0)
                        }.Assign(out _lastUnreadMessage)
                        .OnTapped((me) => { OpenChat(); }),

                        new ButtonMedium()
                        {
                            Margin = new Thickness(24, 32, 24, 24),
                            UseCache = SkiaCacheType.Image,
                            HorizontalOptions = LayoutOptions.Center,
                            WidthRequest = 250,
                            HeightRequest = 44,
                            Text = ResStrings.ChatOpen,
                            Look = BtnStyle.Default
                        }.OnTapped((me) => { OpenChat(); })
                    }
                }
                .Observe(Model, (me, prop) =>
                {
                    if (prop.IsEither(nameof(BindingContext),
                            nameof(Model.Item)))
                    {
                        _lastUnreadMessage.Update(Model.Info);
                    }
                })
                .Initialize((me) =>
                {
                    //UpdateLastMessage();
                })
        };
    }


    public override void OnDisappeared()
    {
        base.OnDisappeared();

        Model.IsActive = false;
    }

    public override void OnAppearing()
    {
        base.OnAppearing();

        Model.IsActive = true;

        Tasks.StartDelayed(TimeSpan.FromMilliseconds(50), () =>
        {
            //Super.Native?.SetBlackTextStatusBar();

            Task.Run(async () =>
            {
                var canUpdate = BindingContext as IUpdateUIState;
                canUpdate?.UpdateState();
            }).ConfigureAwait(false);
        });
    }

    public void OpenChat()
    {
        if (TouchEffect.CheckLockAndSet())
            return;

        _ = Task.Run(async () =>
        {
            await App.Instance.Singletons.Presentation.Shell.PushModalAsync(AppRoutes.Chat.Route, false, true,
                true,
                new Dictionary<string, object>
                {
                    { "id", "support" }
                });

            //await Presentation.Shell.GoToAsync("profile", true);
        }).ConfigureAwait(false);
    }
}
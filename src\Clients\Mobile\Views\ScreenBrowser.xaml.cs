using System.Text.RegularExpressions;

namespace AppoMobi.Mobile;

public class CustomWebView : WebView
{
    public CustomWebView()
    {
        //Navigated += (s, e) =>
        //{
        //    InjectJavaScript(this);
        //};
    }

    private void InjectJavaScript(WebView webView)
    {
        var script = @"
    (function() {
        // Replace pt with px in inline styles within the head
        var styles = document.querySelectorAll('style');
        styles.forEach(function(style) {
            if (style.innerHTML) {
                style.innerHTML = style.innerHTML.replace(/([0-9]+)pt/g, '$1px');
            }
        });
    })();
    ";

        webView.Eval(script);
    }

    public async void LoadAndModifyContent(string url)
    {
        try
        {
            // Load HTML content from URL
            using (HttpClient client = new HttpClient())
            {
                string htmlContent = await client.GetStringAsync(url);

                // Modify the HTML content
                string modifiedContent = ModifyHtml(htmlContent);

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    Source = new HtmlWebViewSource
                    {
                        Html = modifiedContent
                    };
                });


            }
        }
        catch (Exception e)
        {
            Super.Log(e);
        }
    }

    private string ModifyHtml(string htmlContent)
    {
        var pass1 = Regex.Replace(htmlContent, @"72pt", "2pt");
        return pass1.Replace("90%", "100%");

        //return htmlContent.Replace("padding: 72pt 72pt 72pt 72pt;", "padding: 2pt 2pt 2pt 2pt;");
        //return Regex.Replace(htmlContent, @"(\d+)pt", "$1px");
    }

}


public partial class ScreenBrowser
{
    private readonly ProjectViewModel Model;

    /// <summary>
    /// Can pass url or html, if html is passed isUrl must be false
    /// </summary>
    /// <param name="vm"></param>
    /// <param name="title"></param>
    /// <param name="source"></param>
    /// <param name="isUrl"></param>
    public ScreenBrowser(ProjectViewModel vm, string title, string source, bool isUrl = true)
    {
        try
        {
            Model = vm;

            BindingContext = Model;

            InitializeComponent();

            LabelTile.Text = title;

            if (isUrl)
            {
                if (string.IsNullOrEmpty(source))
                {
                    source = "about:blank";
                }

                ControlBrowser.LoadAndModifyContent(source);

                //var url = new UrlWebViewSource
                //{
                //    Url = source
                //};
                //ControlBrowser.Source = url;
            }
            else
            {
                if (string.IsNullOrEmpty(source))
                {
                    source = "";
                }
                var html = new HtmlWebViewSource
                {
                    Html = source
                };
                ControlBrowser.Source = html;

            }

        }
        catch (Exception e)
        {
            Super.DisplayException(this, e);
        }
    }



}
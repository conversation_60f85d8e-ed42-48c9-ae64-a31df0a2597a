﻿namespace AppoMobi.Mobile.Views
{
    /// <summary>
    /// User profile editor screen
    /// </summary>
    public class ScreenBrowser : AppScreen
    {
        private readonly ProjectViewModel Model;

        public ScreenBrowser(ProjectViewModel vm, string title, string source, bool isUrl = true)
        {
            Model = vm;
            BindingContext = Model;

            Shell.SetPresentationMode(this, PresentationMode.ModalAnimated);
            BackgroundColor = Colors.Transparent; //for modal

            Margin = UiHelper.ModalInsets;
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;

            //UseCache = SkiaCacheType.ImageComposite;

            Type = LayoutType.Column;
            Spacing = 0;
            CreateContent();

            Header.SetTitle(title);

            Tasks.StartDelayed(TimeSpan.FromMilliseconds(50), () =>
            {

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    if (isUrl)
                    {
                        if (string.IsNullOrEmpty(source))
                        {
                            source = "about:blank";
                        }
                        var url = new UrlWebViewSource
                        {
                            Url = source
                        };
                        ControlBrowser.Source = url;
                    }
                    else
                    {
                        if (string.IsNullOrEmpty(source))
                        {
                            source = "";
                        }
                        var html = new HtmlWebViewSource
                        {
                            Html = source
                        };
                        ControlBrowser.Source = html;
                    }

                });

            });



            //if (isUrl)
            //{
            //    if (string.IsNullOrEmpty(source))
            //    {
            //        source = "about:blank";
            //    }

            //    ControlBrowser.LoadAndModifyContent(source);

            //    //var url = new UrlWebViewSource
            //    //{
            //    //    Url = source
            //    //};
            //    //ControlBrowser.Source = url;
            //}
            //else
            //{
            //    if (string.IsNullOrEmpty(source))
            //    {
            //        source = "";
            //    }
            //    var html = new HtmlWebViewSource
            //    {
            //        Html = source
            //    };
            //    ControlBrowser.Source = html;

            //}
        }

        private WebView ControlBrowser;

        private ModalHeader Header;

        private void CreateContent()
        {

            Children = new List<SkiaControl>
            {
                new StatusBarPlaceholder(),

                // HEADER
                new ModalHeader
                {
                    Title = ResStrings.MyProfile
                }.Assign(out Header),

                // CONTENT
                CreateContentLayout()
            };
        }

        private SkiaControl CreateContentLayout()
        {
            return new SkiaShape()
            {
                StrokeColor = AppColors.ControlPrimary,
                StrokeWidth = 1,
                BackgroundColor = Colors.White,
                HorizontalOptions = LayoutOptions.Fill,
                VerticalOptions = LayoutOptions.Fill,
                Children =
                {
                    new SkiaMauiElement()
                    {
                        BackgroundColor = AppColors.Background,
                        HorizontalOptions = LayoutOptions.Fill,
                        VerticalOptions = LayoutOptions.Fill,
                        Content = new WebView
                        {
                            HorizontalOptions = LayoutOptions.FillAndExpand,
                            VerticalOptions = LayoutOptions.FillAndExpand,
                        }.AssignNative(out ControlBrowser)
                    }
                }
            };
        }


    }
}
using AppoMobi.Mobile;

namespace AppoMobi.Mobile.Views;

public partial class TabSettings
{
    public TabSettings()
    {
        InitializeComponent();
    }

    public override void OnAppearing()
    {
        base.OnAppearing();

        if (BindingContext is MainPageViewModel vm)
        {
            vm.UpdateSettings();
        }
    }

    private void OnTappedDebug(object sender, SkiaGesturesParameters skiaGesturesParameters)
    {
        Superview.PostponeExecutionAfterDraw(() =>
        {
            var control = new SkiaShape
            {
                BackgroundColor = Colors.Red,
                WidthRequest = 100,
                HeightRequest = 20
            };
            StackContainer.AddSubView(control);
            return;
            //var check = this.GetVisualElementWindow();
            var check1 = StackOptions.GetVisualTreeDescendants();
            if (check1.Count > 0)
            {
                foreach (var visualTreeElement in check1)
                {
                    Trace.WriteLine($"{visualTreeElement}");
                }
            }
        });
    }
}
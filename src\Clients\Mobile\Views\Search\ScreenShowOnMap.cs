﻿namespace AppoMobi.Mobile.Views;

public class ScreenShowOnMap : ScreenSearchOnMap
{
    public ScreenShowOnMap(SearchAddressOnMapViewmodel vm) : base(vm)
    {

    }

    public override void InitializeDefaultContent(bool force = false)
    {
        base.InitializeDefaultContent(force);

        Map.CanClick = false;
        Drawer.IsVisible = false;
        ButtonToggle.IsVisible = false;
        ButtonPosition.IsVisible = false;
    }

}
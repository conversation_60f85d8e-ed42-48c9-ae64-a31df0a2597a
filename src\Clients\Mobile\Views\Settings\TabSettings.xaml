<?xml version="1.0" encoding="utf-8" ?>

<draw:SkiaLayout
    x:Class="AppoMobi.Mobile.Views.TabSettings"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:mobile="clr-namespace:AppoMobi.Mobile"
    xmlns:models="clr-namespace:AppoMobi.Mobile.Infrastructure"
    xmlns:strings="clr-namespace:AppoMobi.Common.ResX;assembly=AppoMobi.Common"
    xmlns:viewModels="clr-namespace:AppoMobi.Mobile.ViewModels"
    x:DataType="mobile:MainPageViewModel"
    BackgroundColor="{StaticResource ColorPaper}"
    HorizontalOptions="Fill"
    VerticalOptions="Fill">

    <!--  main scroll  -->
    <draw:SkiaScroll
        x:Name="MainScroll"
        HorizontalOptions="Fill"
        VerticalOptions="Fill"
        ZIndex="-1">

        <draw:SkiaLayout
            x:Name="StackContainer"
            Padding="0,16,0,0"
            HorizontalOptions="Fill"
            IsClippedToBounds="True"
            MinimumHeightRequest="-1"
            Spacing="8"
            Type="Column"
            UseCache="None">

            <!--  navbar padding  -->
            <draw:SkiaControl HeightRequest="{Binding Presentation.StatusBarHeightRequest}" WidthRequest="1" />

            <!--  title  -->
            <draw:SkiaLabel
                Margin="16,16,64,8"
                FontFamily="FontTextTitle"
                FontSize="32"
                Text="{x:Static strings:ResStrings.Settings}"
                TextColor="{StaticResource ColorText}"
                VerticalOptions="Center" />

            <!--<controls:SmallButton
                Tapped="OnTappedDebug"
                Text="Debug" />-->

            <!--  options  -->
            <draw:SkiaLayout
                x:Name="StackOptions"
                HorizontalOptions="Fill"
                InitializeTemplatesInBackgroundDelay="0"
                IsClippedToBounds="True"
                ItemsSource="{Binding SettingsFields}"
                RecyclingTemplate="Disabled"
                Spacing="8"
                Split="1"
                Tag="StackSettings"
                Type="Wrap"
                UseCache="ImageComposite">

                <draw:SkiaLayout.ItemTemplate>
                    <DataTemplate>
                        <!--  cell [cached]  -->
                        <viewModels:CellSettings
                            draw:AddGestures.AnimationTapped="Ripple"
                            draw:AddGestures.CommandTapped="{Binding Command}"
                            x:DataType="models:ActionOption"
                            HeightRequest="54"
                            HorizontalOptions="Fill"
                            IsClippedToBounds="True"
                            IsVisible="{Binding IsVisible}"
                            UseCache="Image">

                            <!--  back  -->
                            <draw:SkiaShape
                                Margin="16,0"
                                BackgroundColor="{x:Static mobile:AppColors.BackgroundPrimary}"
                                CornerRadius="6"
                                HorizontalOptions="Fill"
                                StrokeColor="{x:Static mobile:AppColors.BackgroundSecondary}"
                                StrokeWidth="1"
                                VerticalOptions="Fill"
                                ZIndex="-1">
                                <draw:SkiaShape.Shadows>
                                    <draw:SkiaShadow
                                        Blur="1"
                                        Opacity="1"
                                        X="1"
                                        Y="1"
                                        Color="{x:Static mobile:AppColors.BackgroundSecondary}" />
                                </draw:SkiaShape.Shadows>
                            </draw:SkiaShape>

                            <draw:SkiaLayout
                                Padding="32,0,0,0"
                                HorizontalOptions="Fill"
                                Spacing="0"
                                Type="Row"
                                VerticalOptions="Fill">

                                <draw:SkiaSvg
                                    Margin="0,0,68,0"
                                    HeightRequest="16"
                                    LockRatio="1"
                                    Opacity="0.4"
                                    Tag="SvgIcon"
                                    TintColor="{StaticResource ColorGray}"
                                    VerticalOptions="Center"
                                    Zoom="{Binding ZoomIcon}" />

                                <draw:SkiaLabel
                                    FontFamily="FontTextSemiBold"
                                    FontSize="17"
                                    HorizontalOptions="Fill"
                                    LineBreakMode="TailTruncation"
                                    MaxLines="1"
                                    Tag="LabelTitle"
                                    TextColor="{StaticResource ColorText}"
                                    VerticalOptions="Center" />

                                <draw:SkiaSvg
                                    Margin="0,0,32,0"
                                    HeightRequest="14"
                                    HorizontalOptions="End"
                                    LockRatio="1"
                                    SvgString="{StaticResource SvgExpandRight}"
                                    VerticalOptions="Center" />

                            </draw:SkiaLayout>

                        </viewModels:CellSettings>
                    </DataTemplate>
                </draw:SkiaLayout.ItemTemplate>

            </draw:SkiaLayout>

            <!--  version  -->
            <draw:SkiaLabel
                Margin="16"
                FontFamily="FontText"
                FontSize="12"
                FontWeight="400"
                HorizontalOptions="Center"
                LineBreakMode="TailTruncation"
                Text="{x:Static mobile:App.Build}"
                TextColor="{StaticResource ColorGray}" />

            <draw:SkiaControl HeightRequest="{Binding Presentation.BottomTabsHeightRequest}" WidthRequest="1" />

        </draw:SkiaLayout>


    </draw:SkiaScroll>

</draw:SkiaLayout>
﻿using AppoMobi.Common.Enums.UserData;
using AppoMobi.Forms.UniversalEditor.Controls;
using AppoMobi.Mobile.Views;
using AppoMobi.Specials.Localization;
using DrawnUi.Controls;

namespace AppoMobi.Mobile;

public class UiHelper
{
    public static Color GetStatusColor(CustomerRequestStatus status, bool forMaster)
    {
        if (forMaster)
        {
            switch (status)
            {
                case CustomerRequestStatus.Unset:
                    return App.Current.Resources.Get<Color>("ColorTextCell");
                    break;

                case CustomerRequestStatus.Approved:
                    return App.Current.Resources.Get<Color>("ColorInfo");
                    break;

                case CustomerRequestStatus.NeedMoreInfo:
                    return App.Current.Resources.Get<Color>("ColorDanger");
                    break;

                case CustomerRequestStatus.Rejected:
                    return App.Current.Resources.Get<Color>("ColorWarning");
                    break;

                case CustomerRequestStatus.Complete:
                    return App.Current.Resources.Get<Color>("ColorTextCell");
                    break;

                case CustomerRequestStatus.Processing:
                    return App.Current.Resources.Get<Color>("ColorSuccess");
                    break;

                default:
                    return App.Current.Resources.Get<Color>("ColorTextCell");
                    break;
            }
        }
        else
        {
            switch (status)
            {
                case CustomerRequestStatus.Unset:
                    //LabelStatus.Text = "Ожидает ответа..";
                    return App.Current.Resources.Get<Color>("ColorTextCell");
                    break;

                case CustomerRequestStatus.Approved:
                    //LabelStatus.Text = "В работу";
                    return App.Current.Resources.Get<Color>("ColorWarning");
                    break;

                case CustomerRequestStatus.NeedMoreInfo:
                    //LabelStatus.Text = "Ждем ответа..";
                    return App.Current.Resources.Get<Color>("ColorInfo");
                    break;

                case CustomerRequestStatus.Rejected:
                    //LabelStatus.Text = "Отказ";
                    return App.Current.Resources.Get<Color>("ColorTextCell");
                    break;

                case CustomerRequestStatus.Complete:
                    //LabelStatus.Text = "Завершено";
                    return App.Current.Resources.Get<Color>("ColorTextCell");
                    break;

                case CustomerRequestStatus.Processing:
                    //LabelStatus.Text = "В работе..";
                    return App.Current.Resources.Get<Color>("ColorSuccess");
                    break;

                default:
                    return App.Current.Resources.Get<Color>("ColorTextCell");
                    break;
            }
        }
    }

    public static Thickness ModalInsets => new Thickness(0, 16, 0, 0);

    public SkiaLayout CreateInputSection(string propertyName, string labelText, int errorId)
    {
        return new SkiaLayout()
        {
            Type = LayoutType.Column,
            HorizontalOptions = LayoutOptions.Fill,
            Children = new List<SkiaControl>()
            {
                // Label
                new SkiaLabel()
                {
                    FontFamily = "FontTextSemiBold",
                    FontSize = 14,
                    Margin = new Thickness(0, 0, 0, 0),
                    Text = labelText,
                    TextColor = AppColors.TextSecondary
                },

                // Entry Field Container
                new AppFrame()
                {
                    Padding = new(4, 2),
                    Layout = LayoutType.Column,
                    Spacing = 0,
                    Children = new List<SkiaControl>()
                    {
                        // Text Entry
                        new ProjectEntryTitleCase()
                            {
                                Padding = new Thickness(0, 2, 0, 4),
                                HeightRequest = 32,
                                HorizontalOptions = LayoutOptions.Fill,
                                FontFamily = AppFonts.Normal,
                                FontSize = 15,
                                MaxLines = 1,
                                IsSpellCheckEnabled = false,
                                Margin = new Thickness(0),
                                VerticalOptions = LayoutOptions.Start,
                                TextColor = AppColors.Text
                            }.Assign(out var entry)
                            .Adapt((entry) =>
                            {
                                entry.SetBinding(SkiaMauiEntry.TextProperty, $"Item.{propertyName}",
                                    BindingMode.TwoWay);

                                // Set up focus trigger

                                entry.PropertyChanged += (s, e) =>
                                {
                                    if (e.PropertyName == nameof(entry.IsFocused))
                                    {
                                        entry.TextColor =
                                            entry.IsFocused ? AppColors.PrimaryDark : AppColors.Text;
                                        //entry.BackgroundColor =
                                        //    entry.IsFocused ? AppColors.Focus : Colors.Transparent;
                                    }
                                };
                            }),

                        // Divider Line with Error Trigger
                        new SkiaShape()
                        {
                            HeightRequest = 1,
                            BackgroundColor = AppColors.Line
                        }.Adapt((shape) =>
                        {
                            if (errorId > 0)
                            {
                                // Set up error indicator trigger
                                shape.ObserveBindingContext<SkiaShape, IEditorForm>((shape, vm, prop) =>
                                {
                                    if (prop == nameof(vm.EditorErrorFields) ||
                                        prop == nameof(SkiaControl.BindingContext))
                                    {
                                        bool hasError = vm.EditorErrorFields != null &&
                                                        vm.EditorErrorFields.Contains(errorId);
                                        shape.BackgroundColor = hasError ? AppColors.Danger : AppColors.Line;
                                    }
                                });
                            }
                        })
                    }
                }
            }
        };
    }

    public SkiaControl CreateWaitingPopup()
    {
        return new SkiaShape()
        {
            CornerRadius = 16,
            WidthRequest = 80,
            LockRatio = 1,
            VerticalOptions = LayoutOptions.Center,
            HorizontalOptions = LayoutOptions.Center,
            BackgroundColor = AppColors.BackgroundSecondary,
            StrokeWidth = 1,
            StrokeColor = AppColors.PrimaryLight,
            Children =
            {
                new AppActivityIndicator()
                {
                    IsRunning = true,
                    VerticalOptions = LayoutOptions.Center
                }
            }
        };
    }

    public SkiaButton CreateRoundButton()
    {
        var btn = new SkiaButton()
        {
            Children = new List<SkiaControl>()
            {
                new SkiaShape()
                {
                    Tag = "BtnShape", //convention
                    WidthRequest = 44,
                    LockRatio = 1,
                    Type = ShapeType.Circle,
                    Children =
                    {
                        new SkiaSvg()
                        {
                            SvgString = App.Current.Resources.Get<string>("SvgAdd"),
                            HeightRequest = 19,
                            LockRatio = 1,
                            VerticalOptions = LayoutOptions.Center,
                            HorizontalOptions = LayoutOptions.Center
                        }
                    }
                }
            }
        };

        btn.BackgroundColor = AppColors.Primary;

        return btn;
    }

    public SkiaButton CreateBackButton()
    {
        var btn = new SkiaButton()
        {
            Children = new List<SkiaControl>()
            {
                new SkiaShape()
                {
                    Tag = "BtnShape", //convention
                    WidthRequest = 44,
                    LockRatio = 1,
                    Type = ShapeType.Circle,
                    Children =
                    {
                        new SkiaSvg()
                        {
                            SvgString = App.Current.Resources.Get<string>("SvgGoBack"),
                            HeightRequest = 20,
                            TintColor = AppColors.ControlPrimary,
                            LockRatio = 1,
                            VerticalOptions = LayoutOptions.Center,
                            HorizontalOptions = LayoutOptions.Center
                        }
                    }
                }
            }
        };

        btn.BackgroundColor = Colors.Transparent;

        return btn;
    }
}
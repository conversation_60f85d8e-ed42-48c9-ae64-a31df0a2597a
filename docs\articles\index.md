# Articles

__NOTE: this is under heavy construction AND NOT READY TO USE YET, may contain some outdated or non-exact information!!!.__  

This section contains documentation articles and guides for using DrawnUi.

## Getting Started

- [Installation and Setup](getting-started.md)
- [Your First DrawnUi App](first-app.md)
- [Understanding the Drawing Pipeline](drawing-pipeline.md)

## Controls

- [Overview](controls/index.md)
- [Buttons](controls/buttons.md)
- [Switches and Toggles](controls/switches.md)
- [Layout Controls](controls/layouts.md)
- [Text and Labels](controls/text.md)
- [Images](controls/images.md)

## Advanced Topics

- [Platform-Specific Styling](advanced/platform-styling.md)
- [Layout System Architecture](advanced/layout-system.md)
- [Gradients](advanced/gradients.md)
- [Game UI & Interactive Games](advanced/game-ui.md)
- [SkiaScroll & Virtualization](advanced/skiascroll.md)
- [Gestures & Touch Input](advanced/gestures.md)


## FAQ

**Is it DrawnUI or DrawnUi?**
Both are totally fine.

**How do I create my custom button?**
While you can use SkiaButton and set a custom content to it, you can also use a click handler Tapped with ANY control you like.

**I have an existing MAUI app, how can DrawnUi be beneficial to me?**
You can definitely speed your app by replacing slow UI parts with faster drawn sections, replacing a horde of native controls with just one canvas. Check out the [Porting MAUI](porting-maui.md) guide.

**Knowing that properties are in points, how do I create a line or stroke of exactly 1 pixel?**
When working with SkiaShape use a negative value (ex: -1) to pass pixels instead of points to compatible properties like StrokeWidth and similar.

**How do I bind SkiaImage source not to a file/url but to an existing bitmap?**
Use `ImageBitmap` property for that, type is `LoadedImageSource`.

**Can DrawnUi use MAUI's default Images folder?**
Unfortunately no. DrawnUi can read from Raw folder and from native storage if the app has written there, but not from the Images folder. It's "hardcoded-designed for MAUI views" and not accessible to DrawnUi controls.

**How do I prevent touch input from passing through overlapping controls?**
Use the `BlockGesturesBelow="True"` property on the top control. Note that `InputTransparent` makes the control itself avoid gestures, but doesn't block gestures from reaching controls below it in the Z-axis.

**Does DrawnUi work with .NET 9?**
Yes, DrawnUi works with .NET 9. However, remember that SkiaLabel, SkiaLayout etc. are virtual drawn controls that must be placed inside a `Canvas` control: `<draw:Canvas>your skia controls</draw:Canvas>`. Only Canvas has handlers for normal and hardware accelerated views.

**How do I enable mouse wheel scrolling in SkiaScroll?**
Mouse wheel scrolling is not built-in by default, but you can easily implement it by subclassing SkiaScroll and overriding the `ProcessGestures` method to handle `TouchActionResult.Wheel` events. See the [GitHub discussions](https://github.com/taublast/DrawnUi/discussions/162) for a complete implementation example.
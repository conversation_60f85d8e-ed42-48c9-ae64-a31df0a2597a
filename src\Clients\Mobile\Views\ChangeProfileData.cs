﻿using System.ComponentModel;
using AppoMobi.Forms.UniversalEditor.Controls;
using DrawnUi.Controls;

namespace AppoMobi.Mobile.Views;

 

public interface IEditorForm : INotifyPropertyChanged
{
    List<int> EditorErrorFields { get; set; }
    string EditorValidationErrorsDesc { get; set; }
    bool EditorIsValid { get; set; }
}

public class ChangeProfileData : SkiaLayout
{

    protected SkiaLayout CreatePersonalInfoFrame()
    {
        return new SkiaLayout()
        {
            Type = LayoutType.Column,
            Spacing = 16,
            Children = new List<SkiaControl>()
            {
                // First Name Section
                App.Instance.Presentation.Shell.Elements.CreateInputSection("FirstName", ResStrings.FirstName, 1),

                // Middle Name Section
                //CreateInputSection("MiddleName", ResStrings.MiddleName, 0),

                // Last Name Section
                App.Instance.Presentation.Shell.Elements.CreateInputSection("LastName", ResStrings.LastName, 2)
            }
        };
    }


}
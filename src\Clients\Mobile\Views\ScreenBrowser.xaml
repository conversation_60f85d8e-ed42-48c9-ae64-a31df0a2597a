<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaLayout
    x:Class="AppoMobi.Mobile.ScreenBrowser"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:mobile="clr-namespace:AppoMobi.Mobile"
    x:Name="MainLayout"
    Margin="0,40,0,0"
    x:DataType="mobile:MainPageViewModel"
    HorizontalOptions="Fill"
    Shell.PresentationMode="ModalAnimated"
    VerticalOptions="Fill">


    <!--  HEADER  -->
    <!--  top nav bar wrapper [cached]  -->
    <!--  we use padding to reserve space for shadow  -->
    <draw:SkiaLayout
        Padding="0,0,0,8"
        HeightRequest="{Binding Presentation.NavBarHeightRequest}"
        HorizontalOptions="Fill"
        Spacing="0"
        UseCache="Image"
        ZIndex="100">

        <!--  background shape + shadow  -->
        <draw:SkiaShape
            BackgroundColor="{x:StaticResource ColorTabs}"
            CornerRadius="20, 20,0,0"
            HorizontalOptions="Fill"
            IsClippedToBounds="False"
            StrokeColor="{x:StaticResource ColorTabsBorder}"
            StrokeWidth="1"
            Tag="TabsBackground"
            VerticalOptions="Fill"
            ZIndex="-1">
            <draw:SkiaShape.Shadows>

                <draw:SkiaShadow
                    Blur="2.78"
                    Opacity="0.245"
                    X="2"
                    Y="3"
                    Color="{StaticResource ColorGray}" />

            </draw:SkiaShape.Shadows>
        </draw:SkiaShape>

        <!--  container navbar without status  -->
        <draw:SkiaLayout
            HeightRequest="{x:Static draw:Super.NavBarHeight}"
            HorizontalOptions="Fill"
            VerticalOptions="End">

            <draw:SkiaLabel
                x:Name="LabelTile"
                Margin="50,0"
                FontFamily="FontText"
                FontSize="18"
                FontWeight="600"
                HorizontalOptions="Center"
                LineBreakMode="TailTruncation"
                MaxLines="2"
                Text="Modal"
                TextColor="{StaticResource ColorText}"
                VerticalOptions="Center" />

            <draw:SkiaSvg
                HorizontalOptions="End"
                LockRatio="1"
                AddMarginRight="24"
                SvgString="{StaticResource SvgClose}"
                TintColor="{StaticResource ColorIcon}"
                VerticalOptions="Center"
                WidthRequest="16"
                ZIndex="1" />

            <draw:SkiaHotspot
                CommandTapped="{Binding CommandCloseModal}"
                HorizontalOptions="End"
                WidthRequest="50" />

        </draw:SkiaLayout>

    </draw:SkiaLayout>

    <draw:SkiaControl
        BackgroundColor="{StaticResource ColorPaper}"
        HorizontalOptions="Fill"
        AddMarginTop="20"
        VerticalOptions="Fill"
        ZIndex="-1" />

    <!--  CONTENT  -->
    <draw:SkiaMauiElement
        HorizontalOptions="Fill"
        AddMarginTop="80"
        VerticalOptions="Fill">

        <mobile:CustomWebView
            BackgroundColor="White"
            x:Name="ControlBrowser"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="FillAndExpand" />

    </draw:SkiaMauiElement>



</draw:SkiaLayout>

### YamlMime:ManagedReference
items:
- uid: DrawnUi.Controls.SkiaShell.ShellStackChild
  commentId: T:DrawnUi.Controls.SkiaShell.ShellStackChild
  id: SkiaShell.ShellStackChild
  parent: DrawnUi.Controls
  children:
  - DrawnUi.Controls.SkiaShell.ShellStackChild.Control
  - DrawnUi.Controls.SkiaShell.ShellStackChild.Part
  langs:
  - csharp
  - vb
  name: SkiaShell.ShellStackChild
  nameWithType: SkiaShell.ShellStackChild
  fullName: DrawnUi.Controls.SkiaShell.ShellStackChild
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ShellStackChild
    path: ../src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs
    startLine: 2345
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: 'public record SkiaShell.ShellStackChild : IEquatable<SkiaShell.ShellStackChild>'
    content.vb: Public Class SkiaShell.ShellStackChild Implements IEquatable(Of SkiaShell.ShellStackChild)
  inheritance:
  - System.Object
  implements:
  - System.IEquatable{DrawnUi.Controls.SkiaShell.ShellStackChild}
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Controls.SkiaShell.ShellStackChild.Part
  commentId: P:DrawnUi.Controls.SkiaShell.ShellStackChild.Part
  id: Part
  parent: DrawnUi.Controls.SkiaShell.ShellStackChild
  langs:
  - csharp
  - vb
  name: Part
  nameWithType: SkiaShell.ShellStackChild.Part
  fullName: DrawnUi.Controls.SkiaShell.ShellStackChild.Part
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Part
    path: ../src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs
    startLine: 2347
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public string Part { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Part As String
  overload: DrawnUi.Controls.SkiaShell.ShellStackChild.Part*
- uid: DrawnUi.Controls.SkiaShell.ShellStackChild.Control
  commentId: P:DrawnUi.Controls.SkiaShell.ShellStackChild.Control
  id: Control
  parent: DrawnUi.Controls.SkiaShell.ShellStackChild
  langs:
  - csharp
  - vb
  name: Control
  nameWithType: SkiaShell.ShellStackChild.Control
  fullName: DrawnUi.Controls.SkiaShell.ShellStackChild.Control
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Control
    path: ../src/Maui/DrawnUi/Controls/Navigation/SkiaShell.cs
    startLine: 2348
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public SkiaControl Control { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.SkiaControl
    content.vb: Public Property Control As SkiaControl
  overload: DrawnUi.Controls.SkiaShell.ShellStackChild.Control*
references:
- uid: DrawnUi.Controls
  commentId: N:DrawnUi.Controls
  href: DrawnUi.html
  name: DrawnUi.Controls
  nameWithType: DrawnUi.Controls
  fullName: DrawnUi.Controls
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.IEquatable{DrawnUi.Controls.SkiaShell.ShellStackChild}
  commentId: T:System.IEquatable{DrawnUi.Controls.SkiaShell.ShellStackChild}
  parent: System
  definition: System.IEquatable`1
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  name: IEquatable<SkiaShell.ShellStackChild>
  nameWithType: IEquatable<SkiaShell.ShellStackChild>
  fullName: System.IEquatable<DrawnUi.Controls.SkiaShell.ShellStackChild>
  nameWithType.vb: IEquatable(Of SkiaShell.ShellStackChild)
  fullName.vb: System.IEquatable(Of DrawnUi.Controls.SkiaShell.ShellStackChild)
  name.vb: IEquatable(Of SkiaShell.ShellStackChild)
  spec.csharp:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: <
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.ShellStackChild
    name: ShellStackChild
    href: DrawnUi.Controls.SkiaShell.ShellStackChild.html
  - name: '>'
  spec.vb:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Controls.SkiaShell
    name: SkiaShell
    href: DrawnUi.Controls.SkiaShell.html
  - name: .
  - uid: DrawnUi.Controls.SkiaShell.ShellStackChild
    name: ShellStackChild
    href: DrawnUi.Controls.SkiaShell.ShellStackChild.html
  - name: )
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.IEquatable`1
  commentId: T:System.IEquatable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  name: IEquatable<T>
  nameWithType: IEquatable<T>
  fullName: System.IEquatable<T>
  nameWithType.vb: IEquatable(Of T)
  fullName.vb: System.IEquatable(Of T)
  name.vb: IEquatable(Of T)
  spec.csharp:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Controls.SkiaShell.ShellStackChild.Part*
  commentId: Overload:DrawnUi.Controls.SkiaShell.ShellStackChild.Part
  href: DrawnUi.Controls.SkiaShell.ShellStackChild.html#DrawnUi_Controls_SkiaShell_ShellStackChild_Part
  name: Part
  nameWithType: SkiaShell.ShellStackChild.Part
  fullName: DrawnUi.Controls.SkiaShell.ShellStackChild.Part
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: DrawnUi.Controls.SkiaShell.ShellStackChild.Control*
  commentId: Overload:DrawnUi.Controls.SkiaShell.ShellStackChild.Control
  href: DrawnUi.Controls.SkiaShell.ShellStackChild.html#DrawnUi_Controls_SkiaShell_ShellStackChild_Control
  name: Control
  nameWithType: SkiaShell.ShellStackChild.Control
  fullName: DrawnUi.Controls.SkiaShell.ShellStackChild.Control
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html

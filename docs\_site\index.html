<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>DrawnUi.Maui | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="DrawnUi.Maui | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="images/favicon.ico">
      <link rel="stylesheet" href="styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="styles/docfx.css">
      <link rel="stylesheet" href="styles/main.css">
      <meta property="docfx:navrel" content="toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="index.html">
                <img id="logo" class="svg" src="images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">
        <div class="article row grid">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="drawnuimaui">DrawnUi.Maui</h1>

<p><strong>Build beautiful, high-performance mobile apps with C# code-behind instead of XAML</strong></p>
<p>DrawnUi is a rendering engine that draws your entire UI on a hardware-accelerated Skia canvas. Create pixel-perfect custom controls with gestures and animations, all powered by <a href="https://github.com/mono/SkiaSharp">SkiaSharp</a> 😍.</p>
<p><strong>Supports:</strong> iOS • MacCatalyst • Android • Windows</p>
<hr>
<h2 id="-quick-start">🚀 Quick Start</h2>
<h3 id="1-install-drawnui">1. Install DrawnUi</h3>
<pre><code class="lang-bash">dotnet add package DrawnUi.Maui
</code></pre>
<h3 id="2-learn-the-basics">2. Learn the Basics</h3>
<ul>
<li><strong><a href="articles/getting-started.html">📖 Getting Started</a></strong> - Installation and setup guide</li>
<li><strong><a href="articles/first-app.html">🎯 Your First App</a></strong> - Build your first DrawnUI app in 5 minutes</li>
<li><strong><a href="articles/fluent-extensions.html">⚡ Fluent Extensions</a></strong> - Master the code-behind fluent API</li>
</ul>
<h3 id="3-explore-controls">3. Explore Controls</h3>
<ul>
<li><strong><a href="articles/controls/index.html">🎛️ All Controls</a></strong> - Buttons, layouts, animations, and more</li>
<li><strong><a href="demo.html">📱 Live Demo</a></strong> - Interactive examples you can try</li>
</ul>
<h3 id="4-go-advanced">4. Go Advanced</h3>
<ul>
<li><strong><a href="articles/advanced/index.html">🏗️ Advanced Topics</a></strong> - Architecture, performance, and platform-specific features</li>
<li><strong><a href="api/index.html">📚 API Reference</a></strong> - Complete technical documentation</li>
</ul>
<hr>
<h2 id="-why-drawnui">✨ Why DrawnUi?</h2>
<p><strong>🎨 Code-Behind First</strong></p>
<ul>
<li>Write UI in C# with fluent extensions - no XAML needed</li>
<li>Type-safe, IntelliSense-friendly development</li>
<li>Reactive property observation without traditional bindings</li>
</ul>
<p><strong>⚡ High Performance</strong></p>
<ul>
<li>Hardware-accelerated Skia rendering</li>
<li>Efficient caching and virtualization</li>
<li>Smooth 60fps animations and gestures</li>
</ul>
<p><strong>🎯 Pixel Perfect</strong></p>
<ul>
<li>Consistent UI across all platforms</li>
<li>Custom controls that look exactly how you want</li>
<li>Full control over every pixel</li>
</ul>
<p><strong>🔧 Flexible Architecture</strong></p>
<ul>
<li>Use alongside existing MAUI controls</li>
<li>Or go fully drawn with SkiaShell navigation</li>
<li>MIT licensed and production-ready</li>
</ul>
<hr>
<h2 id="-see-it-in-action">📱 See It In Action</h2>
<p><strong>Live Examples:</strong></p>
<ul>
<li><strong><a href="https://github.com/taublast/AppoMobi.Maui.DrawnUi.Demo">Engine Demo</a></strong> - Comprehensive control showcase</li>
<li><strong><a href="https://github.com/taublast/AppoMobi.Maui.DrawnUi.SpaceShooter">Space Shooter Game</a></strong> - Full arcade game built with DrawnUI</li>
<li><strong><a href="https://github.com/taublast/SurfAppCompareDrawn">CollectionView Demo</a></strong> - Performance comparison with native controls</li>
<li><strong><a href="https://github.com/taublast/ShadersCarousel/">Shaders Carousel</a></strong> - Advanced SkiaSharp v3 effects</li>
</ul>
<hr>
<h2 id="-need-help">🆘 Need Help?</h2>
<ul>
<li><strong><a href="articles/fluent-extensions.html#troubleshooting">❓ Troubleshooting</a></strong> - Common issues and solutions</li>
<li><strong><a href="https://github.com/taublast/DrawnUi.Maui/issues">💬 GitHub Issues</a></strong> - Report bugs or ask questions</li>
<li><strong><a href="https://taublast.github.io/posts/MauiJuly/">📖 Background Article</a></strong> - Why DrawnUI was created</li>
</ul>
<hr>
<h2 id="faq">FAQ</h2>
<p><strong>Is it DrawnUI or DrawnUi?</strong>
Both are totally fine.</p>
<p><strong>How do I create my custom button?</strong>
While you can use SkiaButton and set a custom content to it, you can also use a click handler Tapped with ANY control you like.</p>
<p><strong>I have an existing MAUI app, how can DrawnUi be beneficial to me?</strong>
You can definitely speed your app by replacing slow UI parts with faster drawn sections, replacing a horde of native controls with just one canvas. Check out the <a href="articles/porting-maui.html">Porting MAUI</a> guide.</p>
<p><strong>Knowing that properties are in points, how do I create a line or stroke of exactly 1 pixel?</strong>
When working with SkiaShape use a negative value (ex: -1) to pass pixels instead of points to compatible properties like StrokeWidth and similar.</p>
<p><strong>How do I bind SkiaImage source not to a file/url but to an existing bitmap?</strong>
Use <code>ImageBitmap</code> property for that, type is <code>LoadedImageSource</code>.</p>
<p><strong>Can DrawnUi use MAUI's default Images folder?</strong>
Unfortunately no. DrawnUi can read from Raw folder and from native storage if the app has written there, but not from the Images folder. It's &quot;hardcoded-designed for MAUI views&quot; and not accessible to DrawnUi controls.</p>
<p><strong>How do I prevent touch input from passing through overlapping controls?</strong>
Use the <code>BlockGesturesBelow=&quot;True&quot;</code> property on the top control. Note that <code>InputTransparent</code> makes the control itself avoid gestures, but doesn't block gestures from reaching controls below it in the Z-axis.</p>
<p><strong>Does DrawnUi work with .NET 9?</strong>
Yes, DrawnUi works with .NET 9. However, remember that SkiaLabel, SkiaLayout etc. are virtual drawn controls that must be placed inside a <code>Canvas</code> control: <code>&lt;draw:Canvas&gt;your skia controls&lt;/draw:Canvas&gt;</code>. Only Canvas has handlers for normal and hardware accelerated views.</p>
<p><strong>How do I enable mouse wheel scrolling in SkiaScroll?</strong>
Mouse wheel scrolling is not built-in by default, but you can easily implement it by subclassing SkiaScroll and overriding the <code>ProcessGestures</code> method to handle <code>TouchActionResult.Wheel</code> events. See the <a href="https://github.com/taublast/DrawnUi/discussions/162">GitHub discussions</a> for a complete implementation example.</p>
<hr>
<p><strong>NOTE: Documentation is under heavy construction AND NOT READY TO USE YET, may contain some outdated or non-exact information!!!</strong></p>
<p><strong>Ready to get started?</strong> → <strong><a href="articles/getting-started.html">Install and Setup Guide</a></strong></p>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/index.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="styles/docfx.js"></script>
    <script type="text/javascript" src="styles/main.js"></script>
  </body>
</html>

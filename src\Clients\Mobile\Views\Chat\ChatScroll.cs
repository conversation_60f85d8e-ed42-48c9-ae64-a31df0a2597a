﻿namespace AppoMobi.Mobile.Views;

public class ChatScroll : SkiaScroll
{
    public ChatScroll()
    {
        HorizontalOptions = LayoutOptions.Fill;
        VerticalOptions = LayoutOptions.Fill;
        TrackIndexPosition = RelativePositionType.Start;
        IndexChanged += OnIndexChanged;

        void OnIndexChanged(object? sender, int index)
        {
            //Debug.WriteLine($"[ChatScroll] last index: {index}");
            if (BindingContext is ChatViewModel vm)
            {
                vm.LastVisibleCellChanged(index);
            }
        }
    }


    public override ISkiaGestureListener ProcessGestures(SkiaGesturesParameters args, GestureEventProcessingInfo apply)
    {
        Superview.FocusedChild = null;

        return base.ProcessGestures(args, apply);
    }

}
﻿using DrawnUi.Controls;
using AppoMobi.Forms.UniversalEditor.Controls;
using AppoMobi.Maui.Gestures;
using DrawnUi.MapsUi;
using static System.Runtime.InteropServices.JavaScript.JSType;
using SkiaShape = DrawnUi.Draw.SkiaShape;

namespace AppoMobi.Mobile.Views
{
    public class ScreenSearchOnMap : AppScreen
    {
        public override ISkiaGestureListener ProcessGestures(SkiaGesturesParameters args,
            GestureEventProcessingInfo apply)
        {
            var consumed = base.ProcessGestures(args, apply);

            //Debug.WriteLine($"[TEST] consumed {args.Type} {consumed}");

            if (consumed == Map && Drawer.IsOpen)
            {
                Drawer.IsOpen = false;
            }

            return consumed;
        }

        protected SearchAddressOnMapViewmodel Model;

        public SkiaButton? ButtonToggle = null;
        public SkiaButton? ButtonPosition = null;
        public SkiaLayout? OverlayButtons = null;
        public SkiaDrawer? Drawer = null;
        public AppMap? Map = null;

        public ScreenSearchOnMap(SearchAddressOnMapViewmodel vm)
        {
            Model = vm;
            BindingContext = Model;

            Shell.SetPresentationMode(this, PresentationMode.ModalAnimated);

            App.Instance.Messager.Subscribe<string>(this, AppMessages.NavigatedToView, async (sender, arg) =>
            {
                if (arg == $"{this}")
                {
                    Model.UpdateState(true);
                }
            });

            BackgroundColor = AppColors.Background;
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;

            var marginTop = 100;
            var headerSize = Super.Screen.BottomInset + 130 + marginTop;
            var drawerSize = 320;

            SkiaLabel? SearchLabel = null;
            SkiaMauiEntry? SearchEntry = null;
            SkiaSvg BtnToggleDrawerIcon;

            Children = new List<SkiaControl>()
            {
                // MAP
                new AppMap()
                    {
                        BackgroundColor = AppColors.Background,
                        UseCache = SkiaCacheType.Operations,
                        HorizontalOptions = LayoutOptions.Fill,
                        VerticalOptions = LayoutOptions.Fill,
                        ZIndex = -1
                    }.Adapt((me) =>
                    {
                        //create if none with Id "To", apply LastClicked
                        MapPin SetupClickedPin(bool show)
                        {
                            var pin = me.Pins.FirstOrDefault(x => x.Id == "To");

                            var lat = me.LastClicked.Y;
                            var lon = me.LastClicked.X;
                            //if (Model.GeoDataTo != null && (lat == 0 || lon == 0))
                            //{
                            //    lat = Model.GeoDataTo.Latitude;
                            //    lon = Model.GeoDataTo.Longitude;
                            //}

                            if (pin == null)
                            {
                                pin = new MapPin()
                                {
                                    Id = "To",
                                    Icon = me.IconFrom,
                                };
                                pin.Latitude = lat;
                                pin.Longitude = lon;
                                me.Pins.Add(pin);
                            }
                            else
                            {
                                pin.IsVisible = show;
                                pin.Latitude = lat;
                                pin.Longitude = lon;
                            }

                            pin.Label = "";
                            pin.Address = "";

                            if (pin.Icon is IMapPinIcon icon)
                            {
                                icon.PinChanged(pin);
                            }

                            //todo remove after implementing static prop
                            me.Update();

                            return pin;
                        }

                        me.ClickedPoint += (s, point) =>
                        {
                            SetupClickedPin(true);

                            Model.SelectCoords(new(point.Y, point.X));
                        };

                        me.Observe(Model, (me, prop) =>
                        {
                            if (prop.IsEither(nameof(BindingContext), nameof(Model.GeoDataTo)))
                            {
                                //change observable collection on Ui thread
                                MainThread.BeginInvokeOnMainThread(() =>
                                {
                                    var pin = SetupClickedPin(Model.GeoDataTo != null);

                                    if (Model.GeoDataTo != null)
                                    {
                                        pin.Label = Model.GeoDataTo.Title;
                                        pin.Address = Model.GeoDataTo.Description;
                                        if (pin.Latitude == 0 || pin.Longitude == 0)
                                        {
                                            pin.Latitude = Model.GeoDataTo.Latitude;
                                            pin.Longitude = Model.GeoDataTo.Longitude;
                                        }
                                    }
                                    else
                                    {
                                        pin.Label = "";
                                        pin.Address = "";
                                    }

                                    if (pin.Icon is IMapPinIcon icon)
                                    {
                                        icon.PinChanged(pin);
                                    }

                                    //todo remove after implementing static prop
                                    me.Update();
                                });
                            }

                            if (prop.IsEither(nameof(BindingContext), nameof(Model.CenterMap)))
                            {
                                if (Model.CenterMap != Point.Zero)
                                {
                                    var point = Model.CenterMap;
                                    var zoom = 10; //me.GetCurrentZoomLevel()
                                    me.AutoCenterTo(point.Y, point.X);
                                    me.LastClicked = point;
                                    //SetupClickedPin(true);
                                }
                            }
                        });
                    })
                    .Assign(out Map),

                // MAP OVERLAY
                new SkiaLayout()
                {
                    VerticalOptions = LayoutOptions.Fill,
                    HorizontalOptions = LayoutOptions.Fill,
                    UseCache = SkiaCacheType.Operations,
                    Children = new List<SkiaControl>()
                    {
                        //Geoposition BUTTON
                        new SkiaButton()
                        {
                            UseCache = SkiaCacheType.Image,
                            BackgroundColor = AppColors.Background,
                            Margin = 8,
                            VerticalOptions = LayoutOptions.Center,
                            HorizontalOptions = LayoutOptions.End,
                            TouchEffectColor = AppColors.PrimaryLight,
                            Children = new List<SkiaControl>()
                            {
                                // button geoposition
                                new SkiaShape()
                                {
                                    Tag = "BtnShape", //convention
                                    WidthRequest = 44,
                                    LockRatio = 1,
                                    Type = ShapeType.Circle,
                                    Children =
                                    {
                                        new SkiaSvg()
                                        {
                                            SvgString = App.Current.Resources.Get<string>("SvgMapArrow"),
                                            HeightRequest = 20,
                                            TintColor = AppColors.ControlPrimary,
                                            LockRatio = 1,
                                            VerticalOptions = LayoutOptions.Center,
                                            HorizontalOptions = LayoutOptions.Center
                                        }
                                    }
                                }
                            },
                            Clicked = async (me, args) => { Model.ApplyUserLocation(); }
                        }.Assign(out ButtonPosition),

                        //TOP BUTTONS
                        new SkiaStack()
                        {
                            Spacing = 0,
                            Children = new List<SkiaControl>()
                            {
                                new StatusBarPlaceholder(),
                                new SkiaLayer()
                                {
                                    Children = new List<SkiaControl>()
                                    {
                                        // GoBack
                                        new SkiaButton()
                                        {
                                            UseCache = SkiaCacheType.Image,
                                            BackgroundColor = AppColors.Background,
                                            Margin = 16,
                                            VerticalOptions = LayoutOptions.Start,
                                            HorizontalOptions = LayoutOptions.Start,
                                            TouchEffectColor = AppColors.PrimaryLight,
                                            Children = new List<SkiaControl>()
                                            {
                                                new SkiaShape()
                                                {
                                                    Tag = "BtnShape", //convention
                                                    WidthRequest = 44,
                                                    LockRatio = 1,
                                                    Type = ShapeType.Circle,
                                                    Children =
                                                    {
                                                        new SkiaSvg()
                                                        {
                                                            SvgString = App.Current.Resources.Get<string>("SvgGoBack"),
                                                            HeightRequest = 20,
                                                            TintColor = AppColors.ControlPrimary,
                                                            LockRatio = 1,
                                                            VerticalOptions = LayoutOptions.Center,
                                                            HorizontalOptions = LayoutOptions.Center
                                                        }
                                                    }
                                                }
                                            },
                                            Clicked = (me, args) => { App.GoBack(); }
                                        },

                                        // ToggleDrawer
                                        new SkiaButton()
                                            {
                                                UseCache = SkiaCacheType.Image,
                                                BackgroundColor = AppColors.Background,
                                                Margin = 16,
                                                VerticalOptions = LayoutOptions.Start,
                                                HorizontalOptions = LayoutOptions.End,
                                                TouchEffectColor = AppColors.PrimaryLight,
                                                Children = new List<SkiaControl>()
                                                {
                                                    new SkiaShape()
                                                    {
                                                        Tag = "BtnShape", //convention
                                                        WidthRequest = 44,
                                                        LockRatio = 1,
                                                        Type = ShapeType.Circle,
                                                        Children =
                                                        {
                                                            new SkiaSvg()
                                                            {
                                                                SvgString = App.Current.Resources.Get<string>(
                                                                    "SvgSearch"),
                                                                HeightRequest = 20,
                                                                TintColor = AppColors.ControlPrimary,
                                                                LockRatio = 1,
                                                                VerticalOptions = LayoutOptions.Center,
                                                                HorizontalOptions = LayoutOptions.Center
                                                            }.Assign(out BtnToggleDrawerIcon)
                                                        }
                                                    }
                                                },
                                                Clicked = (me, args) => { Drawer.IsOpen = !Drawer.IsOpen; }
                                            }.Initialize((me) =>
                                            {
                                                me.Observe(Drawer, (me, prop) =>
                                                {
                                                    if (prop.IsEither(nameof(BindingContext),
                                                            nameof(SkiaDrawer.IsOpen)))
                                                    {
                                                        if (Drawer.IsOpen)
                                                        {
                                                            BtnToggleDrawerIcon.SvgString =
                                                                App.Current.Resources.Get<string>("SvgSearchMap");
                                                        }
                                                        else
                                                        {
                                                            BtnToggleDrawerIcon.SvgString =
                                                                App.Current.Resources.Get<string>("SvgSearch");
                                                        }
                                                    }
                                                });
                                            })
                                            .Assign(out ButtonToggle),
                                    }
                                }
                            }
                        },
                    }
                }.Assign(out OverlayButtons),

                //DRAWER with search
                new SkiaDrawer()
                    {
                        AutoClose = true,
                        BlockGesturesBelow = true,
                        Margin = new Thickness(0, marginTop, 0, 0),
                        Direction = DrawerDirection.FromBottom,
                        HeaderSize = headerSize,
                        VerticalOptions = LayoutOptions.Fill,
                        HorizontalOptions = LayoutOptions.Fill,
                        IsOpen = false,
                        //UseCache = SkiaCacheType.ImageComposite,
                        ZIndex = 1,
                        Content = new SkiaStack()
                        {
                            Spacing = 0,
                            Children = new List<SkiaControl>()
                            {
                                new StatusBarPlaceholder(),
                                new SkiaLayout()
                                {
                                    HorizontalOptions = LayoutOptions.Fill,
                                    VerticalOptions = LayoutOptions.Fill,
                                    Children = new List<SkiaControl>()
                                    {
                                        //background frame with drag indicator
                                        new SkiaShape()
                                        {
                                            CornerRadius = new CornerRadius(20, 20, 0, 0),
                                            HorizontalOptions = LayoutOptions.Fill,
                                            UseCache = SkiaCacheType.Operations,
                                            VerticalOptions = LayoutOptions.Fill,
                                            BackgroundColor = AppColors.Background,
                                            Children =
                                            {
                                                new SkiaLayout()
                                                {
                                                    UseCache = SkiaCacheType.Operations,
                                                    HorizontalOptions = LayoutOptions.Fill,
                                                    VerticalOptions = LayoutOptions.Fill,
                                                    Children = new List<SkiaControl>()
                                                    {
                                                        new SkiaShape()
                                                        {
                                                            //DRAG INDICATOR
                                                            BackgroundColor = Colors.Gainsboro,
                                                            HeightRequest = 3,
                                                            CornerRadius = 8,
                                                            StrokeWidth = -1,
                                                            StrokeColor = Colors.DarkGrey,
                                                            Margin = new Thickness(0, 18),
                                                            VerticalOptions = LayoutOptions.Start,
                                                            HorizontalOptions = LayoutOptions.Center,
                                                            WidthRequest = 32
                                                        },
                                                    }
                                                }
                                            }
                                        },

                                        //drawer CONTENT
                                        new SkiaStack()
                                        {
                                            Padding = new(16, 0),
                                            Margin = new(0, 44, 0, 0),
                                            Type = LayoutType.Column,
                                            Spacing = 8,
                                            HorizontalOptions = LayoutOptions.Fill,
                                            VerticalOptions = LayoutOptions.Fill,
                                            Children = new List<SkiaControl>()
                                            {
                                                // entry
                                                new SkiaLayer()
                                                {
                                                    Children = new List<SkiaControl>()
                                                    {
                                                        new InputFrame()
                                                        {
                                                            Margin = new(0, 0, 48, 0),
                                                            Children = new List<SkiaControl>()
                                                            {
                                                                new SkiaSvg()
                                                                    {
                                                                        UseCache = SkiaCacheType.Operations,
                                                                        TintColor = AppColors.IconSecondary,
                                                                        HeightRequest = 16,
                                                                        LockRatio = 1,
                                                                        SvgString = App.Current.Resources
                                                                            .Get<string>("SvgSearch")
                                                                    }
                                                                    .CenterY(),

                                                                //label simulating entry when drawer closed
                                                                new SkiaScroll()
                                                                    {
                                                                        Orientation = ScrollOrientation.Horizontal,
                                                                        IgnoreWrongDirection = true,
                                                                        Bounces = false,
                                                                        VerticalOptions = LayoutOptions.Fill,
                                                                        HorizontalOptions = LayoutOptions.Fill,
                                                                        Content = new SkiaLabel("Test")
                                                                        {
                                                                            MaxLines = 1,
                                                                            VerticalTextAlignment =
                                                                                TextAlignment.Center,
                                                                            VerticalOptions = LayoutOptions.Fill,
                                                                            HorizontalOptions = LayoutOptions.Fill,
                                                                            LineBreakMode = LineBreakMode.NoWrap
                                                                        }.Assign(out SearchLabel)
                                                                    }
                                                                    .OnTapped((me) =>
                                                                    {
                                                                        if (Drawer.IsOpen)
                                                                        {
                                                                            SearchLabel.IsVisible = false;
                                                                            SearchEntry.IsVisible = true;
                                                                            Tasks.StartDelayed(
                                                                                TimeSpan.FromMilliseconds(100),
                                                                                () =>
                                                                                {
                                                                                    SearchEntry.IsFocused = true;
                                                                                });
                                                                        }
                                                                        else
                                                                        {
                                                                            Drawer.IsOpen = true;
                                                                        }
                                                                    })
                                                                    .WithMargin(20, 0, 20, 0)
                                                                    .Observe(Model, (me, prop) =>
                                                                    {
                                                                        if (prop.IsEither(nameof(BindingContext),
                                                                                nameof(Model.InputText)))
                                                                        {
                                                                            SearchLabel.Text = Model.InputText;
                                                                        }
                                                                    }),

                                                                new AppEntry()
                                                                    {
                                                                        IsVisible = false,
                                                                        Padding = new Thickness(0, 0, 0, 0),
                                                                        //BackgroundColor = AppColors.BrandLightOpacity6
                                                                    }.Fill().WithMargin(20, 0, 20, 0)
                                                                    .Assign(out SearchEntry)
                                                                    .ObserveSelf((me, prop) =>
                                                                    {
                                                                        //if (prop.IsEither(nameof(BindingContext),
                                                                        //        nameof(me.Text)))
                                                                        //{
                                                                        //    Model.InputText = me.Text;
                                                                        //}

                                                                        if (prop.IsEither(nameof(BindingContext),
                                                                                nameof(me.IsFocused)))
                                                                        {
                                                                            if (me.IsFocused)
                                                                            {
                                                                                Drawer.IsOpen = true;
                                                                            }
                                                                        }
                                                                    })
                                                                    //two way binding with 2 methods:
                                                                    .OnTextChanged((me, text) =>
                                                                    {
                                                                        Model.InputText = text;
                                                                    })
                                                                    .Observe(Model, (me, prop) =>
                                                                    {
                                                                        if (prop.IsEither(nameof(BindingContext),
                                                                                nameof(Model.InputText)))
                                                                        {
                                                                            me.Text = Model.InputText;
                                                                        }
                                                                    }),

                                                                new SkiaLayout()
                                                                {
                                                                    Padding = new(3, 0),
                                                                    Children = new List<SkiaControl>()
                                                                    {
                                                                        new SkiaSvg()
                                                                            {
                                                                                UseCache = SkiaCacheType
                                                                                    .Operations,
                                                                                TintColor = AppColors
                                                                                    .IconSecondary,
                                                                                HeightRequest = 12,
                                                                                LockRatio = 1,
                                                                                SvgString = App.Current
                                                                                    .Resources.Get<string>(
                                                                                        "SvgCircleClose")
                                                                            }
                                                                            .CenterY()
                                                                    }
                                                                }.FillY().EndX().Initialize((me) =>
                                                                {
                                                                    me.Observe(SearchEntry, (me, prop) =>
                                                                    {
                                                                        if (prop == "Text" ||
                                                                            prop == nameof(BindingContext))
                                                                        {
                                                                            me.IsVisible =
                                                                                !string.IsNullOrEmpty(
                                                                                    SearchEntry.Text);
                                                                        }
                                                                    });

                                                                    me.Tapped += (sender, args) =>
                                                                    {
                                                                        Model.InputText = "";
                                                                    };
                                                                })
                                                            }
                                                        },

                                                        new SkiaButton()
                                                            //new IconedButton(App.Current.Resources.Get<string>("SvgGoBack"))
                                                            {
                                                                Text = ResStrings.BtnOk,
                                                                BackgroundColor = AppColors.Primary,
                                                                TextColor = Colors.Black,
                                                                FontFamily = AppFonts.SemiBold,
                                                                HorizontalOptions = LayoutOptions.End,
                                                                MinimumWidthRequest = 40,
                                                                VerticalOptions = LayoutOptions.Fill
                                                            }.Adapt((btn) =>
                                                        {
                                                            btn.Clicked += (button, parameters) =>
                                                            {
                                                                SearchEntry.IsFocused = false;
                                                                Model.CommandSubmitResult?.Execute(
                                                                    SearchEntry.Text);
                                                            };
                                                        })
                                                    }
                                                },

                                                // searrch results
                                                new SkiaScroll()
                                                {
                                                    IgnoreWrongDirection = true,
                                                    HorizontalOptions = LayoutOptions.Fill,
                                                    VerticalOptions = LayoutOptions.Fill,

                                                    Content = new SkiaLayout()
                                                        {
                                                            UseCache = SkiaCacheType.Image,
                                                            HorizontalOptions = LayoutOptions.Fill,
                                                            RecyclingTemplate = RecyclingTemplate.Enabled,
                                                            MeasureItemsStrategy = MeasuringStrategy.MeasureAll,
                                                            Spacing = 0,
                                                            Type = LayoutType.Column,
                                                            ItemTemplateType = typeof(CellSearchAddress),
                                                        }
                                                        .Observe(Model, (me, prop) =>
                                                        {
                                                            if (prop.IsEither(nameof(BindingContext),
                                                                    nameof(Model.HasData)))
                                                            {
                                                                if (Model.HasData)
                                                                    me.ItemsSource = Model.Storage.Items;
                                                            }
                                                        })
                                                        .Adapt((layout) =>
                                                        {
                                                            layout.ChildTapped += (sender, args) =>
                                                            {
                                                                if (args.Control is SkiaControl child)
                                                                {
                                                                    Map.WillResetZoom();
                                                                    Model.CommandSelectFromList.Execute(
                                                                        child.BindingContext);
                                                                }
                                                            };
                                                        })
                                                },


                                                // Activity Indicator
                                                /*
                                                new AppActivityIndicator()
                                                    {
                                                        WidthRequest = 40,
                                                        LockRatio = 1,
                                                        IsRunning = true,
                                                        HorizontalOptions = LayoutOptions.Center
                                                    }
                                                    .ObserveBindingContext<AppActivityIndicator, IUniversalSearchViewModel>(
                                                        (me, vm, prop) =>
                                                        {
                                                            bool attached = prop == nameof(BindingContext);
                                                            if (attached || prop == nameof(vm.IsBusy))
                                                            {
                                                                me.IsVisible = vm.IsBusy;
                                                            }
                                                        }),
                                                        */

                                                new KeyboardPlaceholder()
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    .Initialize((me) =>
                    {
                        me.StateTransitionComplete += (s, open) =>
                        {
                            Debug.WriteLine($"[TransitionComplete] at {open}");
                            Model.EditMode = open;
                            if (open)
                            {
                                SearchLabel.IsVisible = false;
                                SearchEntry.IsVisible = true;
                                SearchEntry.IsFocused = true;
                            }
                            else
                            {
                                SearchEntry.IsFocused = false;
                                SearchLabel.IsVisible = true;
                                SearchEntry.IsVisible = false;
                            }
                        };
                    }).Assign(out Drawer)
            };
        }

        public override void OnWillDisposeWithChildren()
        {
            base.OnWillDisposeWithChildren();

            App.Instance.Messager.Unsubscribe(this, AppMessages.NavigatedToView);
        }
        /*
                   <maps:RaceBoxMap
               x:Name="MainMap"
               HorizontalOptions="Fill"
               NeedPath="True"
               PathCoords="{Binding Path}"
               VerticalOptions="Fill">


           private IEnumerable<(double, double)> _Path;
           private string _displaySatellites = _placeholder;

           public IEnumerable<(double, double)> Path
           {
               get
               {
                   return _Path;
               }
               set
               {
                   if (_Path != value)
                   {
                       _Path = value;
                       OnPropertyChanged();
                   }
               }
           }

         */
    }
}

<div id="sidetoggle">
  <div>
    <div class="sidefilter">
      <form class="toc-filter">
        <span class="glyphicon glyphicon-filter filter-icon"></span>
        <span class="glyphicon glyphicon-remove clear-icon" id="toc_filter_clear"></span>
        <input type="text" id="toc_filter_input" placeholder="Filter by title" onkeypress="if(event.keyCode==13) {return false;}">
      </form>
    </div>
    <div class="sidetoc">
      <div class="toc" id="toc">

          <ul class="nav level1">
                <li>
                    <a href="index.html" name="" title="Controls Overview">Controls Overview</a>
                </li>
                <li>
                    <a href="buttons.html" name="" title="Buttons">Buttons</a>
                </li>
                <li>
                    <a href="switches.html" name="" title="Switches and Toggles">Switches and Toggles</a>
                </li>
                <li>
                    <a href="layouts.html" name="" title="Layout Controls">Layout Controls</a>
                </li>
                <li>
                    <a href="scroll.html" name="" title="Scroll Views">Scroll Views</a>
                </li>
                <li>
                    <a href="carousels.html" name="" title="Carousels">Carousels</a>
                </li>
                <li>
                    <a href="drawers.html" name="" title="Drawers">Drawers</a>
                </li>
                <li>
                    <a href="native-integration.html" name="" title="Native Integration">Native Integration</a>
                </li>
                <li>
                    <a href="shapes.html" name="" title="Shapes">Shapes</a>
                </li>
                <li>
                    <a href="text.html" name="" title="Text and Labels">Text and Labels</a>
                </li>
                <li>
                    <a href="images.html" name="" title="Images">Images</a>
                </li>
                <li>
                    <a href="sprites.html" name="" title="Sprites">Sprites</a>
                </li>
                <li>
                    <a href="animations.html" name="" title="Animations">Animations</a>
                </li>
                <li>
                    <a href="shell.html" name="" title="Navigation Shell">Navigation Shell</a>
                </li>
          </ul>
      </div>
    </div>
  </div>
</div>

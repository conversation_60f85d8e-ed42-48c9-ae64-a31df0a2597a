﻿using AppoMobi.Mobile.Views;
using DrawnUi.Controls;

namespace AppoMobi.Mobile;

/// <summary>
/// User profile editor screen
/// </summary>
public class ScreenTextEditor : AppScreen, IQueryAttributable
{
    public void ApplyQueryAttributes(IDictionary<string, object> query)
    {
        foreach (var key in query.Keys)
        {
            if (query.TryGetValue(key, out var value))
            {
                if (key == "value")
                {
                    Value = (string)value;
                }
                else if (key == "title")
                {
                    _title = (string)value;
                }
                else if (key == "callback")
                {
                    _callback = (Action<string>)value;
                }
            }
        }
    }

    private Action<string> _callback;
    private string _title;
    private string _value;
    private readonly bool _multiline;

    public string Value
    {
        get { return _value; }
        set
        {
            var newValue = Uri.UnescapeDataString(value ?? string.Empty);
            if (_value != newValue)
            {
                _value = newValue;
                OnPropertyChanged();
                OnParametersSet(_value);
            }
        }
    }

    private void OnParametersSet(string id)
    {
        Debug.WriteLine($"[ModalScreenEnterText] OnParametersSet: {Value}");
    }

    public ScreenTextEditor
        (string Title, string value, bool multiline, Action<string> callback)
    {
        _callback = callback;
        _title = Title;
        _value = value;
        _multiline = multiline;

        BindingContext = this;

        Shell.SetPresentationMode(this, PresentationMode.ModalAnimated);
        BackgroundColor = Colors.Transparent; //for modal

        Margin = UiHelper.ModalInsets;
        HorizontalOptions = LayoutOptions.Fill;
        VerticalOptions = LayoutOptions.Fill;

        Type = LayoutType.Column;
        Spacing = 0;

        Rendered += (s, a) =>
        {
            Tasks.StartDelayed(TimeSpan.FromMilliseconds(500), () =>
            {
                try
                {
                    MainEditor.IsFocused = true;
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
            });
        };

        CreateContent();
    }

    SkiaMauiEditor MainEditor;

    private void CreateContent()
    {
        Children = new List<SkiaControl>
        {
            new StatusBarPlaceholder(),

            // HEADER
            new ModalHeader
            {
                Title = _title,
            },

            // CONTENT
            new SkiaShape()
            {
                //dot not cache cuz native element inside
                StrokeColor = AppColors.ControlPrimary,
                StrokeWidth = 1,
                Tag = "PopupFrame",
                BackgroundColor = AppColors.Background,
                HorizontalOptions = LayoutOptions.Fill,
                VerticalOptions = LayoutOptions.Fill,
                Children =
                {
                    new ScreenVerticalStack()
                    {
                        Padding = new(16),
                        Children = new List<SkiaControl>()
                        {
                            new AppFrame()
                            {
                                Padding = new(4, 2),
                                Children =
                                {
                                    new SkiaLayout()
                                    {
                                        HorizontalOptions = LayoutOptions.Fill,
                                        Type = LayoutType.Column,
                                        Spacing = 0,
                                        Children = new List<SkiaControl>()
                                        {
                                            // Text Entry
                                            new AppEditor(_value)
                                                {
                                                    MaxLines = 1,
                                                    HeightRequest = 32,
                                                    Placeholder = "...",
                                                    PlaceholderColor = AppColors.Primary,
                                                    Padding = new Thickness(0, 2, 0, 4),
                                                    VerticalOptions = LayoutOptions.Start,
                                                }.Assign(out MainEditor)
                                                .Adapt((entry) =>
                                                {
                                                    //todo size, lines upon settings
                                                    if (_multiline)
                                                    {
                                                        entry.MaxLines = -1;
                                                        entry.HeightRequest = 180;
                                                        entry.VerticalOptions = LayoutOptions.Start;
                                                    }

                                                    entry.SetBinding(SkiaMauiEditor.TextProperty, $"Value",
                                                        BindingMode.TwoWay);

                                                    // Set up focus trigger
                                                    entry.PropertyChanged += (s, e) =>
                                                    {
                                                        if (e.PropertyName == nameof(entry.IsFocused))
                                                        {
                                                            entry.TextColor = entry.IsFocused
                                                                ? AppColors.PrimaryDark
                                                                : AppColors.Text;
                                                        }
                                                    };
                                                }),
                                        }
                                    }
                                }
                            },


                            // Submit Button
                            new ButtonMedium()
                            {
                            UseCache = SkiaCacheType.Image,
                            HorizontalOptions = LayoutOptions.Center,
                            WidthRequest = 250,
                            Margin = 0,
                            HeightRequest = 44,
                            Text = ResStrings.BtnOk,
                        }.OnTapped((btn) =>
                        {
                            App.Instance.Presentation.Shell.GoBack(true);
                            _callback?.Invoke(Value);
                        }),

                        // Keyboard Offset
                        new KeyboardPlaceholder()
                    }
                }
            }
        }
        };
    }
}
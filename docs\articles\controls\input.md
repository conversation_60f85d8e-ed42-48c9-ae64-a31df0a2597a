# Input Controls

DrawnUi provides various input controls for user interaction and data entry.

## SkiaSlider

SkiaSlider provides a slider control with range selection capability.

### Basic Usage

```xml
<draw:SkiaSlider
    Minimum="0"
    Maximum="100"
    Value="50"
    WidthRequest="300"
    HeightRequest="40"
    TrackColor="LightGray"
    ThumbColor="Blue" />
```

### Range Slider

For selecting a range of values:

```xml
<draw:SkiaSlider
    Minimum="0"
    Maximum="100"
    ValueStart="25"
    ValueEnd="75"
    IsRange="True"
    WidthRequest="300"
    HeightRequest="40"
    TrackColor="LightGray"
    RangeColor="Blue"
    ThumbColor="DarkBlue" />
```

### Key Properties

| Property | Type | Description |
|----------|------|-------------|
| `Value` | double | Current value (single slider) |
| `ValueStart` | double | Start value (range slider) |
| `ValueEnd` | double | End value (range slider) |
| `Minimum` | double | Minimum allowed value |
| `Maximum` | double | Maximum allowed value |
| `IsRange` | bool | Whether to enable range selection |
| `Step` | double | Step increment for value changes |
| `TrackColor` | Color | Color of the slider track |
| `RangeColor` | Color | Color of the selected range |
| `ThumbColor` | Color | Color of the thumb(s) |

### Events

```csharp
// Value changed event
mySlider.ValueChanged += (sender, e) => {
    Console.WriteLine($"New value: {e.NewValue}");
};

// Range changed event (for range sliders)
mySlider.RangeChanged += (sender, e) => {
    Console.WriteLine($"Range: {e.Start} - {e.End}");
};
```

## SkiaWheelPicker

SkiaWheelPicker provides an iOS-style picker wheel for selecting from a list of options.

### Basic Usage

```xml
<draw:SkiaWheelPicker
    ItemsSource="{Binding Countries}"
    SelectedItem="{Binding SelectedCountry}"
    WidthRequest="200"
    HeightRequest="150"
    ItemHeight="40"
    VisibleItemCount="5" />
```

### Custom Item Template

```xml
<draw:SkiaWheelPicker
    ItemsSource="{Binding TimeSlots}"
    SelectedIndex="{Binding SelectedTimeIndex}"
    WidthRequest="150"
    HeightRequest="200">
    
    <draw:SkiaWheelPicker.ItemTemplate>
        <DataTemplate>
            <draw:SkiaLabel
                Text="{Binding DisplayText}"
                TextColor="Black"
                FontSize="16"
                HorizontalTextAlignment="Center"
                VerticalTextAlignment="Center" />
        </DataTemplate>
    </draw:SkiaWheelPicker.ItemTemplate>
    
</draw:SkiaWheelPicker>
```

### Key Properties

| Property | Type | Description |
|----------|------|-------------|
| `ItemsSource` | IEnumerable | Collection of items to display |
| `SelectedItem` | object | Currently selected item |
| `SelectedIndex` | int | Index of selected item |
| `ItemHeight` | double | Height of each item |
| `VisibleItemCount` | int | Number of visible items |
| `IsLooped` | bool | Whether picker wraps around |
| `ItemTemplate` | DataTemplate | Template for custom item appearance |

## SkiaHotspot

SkiaHotspot provides an invisible area for handling gestures in a lazy way.

### Basic Usage

```xml
<draw:SkiaLayout>
    <draw:SkiaImage Source="background.png" />
    
    <!-- Invisible hotspot over specific area -->
    <draw:SkiaHotspot
        WidthRequest="100"
        HeightRequest="100"
        HorizontalOptions="Start"
        VerticalOptions="Start"
        Margin="50,50,0,0"
        Tapped="OnHotspotTapped" />
        
</draw:SkiaLayout>
```

### Gesture Handling

```csharp
private void OnHotspotTapped(object sender, EventArgs e)
{
    // Handle tap gesture
    DisplayAlert("Hotspot", "Area tapped!", "OK");
}
```

### Key Properties

| Property | Type | Description |
|----------|------|-------------|
| `IsVisible` | bool | Whether hotspot is visible (for debugging) |
| `BackgroundColor` | Color | Background color (usually transparent) |
| `Gestures` | GesturesMode | Types of gestures to handle |

### Events

- `Tapped`: Single tap gesture
- `DoubleTapped`: Double tap gesture
- `LongPressed`: Long press gesture
- `Panning`: Pan/drag gesture
- `Pinching`: Pinch/zoom gesture

## Examples

### Volume Control Slider

```xml
<draw:SkiaLayout Type="Row" Spacing="10" VerticalOptions="Center">
    
    <draw:SkiaImage
        Source="volume_low.png"
        WidthRequest="24"
        HeightRequest="24" />
    
    <draw:SkiaSlider
        Minimum="0"
        Maximum="100"
        Value="{Binding Volume}"
        WidthRequest="200"
        HeightRequest="30"
        TrackColor="LightGray"
        ThumbColor="Blue"
        Step="1" />
    
    <draw:SkiaImage
        Source="volume_high.png"
        WidthRequest="24"
        HeightRequest="24" />
        
</draw:SkiaLayout>
```

### Time Picker Wheel

```xml
<draw:SkiaLayout Type="Row" Spacing="20" HorizontalOptions="Center">
    
    <!-- Hours -->
    <draw:SkiaWheelPicker
        ItemsSource="{Binding Hours}"
        SelectedItem="{Binding SelectedHour}"
        WidthRequest="80"
        HeightRequest="150"
        ItemHeight="30" />
    
    <draw:SkiaLabel Text=":" FontSize="24" VerticalOptions="Center" />
    
    <!-- Minutes -->
    <draw:SkiaWheelPicker
        ItemsSource="{Binding Minutes}"
        SelectedItem="{Binding SelectedMinute}"
        WidthRequest="80"
        HeightRequest="150"
        ItemHeight="30" />
        
</draw:SkiaLayout>
```

## Performance Tips

- Use `Step` property on sliders to limit precision and improve performance
- Consider using `IsLooped="False"` on wheel pickers for better performance with large datasets
- SkiaHotspot is lightweight and ideal for invisible interaction areas
- Combine multiple input controls for complex data entry scenarios

﻿using AppoMobi.Maui.Gestures;
using DrawnUi.Controls;
using DrawnUi.Views;
using System.ComponentModel;
 

namespace AppoMobi.Mobile.Views;

public class ScreenChat : AppScreen
{
    public readonly ChatViewModel Model;

    public ScreenChat(ChatViewModel vm)
    {
        //manually setting stuff, not from route
        vm.Id = "support";
        vm.OnParametersSet();

        Model = vm;
        BindingContext = Model;

        Shell.SetPresentationMode(this, PresentationMode.ModalAnimated);
        BackgroundColor = Colors.Transparent; //for modal

        Margin = UiHelper.ModalInsets;
        HorizontalOptions = LayoutOptions.Fill;
        VerticalOptions = LayoutOptions.Fill;

        Type = LayoutType.Column;
        Spacing = 0;
        CreateContent();

        App.Instance.Messager.Subscribe<string>(this, AppMessages.Shell, async (sender, arg) =>
        {
            if (arg == "PopupsClosed")
            {
                MainEntry.IsVisible = true;
            }
            else if (arg == "PopupOpen")
            {
                MainEntry.IsVisible = false;
            }
        });
    }

    SkiaScroll MainScroll;
    ChatEntry MainEntry;
    SkiaLayout StackCells;
    SkiaLayout BarInput;

    protected void CreateContent()
    {
        Children = new List<SkiaControl>()
        {
            //new SkiaSvg()
            //{
            //    SvgString = App.Current.Resources.Get<string>("SvgConstruction"),
            //    VerticalOptions = LayoutOptions.Center,
            //    HorizontalOptions = LayoutOptions.Center,
            //    HeightRequest = 150,
            //    LockRatio = 1,
            //    TintColor = Color.Parse("#22000000")
            //},

            new StatusBarPlaceholder(),

            // HEADER
            new ModalHeader
            {
                Title = ResStrings.Support
            },

            // CONTENT
            new ChatContentGrid()
                {
                    BackgroundColor = AppColors.Background,
                    Children = new List<SkiaControl>()
                    {
                        //offline indicator
                        new SkiaLabel()
                            {
                                IsVisible = false,
                                Margin = 16,
                                Text = ResStrings.Offline,
                                TextColor = AppColors.TextPrimaryOpacity6,
                                FontFamily = AppFonts.Bold,
                                FontSize = 16
                            }
                            .Observe(Model, (me, prop) =>
                            {
                                bool attached = prop == nameof(BindingContext);
                                if (attached || prop == nameof(Model.IsOnline))
                                {
                                    me.IsVisible = !Model.IsOnline;
                                    Debug.WriteLine($"[ONLINE] {Model.IsOnline} for {prop}");
                                }
                            }),

                        // CHAT MESSAGES
                        new ChatScroll()
                            {
                                //IsParentIndependent = true,
                                Rotation = 180,
                                ReverseGestures = true,
                                LoadMoreCommand = Model.LoaderItems.CommandLoadMore,
                                Header = new SkiaControl()
                                {
                                    HeightRequest = 16,
                                    HorizontalOptions = LayoutOptions.Fill,
                                },
                                Footer = new SkiaLayout()
                                {
                                    HorizontalOptions = LayoutOptions.Fill,
                                    HeightRequest = 50,
                                    //Children = new List<SkiaControl>()
                                    //{
                                    //    new SkiaLayout()
                                    //    {
                                    //        BackgroundColor = Colors.Red,
                                    //        HorizontalOptions = LayoutOptions.Fill,
                                    //        VerticalOptions = LayoutOptions.Fill,
                                    //        Children = new List<SkiaControl>()
                                    //        {
                                    //            new AppActivityIndicator()
                                    //            {
                                    //                IsRunning = true,
                                    //            },
                                    //        }
                                    //    }.Observe(Model.LoaderItems, (me, prop) =>
                                    //    {
                                    //        if (prop.IsEither(nameof(BindingContext),
                                    //                nameof(Model.LoaderItems.IsLoadingMore)))
                                    //        {
                                    //            me.IsVisible = Model.LoaderItems.IsLoadingMore;
                                    //        }
                                    //    })
                                    //}
                                },
                                //RefreshEnabled = true,
                                //RefreshIndicator = new ScrollRefreshIndicator()
                                //{
                                //    WidthRequest = 40,
                                //    LockRatio = 1,
                                //    HorizontalOptions = LayoutOptions.Center
                                //},
                                Content = new ChatCellStack()
                                {
                                    //IsParentIndependent = true,
                                    RecyclingTemplate = RecyclingTemplate.Enabled,
                                    MeasureItemsStrategy = MeasuringStrategy.MeasureAll,
                                    VirtualisationInflated = 40,
                                    ItemTemplateType = typeof(ChatMessageCell),
                                    ItemsSource = Model.LoaderItems.Items,
                                    Margin = new(8, 0),
                                }.Assign(out StackCells)
                            }
                            .Assign(out MainScroll)
                            .WithRow(0)
                            .Observe(Model, (me, prop) =>
                            {
                                if (prop.IsEither(nameof(BindingContext),
                                        nameof(Model.ScrollToIndex)))
                                {
                                    me.OrderedScroll = Model.ScrollToIndex;
                                    Model.ScrollToIndex = -1;
                                }
                            }),

                        //ERROR VIEW
                        new ConnectionErrorView()
                            .WithRow(0),

                        //EMPTY VIEW
                        new SkiaStack()
                            {
                                IsVisible = false,
                                UseCache = SkiaCacheType.Image,
                                VerticalOptions = LayoutOptions.Center,
                                Spacing = 24,
                                Margin = new(24, 32),
                                Children = new List<SkiaControl>()
                                {
                                    new HintCardIcon()
                                    {
                                        SvgString = App.Current.Resources.Get<string>(
                                            "SvgDuoOperator")
                                    },

                                    new SkiaLabel(ResStrings.ChatWelcome)
                                    {
                                        TextColor = AppColors.Text,
                                        FontFamily = AppFonts.SemiBold,
                                        HorizontalOptions = LayoutOptions.Fill,
                                        HorizontalTextAlignment = DrawTextAlignment.Center,
                                    }
                                }
                            }
                            .WithRow(0)
                            .ObserveBindingContext<SkiaLayout, ChatViewModel>((me, vm,
                                prop) =>
                            {
                                bool attached = prop == nameof(BindingContext);
                                if (attached || prop == nameof(vm.IsBusy))
                                {
                                    me.IsVisible = !vm.IsBusy && vm.CanSend && Model.LoaderItems.Items.Count == 0;
                                }
                            }),

                        //INITIAL LOADER
                        new AppActivityIndicator()
                            {
                                AddMarginTop = 50,
                                IsRunning = true,
                            }
                            .ObserveBindingContext<AppActivityIndicator, ChatViewModel>((me, vm, prop) =>
                            {
                                bool attached = prop == nameof(BindingContext);
                                if (attached || prop == nameof(vm.IsBusy))
                                {
                                    me.IsVisible = !vm.HasData && vm.IsBusy;
                                }
                            }),

#if DEBUG
                        new SkiaLabel()
                        {
                            UseCache = SkiaCacheType.Operations,
                            BackgroundColor = Color.Parse("#66ff0000"),
                            TextColor = WhiteColor,
                            HorizontalOptions = LayoutOptions.Fill,
                            VerticalOptions = LayoutOptions.Start,
                        }.Observe(() => StackCells, (me, prop) => { me.Text = StackCells.DebugString; }),
#endif

                        // you have unread messages - scroll to unread
                        //todo        .WithRow(1)

                        // ATTACHMENT-REPLY WHILE TYPING
                        new SkiaLayout()
                            {
                                UseCache = SkiaCacheType.Image,
                                BackgroundColor = AppColors.BackgroundMinor,
                                Padding = 8,
                                RowSpacing = 0,
                                ColumnSpacing = 8,
                                Type = LayoutType.Grid,
                                HorizontalOptions = LayoutOptions.Fill,
                                Children = new List<SkiaControl>()
                                {
                                    new SkiaLabel()
                                    {
                                        Margin = new(0, 0, 12, 0),
                                        FontFamily = "FaSolid",
                                        HorizontalOptions = LayoutOptions.End,
                                        Text = FaPro.Xmark,
                                        TextColor = AppColors.TextMinor,
                                        VerticalOptions = LayoutOptions.Center,
                                    }.SetGrid(2, 0, 1, 2),

                                    new SkiaSvg()
                                    {
                                        HeightRequest = 20,
                                        HorizontalOptions = LayoutOptions.Center,
                                        SvgString = App.Current.Resources.Get<string>("SvgReply"),
                                        TintColor = AppColors.Primary,
                                        VerticalOptions = LayoutOptions.Center,
                                        LockRatio = 1,
                                    }.SetGrid(0, 0, 1, 2),

                                    new SkiaLabel()
                                        {
                                            FontSize = 14,
                                            LineBreakMode = LineBreakMode.HeadTruncation,
                                            MaxLines = 2,
                                            TextColor = AppColors.Primary
                                        }
                                        .ObserveBindingContext<SkiaLabel, ChatViewModel>((me, vm, prop) =>
                                        {
                                            bool attached = prop == nameof(BindingContext);
                                            if (attached || prop == nameof(vm.SelectedMessageFull))
                                            {
                                                me.Text = vm.SelectedMessageFull.PlayerName;
                                            }
                                        })
                                        .SetGrid(1, 0),

                                    new SkiaLabel()
                                        {
                                            FontSize = 14,
                                            LineBreakMode = LineBreakMode.HeadTruncation,
                                            MaxLines = 2,
                                        }
                                        .ObserveBindingContext<SkiaLabel, ChatViewModel>((me, vm, prop) =>
                                        {
                                            bool attached = prop == nameof(BindingContext);
                                            if (attached || prop == nameof(vm.SelectedMessageFull))
                                            {
                                                me.Text = vm.SelectedMessageFull.Text;
                                            }
                                        })
                                        .SetGrid(1, 1),
                                }
                            }
                            .OnTapped((me) => { Model.CommandCancelReply.Execute(null); })
                            .WithRow(2)
                            .WithRowDefinitions("Auto,Auto")
                            .WithColumnDefinitions("Auto,*,40")
                            .Observe(Model, (me, prop) =>
                            {
                                bool attached = prop == nameof(BindingContext);
                                if (attached || prop == nameof(Model.ReplyOn))
                                {
                                    me.IsVisible = Model.ReplyOn;
                                    me.Parent?.Invalidate();
                                    if (Model.ReplyOn)
                                    {
                                        MainEntry.IsFocused = true;
                                    }
                                }
                            }),

                        // SEND BAR
                        new SkiaStack()
                            {
                                IsParentIndependent = true,
                                BackgroundColor = AppColors.Focus,
                                Spacing = 0,
                                Children = new List<SkiaControl>()
                                {
                                    //top separator line
                                    new SkiaShape()
                                    {
                                        HorizontalOptions = LayoutOptions.Fill,
                                        HeightRequest = 1,
                                        BackgroundColor = AppColors.Primary
                                    },

                                    new SkiaLayout()
                                        {
                                            Padding = new(4, 0,4,0),
                                            HeightRequest = 66,
                                            RowSpacing = 0,
                                            Type = LayoutType.Grid,
                                            ColumnSpacing = 8,
                                            HorizontalOptions = LayoutOptions.Fill,
                                            DefaultRowDefinition = new RowDefinition(GridLength.Star),
                                            Children = new List<SkiaControl>()
                                            {
                                                //todo
                                                //new SkiaShape()
                                                //{
                                                //    Margin = 8,
                                                //    Padding = 0,
                                                //    CornerRadius = 20,
                                                //    //bullshit
                                                //}.WithColumn(0),

                                                new SkiaShape()
                                                {
                                                    //Margin = new(0, 0, 0, 0),
                                                    HorizontalOptions = LayoutOptions.Fill,
                                                    VerticalOptions = LayoutOptions.Center,
                                                    BackgroundColor = AppColors.Background,
                                                    Padding = new(8, 2, 0, 2),
                                                    CornerRadius = 16,

                                                    Children =
                                                    {
                                                        new ChatEntry()
                                                            {
                                                                IsParentIndependent = true,
                                                                BackgroundColor = Colors.Transparent,
                                                                LockFocus = true,
                                                                ReturnType = ReturnType.Send,
                                                                Placeholder = ResStrings.PlaceholderYourMessage,
                                                                HorizontalOptions = LayoutOptions.Fill,
                                                                VerticalOptions = LayoutOptions.Start,
                                                                Padding = new Thickness(0, 0, 0, 0),
                                                                MaxLines = 3,
                                                                MinimumHeightRequest = 30,
                                                                TextColor = AppColors.Text,
                                                                PlaceholderColor = AppColors.Primary,
                                                                //SelectOnFocus="False"
                                                                // AutoSize="TextChanges"
                                                                // UnfocusLocked="True"
//#if WINDOWS
//                                                          BackgroundColor = AppColors.BrandLightOpacity6
//#endif
                                                            }
                                                            .Assign(out MainEntry)
                                                            .ObserveSelf((me, prop) =>
                                                            {
                                                                if (prop.IsEither(nameof(BindingContext),
                                                                        nameof(me.Text)))
                                                                {
                                                                    Model.Message = me.Text;
                                                                }

                                                                //if (prop.IsEither(nameof(BindingContext),
                                                                //        nameof(me.IsFocused)))
                                                                //{
                                                                //    if (me.IsFocused)
                                                                //    {
                                                                //        Drawer.IsOpen = true;
                                                                //    }
                                                                //}
                                                            })
                                                            .Observe(Model, (me, prop) =>
                                                            {
                                                                if (prop.IsEither(nameof(BindingContext),
                                                                        nameof(Model.Message)))
                                                                {
                                                                    me.Text = Model.Message;
                                                                }
                                                            })
                                                        }
                                                }.WithColumn(1),

                                                //BTN ATTACH
                                                new SkiaLayer()
                                                    {
                                                        VerticalOptions = LayoutOptions.Fill,
                                                        HorizontalOptions = LayoutOptions.Fill,
                                                        UseCache = SkiaCacheType.Image,
                                                        Children = new List<SkiaControl>()
                                                        {
                                                            new SkiaSvg()
                                                            {
                                                                UseCache = SkiaCacheType.Operations,
                                                                HeightRequest = 20,
                                                                LockRatio = 1,
                                                                SvgString = App.Current.Resources.Get<string>(
                                                                    "SvgAttachment"),
                                                                VerticalOptions = LayoutOptions.Center,
                                                                HorizontalOptions = LayoutOptions.Center,
                                                                TintColor = AppColors.Primary
                                                            },
                                                        }
                                                    }.WithColumn(0)
                                                    .OnTapped((me) => { Model.CommandSelectAttachment.Execute(null); }),

                                                //BTN SEND
                                                new SkiaLayer()
                                                    {
                                                        VerticalOptions = LayoutOptions.Fill,
                                                        HorizontalOptions = LayoutOptions.Fill,
                                                        UseCache = SkiaCacheType.Image,
                                                        Children = new List<SkiaControl>()
                                                        {
                                                            new SkiaSvg()
                                                            {
                                                                UseCache = SkiaCacheType.Operations,
                                                                HeightRequest = 32,
                                                                LockRatio = 1,
                                                                SvgString = App.Current.Resources.Get<string>(
                                                                    "SvgCircle"),
                                                                VerticalOptions = LayoutOptions.Center,
                                                                HorizontalOptions = LayoutOptions.Center,
                                                                TintColor = AppColors.Primary
                                                            },
                                                            new SkiaSvg()
                                                            {
                                                                Margin = new(2, 0, 0, 0),
                                                                UseCache = SkiaCacheType.Operations,
                                                                HeightRequest = 14,
                                                                LockRatio = 1,
                                                                SvgString = App.Current.Resources.Get<string>(
                                                                    "SvgSend"),
                                                                VerticalOptions = LayoutOptions.Center,
                                                                HorizontalOptions = LayoutOptions.Center,
                                                                TintColor = Colors.White
                                                            }
                                                        }
                                                    }.WithColumn(2)
                                                    .OnTapped((me) =>
                                                    {
                                                        //todo  TransformView="{x:Reference IconSend}"
                                                        Debug.WriteLine("SEND TAPPED");
                                                        Model.CommandSubmit.Execute(null);
                                                    }),
                                            }
                                        }
                                        .WithColumnDefinitions("32,*,40"),
                                }
                            }
                            .Assign(out BarInput)
                            .WithRow(3),

                        new KeyboardWIthInsetsPlaceholder()
                        {
                            BackgroundColor = AppColors.ControlPrimary
                        }.WithRow(4),
                    },
                }
                //messages, undread, attachment, sendbar, placeholder
                .WithRowDefinitions("*,Auto,Auto,Auto,Auto"),

            // todo BTN SCROLL TO UNREAD

            // NAVIGATION BLOCK
            /*
            new Area()
            {
                BackgroundColor = AppColors.Background,
                HeightRequest = 64,
                Children = new List<SkiaControl>()
                {
                    // avatar
                    new SkiaShape()
                    {
                        Tag = "Avatar",
                        WidthRequest = 38,
                        LockRatio = 1,
                        Type = ShapeType.Circle,
                        Margin = new(24,0),
                        VerticalOptions = LayoutOptions.Center,
                        Content = new SkiaImage()
                        {
                            BackgroundColor = AppColors.BackgroundMinor,
                            VerticalOptions = LayoutOptions.Fill,
                            HorizontalOptions = LayoutOptions.Fill,
                        }.Observe(Model, (me, prop) =>
                        {
                            if (prop.IsEither(nameof(BindingContext),
                                    nameof(Model.Item)))
                            {
                                me.Source = Model.Item.ImageMain.Small;
                            }
                        })
                    },

                    // name
                    new SkiaLabel()
                    {
                        Margin = new(72,0,0,0),
                        VerticalOptions = LayoutOptions.Center,
                        FontFamily = AppFonts.Bold

                    }.Observe(Model, (me, prop) =>
                    {
                        if (prop.IsEither(nameof(BindingContext),
                                nameof(Model.Item)))
                        {
                            me.Text = Model.Item.Fullname;
                        }
                    }),

                    //line
                    new SkiaShape()
                    {
                        HorizontalOptions = LayoutOptions.Fill,
                        HeightRequest = 3,
                        VerticalOptions = LayoutOptions.End,
                        Margin = new(24, 0, 24, 0),
                        BackgroundColor = AppColors.Primary
                    },
                }
            },
            */
        };

        MainScroll.PropertyChanged += MainScroll_OnPropertyChanged;
    }

    #region IMPORTED

    public override void OnDisappearing()
    {
        base.OnDisappearing();

        Model.CommandClearNotifications.Execute(null);

        MainEntry?.Unfocus();

        Model?.UnregisterAsNotificationsProcessor();

        App.Instance.Messager.Unsubscribe(this, AppMessages.Chat);
    }

    //todo reset upon resume event
    protected bool onceLoad;

    public override void OnAppearing()
    {
        base.OnAppearing();

        Tasks.StartDelayed(TimeSpan.FromMilliseconds(50), () =>
        {
            //Super.Native?.SetBlackTextStatusBar();

            Task.Run(async () =>
            {
                var canUpdate = BindingContext as IUpdateUIState;
                canUpdate?.UpdateState();
            }).ConfigureAwait(false);

            //Model.OnViewAppearing();
            Model?.RegisterAsNotificationsProcessor();
        });

        App.Instance.Messager.Subscribe<string>(this, AppMessages.Chat, async (sender, arg) =>
        {
            if (arg == "Clicked")
            {
                if (MainEntry != null)
                {
                    if (MainEntry.IsFocused)
                    {
                        MainEntry.Unfocus();
                    }
                }
            }
        });
    }

    private double _ColorOpacity;

    public double ColorOpacity
    {
        get { return _ColorOpacity; }
        set
        {
            if (_ColorOpacity != value)
            {
                _ColorOpacity = value;
                OnPropertyChanged();
            }
        }
    }

    private void ScrollView_OnScrolled(object sender, ScrolledEventArgs e)
    {
        var test = ((ScrollView)sender).ScrollY;

        var t = test / 100.0;

        if (t > 1) t = 1.0;

        ColorOpacity = t;
    }

    //public async Task<bool> PreloadImages()
    //{
    //    return await Model.PreloadImages();
    //}

    private void MainEntry_OnCompleted(object sender, EventArgs e)
    {
        var stop = e;
    }

    private void MainScroll_OnPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        //todo

        //if (e.PropertyName == "FirstVisibleCell")
        //    Model?.LastVisibleCellChanged(MainScroll.FirstVisibleCell);
        //else
        //if (e.PropertyName == "LastVisibleCell")
        //    Model?.FirstVisibleCellChanged(MainScroll.LastVisibleCell);
    }

    private void Hotspot_OnDown(object sender, TouchActionEventArgs e)
    {
        if (MainEntry.IsFocused != null && !MainEntry.IsFocused)
        {
            MainEntry.Focus();
        }
    }

    #endregion
}
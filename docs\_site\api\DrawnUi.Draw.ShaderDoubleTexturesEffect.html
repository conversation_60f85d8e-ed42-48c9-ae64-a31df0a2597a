<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Class ShaderDoubleTexturesEffect | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Class ShaderDoubleTexturesEffect | DrawnUi Documentation ">
    
    <meta name="description" content="Base shader effect class that has 2 input textures.">
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect">



  <h1 id="DrawnUi_Draw_ShaderDoubleTexturesEffect" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect" class="text-break">Class ShaderDoubleTexturesEffect</h1>
  <div class="markdown level0 summary"><p>Base shader effect class that has 2 input textures.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
    <div class="level1"><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject">BindableObject</a></div>
    <div class="level2"><a class="xref" href="DrawnUi.Draw.SkiaEffect.html">SkiaEffect</a></div>
    <div class="level3"><a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html">SkiaShaderEffect</a></div>
    <div class="level4"><span class="xref">ShaderDoubleTexturesEffect</span></div>
  </div>
  <div class="implements">
    <h5>Implements</h5>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged">INotifyPropertyChanged</a></div>
    <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable">IDisposable</a></div>
    <div><a class="xref" href="DrawnUi.Draw.IPostRendererEffect.html">IPostRendererEffect</a></div>
    <div><a class="xref" href="DrawnUi.Draw.ISkiaEffect.html">ISkiaEffect</a></div>
    <div><a class="xref" href="DrawnUi.Draw.ICanBeUpdatedWithContext.html">ICanBeUpdatedWithContext</a></div>
    <div><a class="xref" href="DrawnUi.Draw.ICanBeUpdated.html">ICanBeUpdated</a></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_PaintWithShader">SkiaShaderEffect.PaintWithShader</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_UseContextProperty">SkiaShaderEffect.UseContextProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_UseContext">SkiaShaderEffect.UseContext</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_AutoCreateInputTextureProperty">SkiaShaderEffect.AutoCreateInputTextureProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_AutoCreateInputTexture">SkiaShaderEffect.AutoCreateInputTexture</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_CreateSnapshot_DrawnUi_Draw_SkiaDrawingContext_SkiaSharp_SKRect_">SkiaShaderEffect.CreateSnapshot(SkiaDrawingContext, SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_Render_DrawnUi_Draw_DrawingContext_">SkiaShaderEffect.Render(DrawingContext)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_CreateShader_DrawnUi_Draw_DrawingContext_SkiaSharp_SKImage_">SkiaShaderEffect.CreateShader(DrawingContext, SKImage)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_CompiledShader">SkiaShaderEffect.CompiledShader</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_NeedApply">SkiaShaderEffect.NeedApply</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_CreateUniforms_SkiaSharp_SKRect_">SkiaShaderEffect.CreateUniforms(SKRect)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect__template">SkiaShaderEffect._template</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect__templatePlacehodler">SkiaShaderEffect._templatePlacehodler</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_CompileShader">SkiaShaderEffect.CompileShader()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_NormalizeShaderCode_System_String_">SkiaShaderEffect.NormalizeShaderCode(string)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_CompileShader_System_String_System_Boolean_">SkiaShaderEffect.CompileShader(string, bool)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_LoadedCode">SkiaShaderEffect.LoadedCode</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_ApplyShaderSource">SkiaShaderEffect.ApplyShaderSource()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_NeedChangeSource_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaShaderEffect.NeedChangeSource(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_ShaderCodeProperty">SkiaShaderEffect.ShaderCodeProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_ShaderCode">SkiaShaderEffect.ShaderCode</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_ShaderSourceProperty">SkiaShaderEffect.ShaderSourceProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_ShaderSource">SkiaShaderEffect.ShaderSource</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_ShaderTemplateProperty">SkiaShaderEffect.ShaderTemplateProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_ShaderTemplate">SkiaShaderEffect.ShaderTemplate</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_FilterModeProperty">SkiaShaderEffect.FilterModeProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_MipmapModeProperty">SkiaShaderEffect.MipmapModeProperty</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_FilterMode">SkiaShaderEffect.FilterMode</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_MipmapMode">SkiaShaderEffect.MipmapMode</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_Update">SkiaShaderEffect.Update()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_Parent">SkiaEffect.Parent</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_Attach_DrawnUi_Draw_SkiaControl_">SkiaEffect.Attach(SkiaControl)</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_Dettach">SkiaEffect.Dettach()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_Dispose">SkiaEffect.Dispose()</a>
    </div>
    <div>
      <a class="xref" href="DrawnUi.Draw.SkiaEffect.html#DrawnUi_Draw_SkiaEffect_NeedUpdate_Microsoft_Maui_Controls_BindableObject_System_Object_System_Object_">SkiaEffect.NeedUpdate(BindableObject, object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextproperty">BindableObject.BindingContextProperty</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)">BindableObject.ClearValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)">BindableObject.ClearValue(BindablePropertyKey)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue">BindableObject.GetValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset">BindableObject.IsSet(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding">BindableObject.RemoveBinding(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding">BindableObject.SetBinding(BindableProperty, BindingBase)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings">BindableObject.ApplyBindings()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onbindingcontextchanged">BindableObject.OnBindingContextChanged()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanged">BindableObject.OnPropertyChanged(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging">BindableObject.OnPropertyChanging(string)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings">BindableObject.UnapplyBindings()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)">BindableObject.SetValue(BindableProperty, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)">BindableObject.SetValue(BindablePropertyKey, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)">BindableObject.CoerceValue(BindableProperty)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)">BindableObject.CoerceValue(BindablePropertyKey)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.dispatcher">BindableObject.Dispatcher</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontext">BindableObject.BindingContext</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanged">BindableObject.PropertyChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanging">BindableObject.PropertyChanging</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextchanged">BindableObject.BindingContextChanged</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></h6>
  <h6><strong>Assembly</strong>: DrawnUi.Maui.dll</h6>
  <h5 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class ShaderDoubleTexturesEffect : SkiaShaderEffect, INotifyPropertyChanged, IDisposable, IPostRendererEffect, ISkiaEffect, ICanBeUpdatedWithContext, ICanBeUpdated</code></pre>
  </div>
  <h3 id="fields">Fields
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_AssignedControlTo.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.AssignedControlTo%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L419">View Source</a>
  </span>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_AssignedControlTo" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.AssignedControlTo">AssignedControlTo</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected SkiaControl AssignedControlTo</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_ControlFromProperty.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.ControlFromProperty%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L120">View Source</a>
  </span>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ControlFromProperty" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ControlFromProperty">ControlFromProperty</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty ControlFromProperty</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_ControlToProperty.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.ControlToProperty%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L467">View Source</a>
  </span>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ControlToProperty" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ControlToProperty">ControlToProperty</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty ControlToProperty</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_LoadedSecondaryBitmap.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.LoadedSecondaryBitmap%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L311">View Source</a>
  </span>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_LoadedSecondaryBitmap" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.LoadedSecondaryBitmap">LoadedSecondaryBitmap</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected SKBitmap LoadedSecondaryBitmap</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap">SKBitmap</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_PrimarySourceProperty.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.PrimarySourceProperty%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L138">View Source</a>
  </span>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_PrimarySourceProperty" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.PrimarySourceProperty">PrimarySourceProperty</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty PrimarySourceProperty</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_SecondarySourceProperty.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.SecondarySourceProperty%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L315">View Source</a>
  </span>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_SecondarySourceProperty" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.SecondarySourceProperty">SecondarySourceProperty</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly BindableProperty SecondarySourceProperty</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty">BindableProperty</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_SecondaryTexture.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.SecondaryTexture%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L290">View Source</a>
  </span>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_SecondaryTexture" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.SecondaryTexture">SecondaryTexture</h4>
  <div class="markdown level1 summary"><p>Will be normally set by CompileSecondaryTexture</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected SKShader SecondaryTexture</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skshader">SKShader</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_TilingSecondaryTexture.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.TilingSecondaryTexture%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L44">View Source</a>
  </span>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_TilingSecondaryTexture" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.TilingSecondaryTexture">TilingSecondaryTexture</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected SKShaderTileMode TilingSecondaryTexture</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skshadertilemode">SKShaderTileMode</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_ControlFrom.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.ControlFrom%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L126">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ControlFrom_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ControlFrom*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ControlFrom" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ControlFrom">ControlFrom</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl ControlFrom { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_ControlTo.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.ControlTo%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L473">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ControlTo_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ControlTo*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ControlTo" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ControlTo">ControlTo</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public SkiaControl ControlTo { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_LoadedPrimaryBitmap.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.LoadedPrimaryBitmap%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L211">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_LoadedPrimaryBitmap_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.LoadedPrimaryBitmap*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_LoadedPrimaryBitmap" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.LoadedPrimaryBitmap">LoadedPrimaryBitmap</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected SKBitmap LoadedPrimaryBitmap { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skbitmap">SKBitmap</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_PrimarySource.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.PrimarySource%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L145">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_PrimarySource_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.PrimarySource*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_PrimarySource" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.PrimarySource">PrimarySource</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string PrimarySource { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_ResizedPrimaryImage.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.ResizedPrimaryImage%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L210">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ResizedPrimaryImage_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ResizedPrimaryImage*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ResizedPrimaryImage" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ResizedPrimaryImage">ResizedPrimaryImage</h4>
  <div class="markdown level1 summary"><p>Loaded from file</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected SKImage ResizedPrimaryImage { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skimage">SKImage</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_SecondarySource.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.SecondarySource%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L322">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_SecondarySource_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.SecondarySource*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_SecondarySource" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.SecondarySource">SecondarySource</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string SecondarySource { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_ApplyControlTo_DrawnUi_Draw_SkiaControl_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.ApplyControlTo(DrawnUi.Draw.SkiaControl)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L436">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ApplyControlTo_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ApplyControlTo*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ApplyControlTo_DrawnUi_Draw_SkiaControl_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ApplyControlTo(DrawnUi.Draw.SkiaControl)">ApplyControlTo(SkiaControl)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void ApplyControlTo(SkiaControl control)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></td>
        <td><span class="parametername">control</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_CompileSecondaryTexture_SkiaSharp_SKImage_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.CompileSecondaryTexture(SkiaSharp.SKImage)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L292">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_CompileSecondaryTexture_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.CompileSecondaryTexture*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_CompileSecondaryTexture_SkiaSharp_SKImage_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.CompileSecondaryTexture(SkiaSharp.SKImage)">CompileSecondaryTexture(SKImage)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void CompileSecondaryTexture(SKImage image)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skimage">SKImage</a></td>
        <td><span class="parametername">image</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_CreateTexturesUniforms_DrawnUi_Draw_SkiaDrawingContext_SkiaSharp_SKRect_SkiaSharp_SKShader_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.CreateTexturesUniforms(DrawnUi.Draw.SkiaDrawingContext%2CSkiaSharp.SKRect%2CSkiaSharp.SKShader)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L46">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_CreateTexturesUniforms_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.CreateTexturesUniforms*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_CreateTexturesUniforms_DrawnUi_Draw_SkiaDrawingContext_SkiaSharp_SKRect_SkiaSharp_SKShader_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.CreateTexturesUniforms(DrawnUi.Draw.SkiaDrawingContext,SkiaSharp.SKRect,SkiaSharp.SKShader)">CreateTexturesUniforms(SkiaDrawingContext, SKRect, SKShader)</h4>
  <div class="markdown level1 summary"><p>Creates texture uniforms fresh each time</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override SKRuntimeEffectChildren CreateTexturesUniforms(SkiaDrawingContext ctx, SKRect destination, SKShader primaryTexture)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaDrawingContext.html">SkiaDrawingContext</a></td>
        <td><span class="parametername">ctx</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></td>
        <td><span class="parametername">destination</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skshader">SKShader</a></td>
        <td><span class="parametername">primaryTexture</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skruntimeeffectchildren">SKRuntimeEffectChildren</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_CreateTexturesUniforms_DrawnUi_Draw_SkiaDrawingContext_SkiaSharp_SKRect_SkiaSharp_SKShader_">SkiaShaderEffect.CreateTexturesUniforms(SkiaDrawingContext, SKRect, SKShader)</a></div>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_GetPrimaryTextureImage_DrawnUi_Draw_SkiaDrawingContext_SkiaSharp_SKRect_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.GetPrimaryTextureImage(DrawnUi.Draw.SkiaDrawingContext%2CSkiaSharp.SKRect)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L72">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_GetPrimaryTextureImage_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.GetPrimaryTextureImage*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_GetPrimaryTextureImage_DrawnUi_Draw_SkiaDrawingContext_SkiaSharp_SKRect_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.GetPrimaryTextureImage(DrawnUi.Draw.SkiaDrawingContext,SkiaSharp.SKRect)">GetPrimaryTextureImage(SkiaDrawingContext, SKRect)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override SKImage GetPrimaryTextureImage(SkiaDrawingContext ctx, SKRect destination)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="DrawnUi.Draw.SkiaDrawingContext.html">SkiaDrawingContext</a></td>
        <td><span class="parametername">ctx</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skrect">SKRect</a></td>
        <td><span class="parametername">destination</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skimage">SKImage</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_GetPrimaryTextureImage_DrawnUi_Draw_SkiaDrawingContext_SkiaSharp_SKRect_">SkiaShaderEffect.GetPrimaryTextureImage(SkiaDrawingContext, SKRect)</a></div>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_GetSecondaryTexture.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.GetSecondaryTexture%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L265">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_GetSecondaryTexture_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.GetSecondaryTexture*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_GetSecondaryTexture" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.GetSecondaryTexture">GetSecondaryTexture()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual SKShader GetSecondaryTexture()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skshader">SKShader</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_ImportCacheTo.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.ImportCacheTo%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L421">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ImportCacheTo_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ImportCacheTo*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ImportCacheTo" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ImportCacheTo">ImportCacheTo()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void ImportCacheTo()</code></pre>
  </div>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_LoadPrimarySource_System_String_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.LoadPrimarySource(System.String)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L220">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_LoadPrimarySource_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.LoadPrimarySource*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_LoadPrimarySource_System_String_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.LoadPrimarySource(System.String)">LoadPrimarySource(string)</h4>
  <div class="markdown level1 summary"><p>Loading from local files only</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Task LoadPrimarySource(string fileName)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td><span class="parametername">fileName</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_LoadSecondarySource_System_String_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.LoadSecondarySource(System.String)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L379">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_LoadSecondarySource_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.LoadSecondarySource*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_LoadSecondarySource_System_String_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.LoadSecondarySource(System.String)">LoadSecondarySource(string)</h4>
  <div class="markdown level1 summary"><p>Loading from local files only</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Task LoadSecondarySource(string fileName)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td><span class="parametername">fileName</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_OnDisposing.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.OnDisposing%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L30">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_OnDisposing_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.OnDisposing*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_OnDisposing" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.OnDisposing">OnDisposing()</h4>
  <div class="markdown level1 summary"><p>Simplified dispose - only CPU-side resources</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void OnDisposing()</code></pre>
  </div>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_OnDisposing">SkiaShaderEffect.OnDisposing()</a></div>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_ParentReady.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.ParentReady%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L10">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ParentReady_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ParentReady*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ParentReady" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ParentReady">ParentReady()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected bool ParentReady()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_ResizePrimaryLoadedBitmap.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.ResizePrimaryLoadedBitmap%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L171">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ResizePrimaryLoadedBitmap_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ResizePrimaryLoadedBitmap*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ResizePrimaryLoadedBitmap" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ResizePrimaryLoadedBitmap">ResizePrimaryLoadedBitmap()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ResizePrimaryLoadedBitmap()</code></pre>
  </div>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_ResizeSecondaryLoadedBitmapAndCompileTexture.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.ResizeSecondaryLoadedBitmapAndCompileTexture%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L349">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ResizeSecondaryLoadedBitmapAndCompileTexture_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ResizeSecondaryLoadedBitmapAndCompileTexture*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ResizeSecondaryLoadedBitmapAndCompileTexture" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ResizeSecondaryLoadedBitmapAndCompileTexture">ResizeSecondaryLoadedBitmapAndCompileTexture()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ResizeSecondaryLoadedBitmapAndCompileTexture()</code></pre>
  </div>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect_ShouldDisposePreviousTexture_SkiaSharp_SKImage_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect.ShouldDisposePreviousTexture(SkiaSharp.SKImage)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L15">View Source</a>
  </span>
  <a id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ShouldDisposePreviousTexture_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ShouldDisposePreviousTexture*"></a>
  <h4 id="DrawnUi_Draw_ShaderDoubleTexturesEffect_ShouldDisposePreviousTexture_SkiaSharp_SKImage_" data-uid="DrawnUi.Draw.ShaderDoubleTexturesEffect.ShouldDisposePreviousTexture(SkiaSharp.SKImage)">ShouldDisposePreviousTexture(SKImage)</h4>
  <div class="markdown level1 summary"><p>Checks if image is a new snapshot that needs disposal</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override bool ShouldDisposePreviousTexture(SKImage image)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skimage">SKImage</a></td>
        <td><span class="parametername">image</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html#DrawnUi_Draw_SkiaShaderEffect_ShouldDisposePreviousTexture_SkiaSharp_SKImage_">SkiaShaderEffect.ShouldDisposePreviousTexture(SKImage)</a></div>
  <h3 id="implements">Implements</h3>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged">INotifyPropertyChanged</a>
  </div>
  <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable">IDisposable</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.IPostRendererEffect.html">IPostRendererEffect</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.ISkiaEffect.html">ISkiaEffect</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.ICanBeUpdatedWithContext.html">ICanBeUpdatedWithContext</a>
  </div>
  <div>
      <a class="xref" href="DrawnUi.Draw.ICanBeUpdated.html">ICanBeUpdated</a>
  </div>
  <h3 id="extensionmethods">Extension Methods</h3>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_ShaderDoubleTexturesEffect.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.ShaderDoubleTexturesEffect%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A" class="contribution-link">Edit this page</a>
                  </li>
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Features/Effects/ShaderDoubleTexturesEffect.cs/#L8" class="contribution-link">View Source</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>

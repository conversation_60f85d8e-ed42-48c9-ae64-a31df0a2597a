### YamlMime:ManagedReference
items:
- uid: DrawnUi.Controls.IWheelPickerCell
  commentId: T:DrawnUi.Controls.IWheelPickerCell
  id: IWheelPickerCell
  parent: DrawnUi.Controls
  children:
  - DrawnUi.Controls.IWheelPickerCell.UpdateContext(DrawnUi.Controls.WheelCellInfo)
  langs:
  - csharp
  - vb
  name: IWheelPickerCell
  nameWithType: IWheelPickerCell
  fullName: DrawnUi.Controls.IWheelPickerCell
  type: Interface
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PickerWheel/IWheelPickerCell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IWheelPickerCell
    path: ../src/Maui/DrawnUi/Controls/PickerWheel/IWheelPickerCell.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: public interface IWheelPickerCell
    content.vb: Public Interface IWheelPickerCell
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Controls.IWheelPickerCell.UpdateContext(DrawnUi.Controls.WheelCellInfo)
  commentId: M:DrawnUi.Controls.IWheelPickerCell.UpdateContext(DrawnUi.Controls.WheelCellInfo)
  id: UpdateContext(DrawnUi.Controls.WheelCellInfo)
  parent: DrawnUi.Controls.IWheelPickerCell
  langs:
  - csharp
  - vb
  name: UpdateContext(WheelCellInfo)
  nameWithType: IWheelPickerCell.UpdateContext(WheelCellInfo)
  fullName: DrawnUi.Controls.IWheelPickerCell.UpdateContext(DrawnUi.Controls.WheelCellInfo)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PickerWheel/IWheelPickerCell.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: UpdateContext
    path: ../src/Maui/DrawnUi/Controls/PickerWheel/IWheelPickerCell.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: void UpdateContext(WheelCellInfo ctx)
    parameters:
    - id: ctx
      type: DrawnUi.Controls.WheelCellInfo
    content.vb: Sub UpdateContext(ctx As WheelCellInfo)
  overload: DrawnUi.Controls.IWheelPickerCell.UpdateContext*
references:
- uid: DrawnUi.Controls
  commentId: N:DrawnUi.Controls
  href: DrawnUi.html
  name: DrawnUi.Controls
  nameWithType: DrawnUi.Controls
  fullName: DrawnUi.Controls
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Controls.IWheelPickerCell.UpdateContext*
  commentId: Overload:DrawnUi.Controls.IWheelPickerCell.UpdateContext
  href: DrawnUi.Controls.IWheelPickerCell.html#DrawnUi_Controls_IWheelPickerCell_UpdateContext_DrawnUi_Controls_WheelCellInfo_
  name: UpdateContext
  nameWithType: IWheelPickerCell.UpdateContext
  fullName: DrawnUi.Controls.IWheelPickerCell.UpdateContext
- uid: DrawnUi.Controls.WheelCellInfo
  commentId: T:DrawnUi.Controls.WheelCellInfo
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.WheelCellInfo.html
  name: WheelCellInfo
  nameWithType: WheelCellInfo
  fullName: DrawnUi.Controls.WheelCellInfo

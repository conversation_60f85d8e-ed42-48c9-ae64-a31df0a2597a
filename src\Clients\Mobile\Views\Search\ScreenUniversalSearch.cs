﻿using DrawnUi.Controls;
using SkiaButton = DrawnUi.Draw.SkiaButton;

namespace AppoMobi.Mobile.Views;

/// <summary>
/// Our universal search screen for TModel the data model and TCell the cell type that would be used as template for list display.
/// </summary>
/// <typeparam name="TModel"></typeparam>
/// <typeparam name="TCell"></typeparam>
public class ScreenUniversalSearch<TModel, TCell> : AppScreen where TCell : SkiaControl
{
    protected readonly IUniversalSearchViewModel<TModel> Model;

    public ScreenUniversalSearch(IUniversalSearchViewModel<TModel> vm)
    {
        Model = vm;
        BindingContext = Model;

        Shell.SetPresentationMode(this, PresentationMode.ModalAnimated);
        BackgroundColor = Colors.Transparent; //for modal

        Margin = UiHelper.ModalInsets;
        HorizontalOptions = LayoutOptions.Fill;
        VerticalOptions = LayoutOptions.Fill;

        Type = LayoutType.Column;
        Spacing = 0;
        CreateContent();
    }

    protected override void OnLayoutReady()
    {
        base.OnLayoutReady();

        Tasks.StartDelayed(TimeSpan.FromMilliseconds(100), () => { Model.CommandRefreshData.Execute(null); });
    }

    private void CreateContent()
    {
        ModalHeader header;
        SkiaMauiEntry SearchEntry;

        string testEntry = "Москва, Лесная ул, 1";
        string testCell = "Москва, Лесная ул, ";

        Children = new List<SkiaControl>
        {
            new StatusBarPlaceholder(),

            // HEADER
            new ModalHeader().Assign(out header),

            // CONTENT
            new SkiaShape()
            {
                //UseCache = SkiaCacheType.Operations,
                StrokeColor = AppColors.ControlPrimary,
                StrokeWidth = 1,
                BackgroundColor = AppColors.Background,
                HorizontalOptions = LayoutOptions.Fill,
                VerticalOptions = LayoutOptions.Fill,
                Children =
                {
                new ScreenVerticalStack()
                {
                    Type = LayoutType.Column,
                    UseCache = SkiaCacheType.Operations,
                    Spacing = 24,
                    HorizontalOptions = LayoutOptions.Fill,
                    Padding = new(16, 16, 16, 0),
                    Children = new List<SkiaControl>()
                    {

                        new SkiaLayer()
                        {
                            new InputFrame()
                            {
                                Margin = new(0, 0, 48, 0),
                                Children =
                                {
                                              new SkiaLayer()
                                {
                                    VerticalOptions = LayoutOptions.Fill,
                                    Children = new List<SkiaControl>()
                                    {
                                        new SkiaSvg()
                                            {
                                                UseCache = SkiaCacheType.Operations,
                                                TintColor = AppColors.IconSecondary,
                                                HeightRequest = 16,
                                                LockRatio = 1,
                                                SvgString = App.Current.Resources.Get<string>("SvgSearch")
                                            }
                                            .CenterY(),

                                        new AppEntry()
                                            {
                                                Padding = new Thickness(0, 4, 0, 0),
                                                //BackgroundColor = AppColors.BrandLightOpacity6
                                            }.Fill().WithMargin(20, 0, 20, 0)
                                            .Assign(out SearchEntry).Adapt((entry) =>
                                            {
                                                entry.BindProperty(SkiaMauiEntry.TextProperty, "InputText",
                                                    BindingMode.TwoWay);
                                            }),

                                        //new SkiaLabel(testEntry)
                                        //    .SetMargin(20, 0, 20, 0)
                                        //    .CenterY(),
                                        new SkiaLayout()
                                        {
                                            Padding = new(3,0),
                                            Children = new List<SkiaControl>()
                                            {
                                                new SkiaSvg()
                                                    {
                                                        UseCache = SkiaCacheType.Operations,
                                                        TintColor = AppColors.IconSecondary,
                                                        HeightRequest = 12,
                                                        LockRatio = 1,
                                                        SvgString = App.Current.Resources.Get<string>("SvgCircleClose")
                                                    }
                                                    .CenterY()
                                            }
                                        }.FillY().EndX().Initialize((me) =>
                                        {
                                            me.Observe(SearchEntry, (me, prop) =>
                                            {
                                                if (prop == "Text" || prop == nameof(BindingContext))
                                                {
                                                    me.IsVisible = !string.IsNullOrEmpty(SearchEntry.Text);
                                                }
                                            });

                                            me.Tapped += (sender, args) =>
                                            {
                                                Model.InputText = "";
                                            };
                                        })
                                    }
                                }
                                }
                            },

                            new SkiaButton()
                                //new IconedButton(App.Current.Resources.Get<string>("SvgGoBack"))
                                {
                                    Text = ResStrings.BtnOk,
                                    BackgroundColor = AppColors.Primary,
                                    TextColor = Colors.Black,
                                    FontFamily = AppFonts.SemiBold,
                                    HorizontalOptions = LayoutOptions.End,
                                    MinimumWidthRequest = 40,
                                    VerticalOptions = LayoutOptions.Fill
                                }.Adapt((btn) =>
                            {
                                btn.Clicked += (button, parameters) =>
                                {
                                    Model.CommandSubmitResult?.Execute(SearchEntry.Text);
                                };
                            })
                        },

                        //templated items
                        new SkiaScroll()
                        {
                            Margin = new(0, -16, 0, 0),
                            IgnoreWrongDirection = true,
                            HorizontalOptions = LayoutOptions.Fill,
                            VerticalOptions = LayoutOptions.Fill,

                            Content = new SkiaLayout()
                            {
                                HorizontalOptions = LayoutOptions.Fill,
                                RecyclingTemplate = RecyclingTemplate.Enabled,
                                MeasureItemsStrategy = MeasuringStrategy.MeasureAll,
                                Spacing = 0,
                                Type = LayoutType.Column,
                                ItemTemplateType = typeof(TCell),
                                //ItemsSource = new List<string>() { testCell+"1", testCell+"2", testCell+"3", testCell+"4", testCell+"5" },
                            }.Adapt((layout) =>
                            {
                                layout.BindProperty(SkiaLayout.ItemsSourceProperty, "Storage.Items");

                                layout.ChildTapped += (sender, args) =>
                                {
                                    if (args.Control is SkiaControl child)
                                    {
                                        Model.CommandSelectFromList.Execute(child.BindingContext);
                                    }
                                };
                            })
                        },


                        // Activity Indicator
                        /*
                        new AppActivityIndicator()
                            {
                                WidthRequest = 40,
                                LockRatio = 1,
                                IsRunning = true,
                                HorizontalOptions = LayoutOptions.Center
                            }
                            .ObserveBindingContext<AppActivityIndicator, IUniversalSearchViewModel>(
                                (me, vm, prop) =>
                                {
                                    bool attached = prop == nameof(BindingContext);
                                    if (attached || prop == nameof(vm.IsBusy))
                                    {
                                        me.IsVisible = vm.IsBusy;
                                    }
                                }),
                                */

                        // Keyboard Offset
                        new KeyboardPlaceholder()
                    }
                }
                }
            }
        };

        header.Title = Model.Title;
    }
}
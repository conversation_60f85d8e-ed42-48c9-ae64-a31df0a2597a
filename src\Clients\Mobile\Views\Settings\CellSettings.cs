﻿using AppoMobi.Mobile.Infrastructure;
using AppoMobi.Mobile.Views;
using DrawnUi.Extensions;

namespace AppoMobi.Mobile.ViewModels;

public class CellSettings : FastCellWithBanner
{
    protected override bool ApplyContext()
    {
        if (BindingContext is ActionOption item)
        {
            if (LabelTitle != null)
            {
                LabelTitle.Text = item.Title;
            }

            bool showIcon = false;
            if (SvgIcon != null && !string.IsNullOrEmpty(item.SvgResource))
            {
                var svg = App.Current.Resources.Get<string>(item.SvgResource);
                if (!string.IsNullOrEmpty(svg))
                {
                    showIcon = true;
                    SvgIcon.SvgString = svg;
                }
            }
            else
            {
                showIcon = false;
            }

            SvgIcon.IsVisible = showIcon;

            return true;
        }

        return false;
    }

    protected SkiaSvg SvgIcon;

    protected override void FindViews()
    {
        base.FindViews();

        if (SvgIcon == null)
        {
            SvgIcon = FindView<SkiaSvg>("SvgIcon");
        }
    }
}
{"items": [{"name": "DrawnUi", "href": "DrawnUi.html", "topicHref": "DrawnUi.html", "topicUid": "DrawnUi", "type": "Namespace", "items": [{"name": "HotReloadService", "href": "DrawnUi.HotReloadService.html", "topicHref": "DrawnUi.HotReloadService.html", "topicUid": "DrawnUi.HotReloadService", "type": "Class"}]}, {"name": "DrawnUi.Animate.Animators", "href": "DrawnUi.Animate.Animators.html", "topicHref": "DrawnUi.Animate.Animators.html", "topicUid": "DrawnUi.Animate.Animators", "type": "Namespace", "items": [{"name": "VelocitySkiaAnimator", "href": "DrawnUi.Animate.Animators.VelocitySkiaAnimator.html", "topicHref": "DrawnUi.Animate.Animators.VelocitySkiaAnimator.html", "topicUid": "DrawnUi.Animate.Animators.VelocitySkiaAnimator", "type": "Class"}, {"name": "VelocitySkiaAnimator.DragForce", "href": "DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.html", "topicHref": "DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.html", "topicUid": "DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce", "type": "Class"}, {"name": "VelocitySkiaAnimator.MassState", "href": "DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState.html", "topicHref": "DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState.html", "topicUid": "DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState", "type": "Class"}, {"name": "VelocitySkiaAnimator.PresetType", "href": "DrawnUi.Animate.Animators.VelocitySkiaAnimator.PresetType.html", "topicHref": "DrawnUi.Animate.Animators.VelocitySkiaAnimator.PresetType.html", "topicUid": "DrawnUi.Animate.Animators.VelocitySkiaAnimator.PresetType", "type": "Enum"}]}, {"name": "DrawnUi.Controls", "href": "DrawnUi.Controls.html", "topicHref": "DrawnUi.Controls.html", "topicUid": "DrawnUi.Controls", "type": "Namespace", "items": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "href": "DrawnUi.Controls.AnimatedFramesRenderer.html", "topicHref": "DrawnUi.Controls.AnimatedFramesRenderer.html", "topicUid": "DrawnUi.Controls.AnimatedFramesRenderer", "type": "Class"}, {"name": "ContentWithBackdrop", "href": "DrawnUi.Controls.ContentWithBackdrop.html", "topicHref": "DrawnUi.Controls.ContentWithBackdrop.html", "topicUid": "DrawnUi.Controls.ContentWithBackdrop", "type": "Class"}, {"name": "DrawerDirection", "href": "DrawnUi.Controls.DrawerDirection.html", "topicHref": "DrawnUi.Controls.DrawerDirection.html", "topicUid": "DrawnUi.Controls.DrawerDirection", "type": "Enum"}, {"name": "GifAnimation", "href": "DrawnUi.Controls.GifAnimation.html", "topicHref": "DrawnUi.Controls.GifAnimation.html", "topicUid": "DrawnUi.Controls.GifAnimation", "type": "Class"}, {"name": "GridLayout", "href": "DrawnUi.Controls.GridLayout.html", "topicHref": "DrawnUi.Controls.GridLayout.html", "topicUid": "DrawnUi.Controls.GridLayout", "type": "Class"}, {"name": "ISkiaRadioButton", "href": "DrawnUi.Controls.ISkiaRadioButton.html", "topicHref": "DrawnUi.Controls.ISkiaRadioButton.html", "topicUid": "DrawnUi.Controls.ISkiaRadioButton", "type": "Interface"}, {"name": "ISmartNative", "href": "DrawnUi.Controls.ISmartNative.html", "topicHref": "DrawnUi.Controls.ISmartNative.html", "topicUid": "DrawnUi.Controls.ISmartNative", "type": "Interface"}, {"name": "IWheelPickerCell", "href": "DrawnUi.Controls.IWheelPickerCell.html", "topicHref": "DrawnUi.Controls.IWheelPickerCell.html", "topicUid": "DrawnUi.Controls.IWheelPickerCell", "type": "Interface"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "href": "DrawnUi.Controls.MauiEditor.html", "topicHref": "DrawnUi.Controls.MauiEditor.html", "topicUid": "DrawnUi.Controls.MauiEditor", "type": "Class"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "href": "DrawnUi.Controls.MauiEditorHandler.html", "topicHref": "DrawnUi.Controls.MauiEditorHandler.html", "topicUid": "DrawnUi.Controls.MauiEditorHandler", "type": "Class"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "href": "DrawnUi.Controls.MauiEntry.html", "topicHref": "DrawnUi.Controls.MauiEntry.html", "topicUid": "DrawnUi.Controls.MauiEntry", "type": "Class"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "href": "DrawnUi.Controls.MauiEntryHandler.html", "topicHref": "DrawnUi.Controls.MauiEntryHandler.html", "topicUid": "DrawnUi.Controls.MauiEntryHandler", "type": "Class"}, {"name": "NavigationSource", "href": "DrawnUi.Controls.NavigationSource.html", "topicHref": "DrawnUi.Controls.NavigationSource.html", "topicUid": "DrawnUi.Controls.NavigationSource", "type": "Enum"}, {"name": "RadioButtons", "href": "DrawnUi.Controls.RadioButtons.html", "topicHref": "DrawnUi.Controls.RadioButtons.html", "topicUid": "DrawnUi.Controls.RadioButtons", "type": "Class"}, {"name": "ScrollPickerLabelContainer", "href": "DrawnUi.Controls.ScrollPickerLabelContainer.html", "topicHref": "DrawnUi.Controls.ScrollPickerLabelContainer.html", "topicUid": "DrawnUi.Controls.ScrollPickerLabelContainer", "type": "Class"}, {"name": "ScrollPickerWheel", "href": "DrawnUi.Controls.ScrollPickerWheel.html", "topicHref": "DrawnUi.Controls.ScrollPickerWheel.html", "topicUid": "DrawnUi.Controls.ScrollPickerWheel", "type": "Class"}, {"name": "SkiaCarousel", "href": "DrawnUi.Controls.SkiaCarousel.html", "topicHref": "DrawnUi.Controls.SkiaCarousel.html", "topicUid": "DrawnUi.Controls.SkiaCarousel", "type": "Class"}, {"name": "SkiaDecoratedGrid", "href": "DrawnUi.Controls.SkiaDecoratedGrid.html", "topicHref": "DrawnUi.Controls.SkiaDecoratedGrid.html", "topicUid": "DrawnUi.Controls.SkiaDecoratedGrid", "type": "Class"}, {"name": "SkiaDrawer", "href": "DrawnUi.Controls.SkiaDrawer.html", "topicHref": "DrawnUi.Controls.SkiaDrawer.html", "topicUid": "DrawnUi.Controls.SkiaDrawer", "type": "Class"}, {"name": "SkiaDrawnCell", "href": "DrawnUi.Controls.SkiaDrawnCell.html", "topicHref": "DrawnUi.Controls.SkiaDrawnCell.html", "topicUid": "DrawnUi.Controls.SkiaDrawnCell", "type": "Class"}, {"name": "SkiaDynamicDrawnCell", "href": "DrawnUi.Controls.SkiaDynamicDrawnCell.html", "topicHref": "DrawnUi.Controls.SkiaDynamicDrawnCell.html", "topicUid": "DrawnUi.Controls.SkiaDynamicDrawnCell", "type": "Class"}, {"name": "SkiaGif", "href": "DrawnUi.Controls.SkiaGif.html", "topicHref": "DrawnUi.Controls.SkiaGif.html", "topicUid": "DrawnUi.Controls.SkiaGif", "type": "Class"}, {"name": "SkiaLottie", "href": "DrawnUi.Controls.SkiaLottie.html", "topicHref": "DrawnUi.Controls.SkiaLottie.html", "topicUid": "DrawnUi.Controls.SkiaLottie", "type": "Class"}, {"name": "SkiaLottie.ColorEqualityComparer", "href": "DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.html", "topicHref": "DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.html", "topicUid": "DrawnUi.Controls.SkiaLottie.ColorEqualityComparer", "type": "Class"}, {"name": "SkiaMauiEditor", "href": "DrawnUi.Controls.SkiaMauiEditor.html", "topicHref": "DrawnUi.Controls.SkiaMauiEditor.html", "topicUid": "DrawnUi.Controls.SkiaMauiEditor", "type": "Class"}, {"name": "SkiaMauiEntry", "href": "DrawnUi.Controls.SkiaMauiEntry.html", "topicHref": "DrawnUi.Controls.SkiaMauiEntry.html", "topicUid": "DrawnUi.Controls.SkiaMauiEntry", "type": "Class"}, {"name": "SkiaMediaImage", "href": "DrawnUi.Controls.SkiaMediaImage.html", "topicHref": "DrawnUi.Controls.SkiaMediaImage.html", "topicUid": "DrawnUi.Controls.SkiaMediaImage", "type": "Class"}, {"name": "SkiaRadioButton", "href": "DrawnUi.Controls.SkiaRadioButton.html", "topicHref": "DrawnUi.Controls.SkiaRadioButton.html", "topicUid": "DrawnUi.Controls.SkiaRadioButton", "type": "Class"}, {"name": "SkiaShell", "href": "DrawnUi.Controls.SkiaShell.html", "topicHref": "DrawnUi.Controls.SkiaShell.html", "topicUid": "DrawnUi.Controls.SkiaShell", "type": "Class"}, {"name": "SkiaShell.IHandleGoBack", "href": "DrawnUi.Controls.SkiaShell.IHandleGoBack.html", "topicHref": "DrawnUi.Controls.SkiaShell.IHandleGoBack.html", "topicUid": "DrawnUi.Controls.SkiaShell.IHandleGoBack", "type": "Interface"}, {"name": "SkiaShell.ModalWrapper", "href": "DrawnUi.Controls.SkiaShell.ModalWrapper.html", "topicHref": "DrawnUi.Controls.SkiaShell.ModalWrapper.html", "topicUid": "DrawnUi.Controls.SkiaShell.ModalWrapper", "type": "Class"}, {"name": "SkiaShell.NavigationLayer<T>", "href": "DrawnUi.Controls.SkiaShell.NavigationLayer-1.html", "topicHref": "DrawnUi.Controls.SkiaShell.NavigationLayer-1.html", "topicUid": "DrawnUi.Controls.SkiaShell.NavigationLayer`1", "type": "Class"}, {"name": "SkiaShell.PageInStack", "href": "DrawnUi.Controls.SkiaShell.PageInStack.html", "topicHref": "DrawnUi.Controls.SkiaShell.PageInStack.html", "topicUid": "DrawnUi.Controls.SkiaShell.PageInStack", "type": "Class"}, {"name": "SkiaShell.ParsedRoute", "href": "DrawnUi.Controls.SkiaShell.ParsedRoute.html", "topicHref": "DrawnUi.Controls.SkiaShell.ParsedRoute.html", "topicUid": "DrawnUi.Controls.SkiaShell.ParsedRoute", "type": "Class"}, {"name": "SkiaShell.PopupWrapper", "href": "DrawnUi.Controls.SkiaShell.PopupWrapper.html", "topicHref": "DrawnUi.Controls.SkiaShell.PopupWrapper.html", "topicUid": "DrawnUi.Controls.SkiaShell.PopupWrapper", "type": "Class"}, {"name": "SkiaShell.ShellCurrentRoute", "href": "DrawnUi.Controls.SkiaShell.ShellCurrentRoute.html", "topicHref": "DrawnUi.Controls.SkiaShell.ShellCurrentRoute.html", "topicUid": "DrawnUi.Controls.SkiaShell.ShellCurrentRoute", "type": "Class"}, {"name": "SkiaShell.ShellStackChild", "href": "DrawnUi.Controls.SkiaShell.ShellStackChild.html", "topicHref": "DrawnUi.Controls.SkiaShell.ShellStackChild.html", "topicUid": "DrawnUi.Controls.SkiaShell.ShellStackChild", "type": "Class"}, {"name": "SkiaShell.TypeRouteFactory", "href": "DrawnUi.Controls.SkiaShell.TypeRouteFactory.html", "topicHref": "DrawnUi.Controls.SkiaShell.TypeRouteFactory.html", "topicUid": "DrawnUi.Controls.SkiaShell.TypeRouteFactory", "type": "Class"}, {"name": "SkiaShellNavigatedArgs", "href": "DrawnUi.Controls.SkiaShellNavigatedArgs.html", "topicHref": "DrawnUi.Controls.SkiaShellNavigatedArgs.html", "topicUid": "DrawnUi.Controls.SkiaShellNavigatedArgs", "type": "Class"}, {"name": "SkiaShellNavigatingArgs", "href": "DrawnUi.Controls.SkiaShellNavigatingArgs.html", "topicHref": "DrawnUi.Controls.SkiaShellNavigatingArgs.html", "topicUid": "DrawnUi.Controls.SkiaShellNavigatingArgs", "type": "Class"}, {"name": "SkiaSpinner", "href": "DrawnUi.Controls.SkiaSpinner.html", "topicHref": "DrawnUi.Controls.SkiaSpinner.html", "topicUid": "DrawnUi.Controls.SkiaSpinner", "type": "Class"}, {"name": "SkiaSprite", "href": "DrawnUi.Controls.SkiaSprite.html", "topicHref": "DrawnUi.Controls.SkiaSprite.html", "topicUid": "DrawnUi.Controls.SkiaSprite", "type": "Class"}, {"name": "SkiaTabsSelector", "href": "DrawnUi.Controls.SkiaTabsSelector.html", "topicHref": "DrawnUi.Controls.SkiaTabsSelector.html", "topicUid": "DrawnUi.Controls.SkiaTabsSelector", "type": "Class"}, {"name": "SkiaTabsSelector.TabEntry", "href": "DrawnUi.Controls.SkiaTabsSelector.TabEntry.html", "topicHref": "DrawnUi.Controls.SkiaTabsSelector.TabEntry.html", "topicUid": "DrawnUi.Controls.SkiaTabsSelector.TabEntry", "type": "Class"}, {"name": "SkiaViewSwitcher", "href": "DrawnUi.Controls.SkiaViewSwitcher.html", "topicHref": "DrawnUi.Controls.SkiaViewSwitcher.html", "topicUid": "DrawnUi.Controls.SkiaViewSwitcher", "type": "Class"}, {"name": "SkiaViewSwitcher.NavigationStackEntry", "href": "DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.html", "topicHref": "DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry.html", "topicUid": "DrawnUi.Controls.SkiaViewSwitcher.NavigationStackEntry", "type": "Class"}, {"name": "SkiaWheelPicker", "href": "DrawnUi.Controls.SkiaWheelPicker.html", "topicHref": "DrawnUi.Controls.SkiaWheelPicker.html", "topicUid": "DrawnUi.Controls.SkiaWheelPicker", "type": "Class"}, {"name": "SkiaWheelPickerCell", "href": "DrawnUi.Controls.SkiaWheelPickerCell.html", "topicHref": "DrawnUi.Controls.SkiaWheelPickerCell.html", "topicUid": "DrawnUi.Controls.SkiaWheelPickerCell", "type": "Class"}, {"name": "SkiaWheelScroll", "href": "DrawnUi.Controls.SkiaWheelScroll.html", "topicHref": "DrawnUi.Controls.SkiaWheelScroll.html", "topicUid": "DrawnUi.Controls.SkiaWheelScroll", "type": "Class"}, {"name": "SkiaWheelShape", "href": "DrawnUi.Controls.SkiaWheelShape.html", "topicHref": "DrawnUi.Controls.SkiaWheelShape.html", "topicUid": "DrawnUi.Controls.SkiaWheelShape", "type": "Class"}, {"name": "SkiaWheelStack", "href": "DrawnUi.Controls.SkiaWheelStack.html", "topicHref": "DrawnUi.Controls.SkiaWheelStack.html", "topicUid": "DrawnUi.Controls.SkiaWheelStack", "type": "Class"}, {"name": "WheelCellInfo", "href": "DrawnUi.Controls.WheelCellInfo.html", "topicHref": "DrawnUi.Controls.WheelCellInfo.html", "topicUid": "DrawnUi.Controls.WheelCellInfo", "type": "Class"}]}, {"name": "DrawnUi.Draw", "href": "DrawnUi.Draw.html", "topicHref": "DrawnUi.Draw.html", "topicUid": "DrawnUi.Draw", "type": "Namespace", "items": [{"name": "ActionOnTickAnimator", "href": "DrawnUi.Draw.ActionOnTickAnimator.html", "topicHref": "DrawnUi.Draw.ActionOnTickAnimator.html", "topicUid": "DrawnUi.Draw.ActionOnTickAnimator", "type": "Class"}, {"name": "AddGestures", "href": "DrawnUi.Draw.AddGestures.html", "topicHref": "DrawnUi.Draw.AddGestures.html", "topicUid": "DrawnUi.Draw.AddGestures", "type": "Class"}, {"name": "AddGestures.GestureListener", "href": "DrawnUi.Draw.AddGestures.GestureListener.html", "topicHref": "DrawnUi.Draw.AddGestures.GestureListener.html", "topicUid": "DrawnUi.Draw.AddGestures.GestureListener", "type": "Class"}, {"name": "AdjustBrightnessEffect", "href": "DrawnUi.Draw.AdjustBrightnessEffect.html", "topicHref": "DrawnUi.Draw.AdjustBrightnessEffect.html", "topicUid": "DrawnUi.Draw.AdjustBrightnessEffect", "type": "Class"}, {"name": "AdjustRGBEffect", "href": "DrawnUi.Draw.AdjustRGBEffect.html", "topicHref": "DrawnUi.Draw.AdjustRGBEffect.html", "topicUid": "DrawnUi.Draw.AdjustRGBEffect", "type": "Class"}, {"name": "AnimateExtensions", "href": "DrawnUi.Draw.AnimateExtensions.html", "topicHref": "DrawnUi.Draw.AnimateExtensions.html", "topicUid": "DrawnUi.Draw.AnimateExtensions", "type": "Class"}, {"name": "AnimatorBase", "href": "DrawnUi.Draw.AnimatorBase.html", "topicHref": "DrawnUi.Draw.AnimatorBase.html", "topicUid": "DrawnUi.Draw.AnimatorBase", "type": "Class"}, {"name": "ApplySpan", "href": "DrawnUi.Draw.ApplySpan.html", "topicHref": "DrawnUi.Draw.ApplySpan.html", "topicUid": "DrawnUi.Draw.ApplySpan", "type": "Struct"}, {"name": "AutoSizeType", "href": "DrawnUi.Draw.AutoSizeType.html", "topicHref": "DrawnUi.Draw.AutoSizeType.html", "topicUid": "DrawnUi.Draw.AutoSizeType", "type": "Enum"}, {"name": "BaseChainedEffect", "href": "DrawnUi.Draw.BaseChainedEffect.html", "topicHref": "DrawnUi.Draw.BaseChainedEffect.html", "topicUid": "DrawnUi.Draw.BaseChainedEffect", "type": "Class"}, {"name": "BaseColorFilterEffect", "href": "DrawnUi.Draw.BaseColorFilterEffect.html", "topicHref": "DrawnUi.Draw.BaseColorFilterEffect.html", "topicUid": "DrawnUi.Draw.BaseColorFilterEffect", "type": "Class"}, {"name": "BaseImageFilterEffect", "href": "DrawnUi.Draw.BaseImageFilterEffect.html", "topicHref": "DrawnUi.Draw.BaseImageFilterEffect.html", "topicUid": "DrawnUi.Draw.BaseImageFilterEffect", "type": "Class"}, {"name": "BevelType", "href": "DrawnUi.Draw.BevelType.html", "topicHref": "DrawnUi.Draw.BevelType.html", "topicUid": "DrawnUi.Draw.BevelType", "type": "Enum"}, {"name": "BindToParentContextExtension", "href": "DrawnUi.Draw.BindToParentContextExtension.html", "topicHref": "DrawnUi.Draw.BindToParentContextExtension.html", "topicUid": "DrawnUi.Draw.BindToParentContextExtension", "type": "Class"}, {"name": "BindablePropertyExtension", "href": "DrawnUi.Draw.BindablePropertyExtension.html", "topicHref": "DrawnUi.Draw.BindablePropertyExtension.html", "topicUid": "DrawnUi.Draw.BindablePropertyExtension", "type": "Class"}, {"name": "BlinkAnimator", "href": "DrawnUi.Draw.BlinkAnimator.html", "topicHref": "DrawnUi.Draw.BlinkAnimator.html", "topicUid": "DrawnUi.Draw.BlinkAnimator", "type": "Class"}, {"name": "BlurEffect", "href": "DrawnUi.Draw.BlurEffect.html", "topicHref": "DrawnUi.Draw.BlurEffect.html", "topicUid": "DrawnUi.Draw.BlurEffect", "type": "Class"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "href": "DrawnUi.Draw.CachedGradient.html", "topicHref": "DrawnUi.Draw.CachedGradient.html", "topicUid": "DrawnUi.Draw.CachedGradient", "type": "Class"}, {"name": "CachedObject", "href": "DrawnUi.Draw.CachedObject.html", "topicHref": "DrawnUi.Draw.CachedObject.html", "topicUid": "DrawnUi.Draw.CachedObject", "type": "Class"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "href": "DrawnUi.Draw.CachedShader.html", "topicHref": "DrawnUi.Draw.CachedShader.html", "topicUid": "DrawnUi.<PERSON>.<PERSON><PERSON>", "type": "Class"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "href": "DrawnUi.Draw.CachedShadow.html", "topicHref": "DrawnUi.Draw.CachedShadow.html", "topicUid": "DrawnUi.Draw.Cached<PERSON>hadow", "type": "Class"}, {"name": "CellWIthHeight", "href": "DrawnUi.Draw.CellWIthHeight.html", "topicHref": "DrawnUi.Draw.CellWIthHeight.html", "topicUid": "DrawnUi.Draw.CellWIthHeight", "type": "Class"}, {"name": "ChainAdjustBrightnessEffect", "href": "DrawnUi.Draw.ChainAdjustBrightnessEffect.html", "topicHref": "DrawnUi.Draw.ChainAdjustBrightnessEffect.html", "topicUid": "DrawnUi.Draw.ChainAdjustBrightnessEffect", "type": "Class"}, {"name": "ChainAdjustContrastEffect", "href": "DrawnUi.Draw.ChainAdjustContrastEffect.html", "topicHref": "DrawnUi.Draw.ChainAdjustContrastEffect.html", "topicUid": "DrawnUi.Draw.ChainAdjustContrastEffect", "type": "Class"}, {"name": "ChainAdjustLightnessEffect", "href": "DrawnUi.Draw.ChainAdjustLightnessEffect.html", "topicHref": "DrawnUi.Draw.ChainAdjustLightnessEffect.html", "topicUid": "DrawnUi.Draw.ChainAdjustLightnessEffect", "type": "Class"}, {"name": "ChainAdjustRGBEffect", "href": "DrawnUi.Draw.ChainAdjustRGBEffect.html", "topicHref": "DrawnUi.Draw.ChainAdjustRGBEffect.html", "topicUid": "DrawnUi.Draw.ChainAdjustRGBEffect", "type": "Class"}, {"name": "ChainColorPresetEffect", "href": "DrawnUi.Draw.ChainColorPresetEffect.html", "topicHref": "DrawnUi.Draw.ChainColorPresetEffect.html", "topicUid": "DrawnUi.Draw.ChainColorPresetEffect", "type": "Class"}, {"name": "ChainDropShadowsEffect", "href": "DrawnUi.Draw.ChainDropShadowsEffect.html", "topicHref": "DrawnUi.Draw.ChainDropShadowsEffect.html", "topicUid": "DrawnUi.Draw.ChainDropShadowsEffect", "type": "Class"}, {"name": "ChainEffectResult", "href": "DrawnUi.Draw.ChainEffectResult.html", "topicHref": "DrawnUi.Draw.ChainEffectResult.html", "topicUid": "DrawnUi.Draw.ChainEffectResult", "type": "Struct"}, {"name": "ChainSaturationEffect", "href": "DrawnUi.Draw.ChainSaturationEffect.html", "topicHref": "DrawnUi.Draw.ChainSaturationEffect.html", "topicUid": "DrawnUi.Draw.ChainSaturationEffect", "type": "Class"}, {"name": "ChainTintWithAlphaEffect", "href": "DrawnUi.Draw.ChainTintWithAlphaEffect.html", "topicHref": "DrawnUi.Draw.ChainTintWithAlphaEffect.html", "topicUid": "DrawnUi.Draw.ChainTintWithAlphaEffect", "type": "Class"}, {"name": "ColorBlendAnimator", "href": "DrawnUi.Draw.ColorBlendAnimator.html", "topicHref": "DrawnUi.Draw.ColorBlendAnimator.html", "topicUid": "DrawnUi.Draw.ColorBlendAnimator", "type": "Class"}, {"name": "ColorExtensions", "href": "DrawnUi.Draw.ColorExtensions.html", "topicHref": "DrawnUi.Draw.ColorExtensions.html", "topicUid": "DrawnUi.Draw.ColorExtensions", "type": "Class"}, {"name": "ColorPresetEffect", "href": "DrawnUi.Draw.ColorPresetEffect.html", "topicHref": "DrawnUi.Draw.ColorPresetEffect.html", "topicUid": "DrawnUi.Draw.ColorPresetEffect", "type": "Class"}, {"name": "ContainsPointResult", "href": "DrawnUi.Draw.ContainsPointResult.html", "topicHref": "DrawnUi.Draw.ContainsPointResult.html", "topicUid": "DrawnUi.Draw.ContainsPointResult", "type": "Class"}, {"name": "ContentLayout", "href": "DrawnUi.Draw.ContentLayout.html", "topicHref": "DrawnUi.Draw.ContentLayout.html", "topicUid": "DrawnUi.Draw.ContentLayout", "type": "Class"}, {"name": "ContextArguments", "href": "DrawnUi.Draw.ContextArguments.html", "topicHref": "DrawnUi.Draw.ContextArguments.html", "topicUid": "DrawnUi.Draw.ContextArguments", "type": "Enum"}, {"name": "ContrastEffect", "href": "DrawnUi.Draw.ContrastEffect.html", "topicHref": "DrawnUi.Draw.ContrastEffect.html", "topicUid": "DrawnUi.Draw.ContrastEffect", "type": "Class"}, {"name": "ControlInStack", "href": "DrawnUi.Draw.ControlInStack.html", "topicHref": "DrawnUi.Draw.ControlInStack.html", "topicUid": "DrawnUi.Draw.ControlInStack", "type": "Class"}, {"name": "ControlsTracker", "href": "DrawnUi.Draw.ControlsTracker.html", "topicHref": "DrawnUi.Draw.ControlsTracker.html", "topicUid": "DrawnUi.Draw.ControlsTracker", "type": "Class"}, {"name": "CriticallyDampedSpringTimingParameters", "href": "DrawnUi.Draw.CriticallyDampedSpringTimingParameters.html", "topicHref": "DrawnUi.Draw.CriticallyDampedSpringTimingParameters.html", "topicUid": "DrawnUi.Draw.CriticallyDampedSpringTimingParameters", "type": "Class"}, {"name": "CriticallyDampedSpringTimingVectorParameters", "href": "DrawnUi.Draw.CriticallyDampedSpringTimingVectorParameters.html", "topicHref": "DrawnUi.Draw.CriticallyDampedSpringTimingVectorParameters.html", "topicUid": "DrawnUi.Draw.CriticallyDampedSpringTimingVectorParameters", "type": "Struct"}, {"name": "DataContextIterator", "href": "DrawnUi.Draw.DataContextIterator.html", "topicHref": "DrawnUi.Draw.DataContextIterator.html", "topicUid": "DrawnUi.Draw.DataContextIterator", "type": "Class"}, {"name": "DebugImage", "href": "DrawnUi.Draw.DebugImage.html", "topicHref": "DrawnUi.Draw.DebugImage.html", "topicUid": "DrawnUi.Draw.DebugImage", "type": "Class"}, {"name": "DecelerationTimingParameters", "href": "DrawnUi.Draw.DecelerationTimingParameters.html", "topicHref": "DrawnUi.Draw.DecelerationTimingParameters.html", "topicUid": "DrawnUi.Draw.DecelerationTimingParameters", "type": "Class"}, {"name": "DecelerationTimingVectorParameters", "href": "DrawnUi.Draw.DecelerationTimingVectorParameters.html", "topicHref": "DrawnUi.Draw.DecelerationTimingVectorParameters.html", "topicUid": "DrawnUi.Draw.DecelerationTimingVectorParameters", "type": "Class"}, {"name": "DescendingZIndexGestureListenerComparer", "href": "DrawnUi.Draw.DescendingZIndexGestureListenerComparer.html", "topicHref": "DrawnUi.Draw.DescendingZIndexGestureListenerComparer.html", "topicUid": "DrawnUi.Draw.DescendingZIndexGestureListenerComparer", "type": "Class"}, {"name": "DirectionType", "href": "DrawnUi.Draw.DirectionType.html", "topicHref": "DrawnUi.Draw.DirectionType.html", "topicUid": "DrawnUi.Draw.DirectionType", "type": "Enum"}, {"name": "DrawImageAlignment", "href": "DrawnUi.Draw.DrawImageAlignment.html", "topicHref": "DrawnUi.Draw.DrawImageAlignment.html", "topicUid": "DrawnUi.Draw.DrawImageAlignment", "type": "Enum"}, {"name": "DrawTextAlignment", "href": "DrawnUi.Draw.DrawTextAlignment.html", "topicHref": "DrawnUi.Draw.DrawTextAlignment.html", "topicUid": "DrawnUi.Draw.DrawTextAlignment", "type": "Enum"}, {"name": "DrawingContext", "href": "DrawnUi.Draw.DrawingContext.html", "topicHref": "DrawnUi.Draw.DrawingContext.html", "topicUid": "DrawnUi.Draw.DrawingContext", "type": "Struct"}, {"name": "DrawingRect", "href": "DrawnUi.Draw.DrawingRect.html", "topicHref": "DrawnUi.Draw.DrawingRect.html", "topicUid": "DrawnUi.Draw.DrawingRect", "type": "Class"}, {"name": "DrawnExtensions", "href": "DrawnUi.Draw.DrawnExtensions.html", "topicHref": "DrawnUi.Draw.DrawnExtensions.html", "topicUid": "DrawnUi.Draw.DrawnExtensions", "type": "Class"}, {"name": "DrawnFontAttributesConverter", "href": "DrawnUi.Draw.DrawnFontAttributesConverter.html", "topicHref": "DrawnUi.Draw.DrawnFontAttributesConverter.html", "topicUid": "DrawnUi.Draw.DrawnFontAttributesConverter", "type": "Class"}, {"name": "DrawnUiStartupSettings", "href": "DrawnUi.Draw.DrawnUiStartupSettings.html", "topicHref": "DrawnUi.Draw.DrawnUiStartupSettings.html", "topicUid": "DrawnUi.Draw.DrawnUiStartupSettings", "type": "Class"}, {"name": "DropShadowEffect", "href": "DrawnUi.Draw.DropShadowEffect.html", "topicHref": "DrawnUi.Draw.DropShadowEffect.html", "topicUid": "DrawnUi.Draw.DropShadowEffect", "type": "Class"}, {"name": "DynamicGrid<T>", "href": "DrawnUi.Draw.DynamicGrid-1.html", "topicHref": "DrawnUi.Draw.DynamicGrid-1.html", "topicUid": "DrawnUi.Draw.DynamicGrid`1", "type": "Class"}, {"name": "EdgeGlowAnimator", "href": "DrawnUi.Draw.EdgeGlowAnimator.html", "topicHref": "DrawnUi.Draw.EdgeGlowAnimator.html", "topicUid": "DrawnUi.Draw.EdgeGlowAnimator", "type": "Class"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "href": "DrawnUi.Draw.ElementRenderer.html", "topicHref": "DrawnUi.Draw.ElementRenderer.html", "topicUid": "DrawnUi.Draw.Element<PERSON>er", "type": "Class"}, {"name": "FindTagExtension", "href": "DrawnUi.Draw.FindTagExtension.html", "topicHref": "DrawnUi.Draw.FindTagExtension.html", "topicUid": "DrawnUi.Draw.FindTagExtension", "type": "Class"}, {"name": "FluentExtensions", "href": "DrawnUi.Draw.FluentExtensions.html", "topicHref": "DrawnUi.Draw.FluentExtensions.html", "topicUid": "DrawnUi.Draw.FluentExtensions", "type": "Class"}, {"name": "FontWeight", "href": "DrawnUi.Draw.FontWeight.html", "topicHref": "DrawnUi.Draw.FontWeight.html", "topicUid": "DrawnUi.Draw.FontWeight", "type": "Enum"}, {"name": "FrameTimeInterpolator", "href": "DrawnUi.Draw.FrameTimeInterpolator.html", "topicHref": "DrawnUi.Draw.FrameTimeInterpolator.html", "topicUid": "DrawnUi.Draw.FrameTimeInterpolator", "type": "Class"}, {"name": "GestureEventProcessingInfo", "href": "DrawnUi.Draw.GestureEventProcessingInfo.html", "topicHref": "DrawnUi.Draw.GestureEventProcessingInfo.html", "topicUid": "DrawnUi.Draw.GestureEventProcessingInfo", "type": "Struct"}, {"name": "GesturesMode", "href": "DrawnUi.Draw.GesturesMode.html", "topicHref": "DrawnUi.Draw.GesturesMode.html", "topicUid": "DrawnUi.Draw.GesturesMode", "type": "Enum"}, {"name": "GlowPosition", "href": "DrawnUi.Draw.GlowPosition.html", "topicHref": "DrawnUi.Draw.GlowPosition.html", "topicUid": "DrawnUi.Draw.GlowPosition", "type": "Enum"}, {"name": "GradientType", "href": "DrawnUi.Draw.GradientType.html", "topicHref": "DrawnUi.Draw.GradientType.html", "topicUid": "DrawnUi.Draw.GradientType", "type": "Enum"}, {"name": "IAfterEffectDelete", "href": "DrawnUi.Draw.IAfterEffectDelete.html", "topicHref": "DrawnUi.Draw.IAfterEffectDelete.html", "topicUid": "DrawnUi.Draw.IAfterEffectDelete", "type": "Interface"}, {"name": "IAnimatorsManager", "href": "DrawnUi.Draw.IAnimatorsManager.html", "topicHref": "DrawnUi.Draw.IAnimatorsManager.html", "topicUid": "DrawnUi.Draw.IAnimatorsManager", "type": "Interface"}, {"name": "IBindingContextDebuggable", "href": "DrawnUi.Draw.IBindingContextDebuggable.html", "topicHref": "DrawnUi.Draw.IBindingContextDebuggable.html", "topicUid": "DrawnUi.Draw.IBindingContextDebuggable", "type": "Interface"}, {"name": "ICanBeUpdated", "href": "DrawnUi.Draw.ICanBeUpdated.html", "topicHref": "DrawnUi.Draw.ICanBeUpdated.html", "topicUid": "DrawnUi.Draw.ICanBeUpdated", "type": "Interface"}, {"name": "ICanBeUpdatedWithContext", "href": "DrawnUi.Draw.ICanBeUpdatedWithContext.html", "topicHref": "DrawnUi.Draw.ICanBeUpdatedWithContext.html", "topicUid": "DrawnUi.Draw.ICanBeUpdatedWithContext", "type": "Interface"}, {"name": "ICanRenderOnCanvas", "href": "DrawnUi.Draw.ICanRenderOnCanvas.html", "topicHref": "DrawnUi.Draw.ICanRenderOnCanvas.html", "topicUid": "DrawnUi.Draw.ICanRenderOnCanvas", "type": "Interface"}, {"name": "IColorEffect", "href": "DrawnUi.Draw.IColorEffect.html", "topicHref": "DrawnUi.Draw.IColorEffect.html", "topicUid": "DrawnUi.Draw.IColorEffect", "type": "Interface"}, {"name": "IDampingTimingParameters", "href": "DrawnUi.Draw.IDampingTimingParameters.html", "topicHref": "DrawnUi.Draw.IDampingTimingParameters.html", "topicUid": "DrawnUi.Draw.IDampingTimingParameters", "type": "Interface"}, {"name": "IDampingTimingVectorParameters", "href": "DrawnUi.Draw.IDampingTimingVectorParameters.html", "topicHref": "DrawnUi.Draw.IDampingTimingVectorParameters.html", "topicUid": "DrawnUi.Draw.IDampingTimingVectorParameters", "type": "Interface"}, {"name": "IDefinesViewport", "href": "DrawnUi.Draw.IDefinesViewport.html", "topicHref": "DrawnUi.Draw.IDefinesViewport.html", "topicUid": "DrawnUi.Draw.IDefinesViewport", "type": "Interface"}, {"name": "IDrawnBase", "href": "DrawnUi.Draw.IDrawnBase.html", "topicHref": "DrawnUi.Draw.IDrawnBase.html", "topicUid": "DrawnUi.Draw.IDrawnBase", "type": "Interface"}, {"name": "IDrawnTextSpan", "href": "DrawnUi.Draw.IDrawnTextSpan.html", "topicHref": "DrawnUi.Draw.IDrawnTextSpan.html", "topicUid": "DrawnUi.Draw.IDrawnTextSpan", "type": "Interface"}, {"name": "IHasAfterEffects", "href": "DrawnUi.Draw.IHasAfterEffects.html", "topicHref": "DrawnUi.Draw.IHasAfterEffects.html", "topicUid": "DrawnUi.Draw.IHasAfterEffects", "type": "Interface"}, {"name": "IHasBanner", "href": "DrawnUi.Draw.IHasBanner.html", "topicHref": "DrawnUi.Draw.IHasBanner.html", "topicUid": "DrawnUi.Draw.IHasBanner", "type": "Interface"}, {"name": "IImageEffect", "href": "DrawnUi.Draw.IImageEffect.html", "topicHref": "DrawnUi.Draw.IImageEffect.html", "topicUid": "DrawnUi.Draw.IImageEffect", "type": "Interface"}, {"name": "IInsideViewport", "href": "DrawnUi.Draw.IInsideViewport.html", "topicHref": "DrawnUi.Draw.IInsideViewport.html", "topicUid": "DrawnUi.Draw.IInsideViewport", "type": "Interface"}, {"name": "IInsideWheelStack", "href": "DrawnUi.Draw.IInsideWheelStack.html", "topicHref": "DrawnUi.Draw.IInsideWheelStack.html", "topicUid": "DrawnUi.Draw.IInsideWheelStack", "type": "Interface"}, {"name": "IInterpolator", "href": "DrawnUi.Draw.IInterpolator.html", "topicHref": "DrawnUi.Draw.IInterpolator.html", "topicUid": "DrawnUi.Draw.IInterpolator", "type": "Interface"}, {"name": "ILayoutInsideViewport", "href": "DrawnUi.Draw.ILayoutInsideViewport.html", "topicHref": "DrawnUi.Draw.ILayoutInsideViewport.html", "topicUid": "DrawnUi.Draw.ILayoutInsideViewport", "type": "Interface"}, {"name": "IOverlayEffect", "href": "DrawnUi.Draw.IOverlayEffect.html", "topicHref": "DrawnUi.Draw.IOverlayEffect.html", "topicUid": "DrawnUi.Draw.IOverlayEffect", "type": "Interface"}, {"name": "IPostRendererEffect", "href": "DrawnUi.Draw.IPostRendererEffect.html", "topicHref": "DrawnUi.Draw.IPostRendererEffect.html", "topicUid": "DrawnUi.Draw.IPostRendererEffect", "type": "Interface"}, {"name": "IRefreshIndicator", "href": "DrawnUi.Draw.IRefreshIndicator.html", "topicHref": "DrawnUi.Draw.IRefreshIndicator.html", "topicUid": "DrawnUi.Draw.IRefreshIndicator", "type": "Interface"}, {"name": "IRenderEffect", "href": "DrawnUi.Draw.IRenderEffect.html", "topicHref": "DrawnUi.Draw.IRenderEffect.html", "topicUid": "DrawnUi.Draw.IRenderEffect", "type": "Interface"}, {"name": "IRenderObject", "href": "DrawnUi.Draw.IRenderObject.html", "topicHref": "DrawnUi.Draw.IRenderObject.html", "topicUid": "DrawnUi.Draw.IRenderObject", "type": "Interface"}, {"name": "ISkiaAnimator", "href": "DrawnUi.Draw.ISkiaAnimator.html", "topicHref": "DrawnUi.Draw.ISkiaAnimator.html", "topicUid": "DrawnUi.Draw.ISkiaAnimator", "type": "Interface"}, {"name": "ISkiaCell", "href": "DrawnUi.Draw.ISkiaCell.html", "topicHref": "DrawnUi.Draw.ISkiaCell.html", "topicUid": "DrawnUi.Draw.ISkiaCell", "type": "Interface"}, {"name": "ISkiaControl", "href": "DrawnUi.Draw.ISkiaControl.html", "topicHref": "DrawnUi.Draw.ISkiaControl.html", "topicUid": "DrawnUi.Draw.ISkiaControl", "type": "Interface"}, {"name": "ISkiaDrawable", "href": "DrawnUi.Draw.ISkiaDrawable.html", "topicHref": "DrawnUi.Draw.ISkiaDrawable.html", "topicUid": "DrawnUi.Draw.ISkiaDrawable", "type": "Interface"}, {"name": "ISkiaEffect", "href": "DrawnUi.Draw.ISkiaEffect.html", "topicHref": "DrawnUi.Draw.ISkiaEffect.html", "topicUid": "DrawnUi.Draw.ISkiaEffect", "type": "Interface"}, {"name": "ISkiaGestureListener", "href": "DrawnUi.Draw.ISkiaGestureListener.html", "topicHref": "DrawnUi.Draw.ISkiaGestureListener.html", "topicUid": "DrawnUi.Draw.ISkiaGestureListener", "type": "Interface"}, {"name": "ISkiaGestureProcessor", "href": "DrawnUi.Draw.ISkiaGestureProcessor.html", "topicHref": "DrawnUi.Draw.ISkiaGestureProcessor.html", "topicUid": "DrawnUi.Draw.ISkiaGestureProcessor", "type": "Interface"}, {"name": "ISkiaGridLayout", "href": "DrawnUi.Draw.ISkiaGridLayout.html", "topicHref": "DrawnUi.Draw.ISkiaGridLayout.html", "topicUid": "DrawnUi.Draw.ISkiaGridLayout", "type": "Interface"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "href": "DrawnUi.Draw.ISkiaLayer.html", "topicHref": "DrawnUi.Draw.ISkiaLayer.html", "topicUid": "DrawnUi.Draw.ISkiaLayer", "type": "Interface"}, {"name": "ISkiaLayout", "href": "DrawnUi.Draw.ISkiaLayout.html", "topicHref": "DrawnUi.Draw.ISkiaLayout.html", "topicUid": "DrawnUi.Draw.ISkiaLayout", "type": "Interface"}, {"name": "ISkiaSharpView", "href": "DrawnUi.Draw.ISkiaSharpView.html", "topicHref": "DrawnUi.Draw.ISkiaSharpView.html", "topicUid": "DrawnUi.Draw.ISkiaSharpView", "type": "Interface"}, {"name": "IStateEffect", "href": "DrawnUi.Draw.IStateEffect.html", "topicHref": "DrawnUi.Draw.IStateEffect.html", "topicUid": "DrawnUi.Draw.IStateEffect", "type": "Interface"}, {"name": "ITimingParameters", "href": "DrawnUi.Draw.ITimingParameters.html", "topicHref": "DrawnUi.Draw.ITimingParameters.html", "topicUid": "DrawnUi.Draw.ITimingParameters", "type": "Interface"}, {"name": "ITimingVectorParameters", "href": "DrawnUi.Draw.ITimingVectorParameters.html", "topicHref": "DrawnUi.Draw.ITimingVectorParameters.html", "topicUid": "DrawnUi.Draw.ITimingVectorParameters", "type": "Interface"}, {"name": "IVisibilityAware", "href": "DrawnUi.Draw.IVisibilityAware.html", "topicHref": "DrawnUi.Draw.IVisibilityAware.html", "topicUid": "DrawnUi.Draw.IVisibilityAware", "type": "Interface"}, {"name": "IWithContent", "href": "DrawnUi.Draw.IWithContent.html", "topicHref": "DrawnUi.Draw.IWithContent.html", "topicUid": "DrawnUi.Draw.IWithContent", "type": "Interface"}, {"name": "InfiniteLayout", "href": "DrawnUi.Draw.InfiniteLayout.html", "topicHref": "DrawnUi.Draw.InfiniteLayout.html", "topicUid": "DrawnUi.Draw.InfiniteLayout", "type": "Class"}, {"name": "KeyboardManager", "href": "DrawnUi.Draw.KeyboardManager.html", "topicHref": "DrawnUi.Draw.KeyboardManager.html", "topicUid": "DrawnUi.Draw.KeyboardManager", "type": "Class"}, {"name": "LayoutStructure", "href": "DrawnUi.Draw.LayoutStructure.html", "topicHref": "DrawnUi.Draw.LayoutStructure.html", "topicUid": "DrawnUi.Draw.LayoutStructure", "type": "Class"}, {"name": "LayoutType", "href": "DrawnUi.Draw.LayoutType.html", "topicHref": "DrawnUi.Draw.LayoutType.html", "topicUid": "DrawnUi.Draw.LayoutType", "type": "Enum"}, {"name": "LineGlyph", "href": "DrawnUi.Draw.LineGlyph.html", "topicHref": "DrawnUi.Draw.LineGlyph.html", "topicUid": "DrawnUi.Draw.LineGlyph", "type": "Struct"}, {"name": "LineSpan", "href": "DrawnUi.Draw.LineSpan.html", "topicHref": "DrawnUi.Draw.LineSpan.html", "topicUid": "DrawnUi.Draw.LineSpan", "type": "Struct"}, {"name": "LinearDirectionType", "href": "DrawnUi.Draw.LinearDirectionType.html", "topicHref": "DrawnUi.Draw.LinearDirectionType.html", "topicUid": "DrawnUi.Draw.LinearDirectionType", "type": "Enum"}, {"name": "LinearInterpolationTimingParameters", "href": "DrawnUi.Draw.LinearInterpolationTimingParameters.html", "topicHref": "DrawnUi.Draw.LinearInterpolationTimingParameters.html", "topicUid": "DrawnUi.Draw.LinearInterpolationTimingParameters", "type": "Struct"}, {"name": "LoadPriority", "href": "DrawnUi.Draw.LoadPriority.html", "topicHref": "DrawnUi.Draw.LoadPriority.html", "topicUid": "DrawnUi.Draw.LoadPriority", "type": "Enum"}, {"name": "LoadedImageSource", "href": "DrawnUi.Draw.LoadedImageSource.html", "topicHref": "DrawnUi.Draw.LoadedImageSource.html", "topicUid": "DrawnUi.Draw.LoadedImageSource", "type": "Class"}, {"name": "LockTouch", "href": "DrawnUi.Draw.LockTouch.html", "topicHref": "DrawnUi.Draw.LockTouch.html", "topicUid": "DrawnUi.Draw.LockTouch", "type": "Enum"}, {"name": "<PERSON><PERSON>", "href": "DrawnUi.Draw.Looper.html", "topicHref": "DrawnUi.Draw.Looper.html", "topicUid": "DrawnUi.Draw.Looper", "type": "Class"}, {"name": "LottieRefreshIndicator", "href": "DrawnUi.Draw.LottieRefreshIndicator.html", "topicHref": "DrawnUi.Draw.LottieRefreshIndicator.html", "topicUid": "DrawnUi.Draw.LottieRefreshIndicator", "type": "Class"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "href": "DrawnUi.Draw.MauiKey.html", "topicHref": "DrawnUi.Draw.MauiKey.html", "topicUid": "DrawnUi.Draw.<PERSON>", "type": "Enum"}, {"name": "MauiKeyMapper", "href": "DrawnUi.Draw.MauiKeyMapper.html", "topicHref": "DrawnUi.Draw.MauiKeyMapper.html", "topicUid": "DrawnUi.Draw.MauiKeyMapper", "type": "Class"}, {"name": "MeasureRequest", "href": "DrawnUi.Draw.MeasureRequest.html", "topicHref": "DrawnUi.Draw.MeasureRequest.html", "topicUid": "DrawnUi.Draw.MeasureRequest", "type": "Struct"}, {"name": "MeasuredListCell", "href": "DrawnUi.Draw.MeasuredListCell.html", "topicHref": "DrawnUi.Draw.MeasuredListCell.html", "topicUid": "DrawnUi.Draw.MeasuredListCell", "type": "Class"}, {"name": "MeasuredList<PERSON>ells", "href": "DrawnUi.Draw.MeasuredListCells.html", "topicHref": "DrawnUi.Draw.MeasuredListCells.html", "topicUid": "DrawnUi.Draw.MeasuredListCells", "type": "Class"}, {"name": "MeasuringStrategy", "href": "DrawnUi.Draw.MeasuringStrategy.html", "topicHref": "DrawnUi.Draw.MeasuringStrategy.html", "topicUid": "DrawnUi.Draw.MeasuringStrategy", "type": "Enum"}, {"name": "ObjectAliveType", "href": "DrawnUi.Draw.ObjectAliveType.html", "topicHref": "DrawnUi.Draw.ObjectAliveType.html", "topicUid": "DrawnUi.Draw.ObjectAliveType", "type": "Enum"}, {"name": "ObservableAttachedItemsCollection<T>", "href": "DrawnUi.Draw.ObservableAttachedItemsCollection-1.html", "topicHref": "DrawnUi.Draw.ObservableAttachedItemsCollection-1.html", "topicUid": "DrawnUi.Draw.ObservableAttachedItemsCollection`1", "type": "Class"}, {"name": "OrientationType", "href": "DrawnUi.Draw.OrientationType.html", "topicHref": "DrawnUi.Draw.OrientationType.html", "topicUid": "DrawnUi.Draw.OrientationType", "type": "Enum"}, {"name": "PanningModeType", "href": "DrawnUi.Draw.PanningModeType.html", "topicHref": "DrawnUi.Draw.PanningModeType.html", "topicUid": "DrawnUi.Draw.PanningModeType", "type": "Enum"}, {"name": "PendulumAnimator", "href": "DrawnUi.Draw.PendulumAnimator.html", "topicHref": "DrawnUi.Draw.PendulumAnimator.html", "topicUid": "DrawnUi.Draw.PendulumAnimator", "type": "Class"}, {"name": "PerpetualP<PERSON>ulumAnimat<PERSON>", "href": "DrawnUi.Draw.PerpetualPendulumAnimator.html", "topicHref": "DrawnUi.Draw.PerpetualPendulumAnimator.html", "topicUid": "DrawnUi.Draw.PerpetualPendulumAnimator", "type": "Class"}, {"name": "Ping<PERSON>ongAnimator", "href": "DrawnUi.Draw.PingPongAnimator.html", "topicHref": "DrawnUi.Draw.PingPongAnimator.html", "topicUid": "DrawnUi.Draw.PingPongAnimator", "type": "Class"}, {"name": "Plane", "href": "DrawnUi.Draw.Plane.html", "topicHref": "DrawnUi.Draw.Plane.html", "topicUid": "DrawnUi.Draw.Plane", "type": "Class"}, {"name": "PlanesScroll", "href": "DrawnUi.Draw.PlanesScroll.html", "topicHref": "DrawnUi.Draw.PlanesScroll.html", "topicUid": "DrawnUi.Draw.PlanesScroll", "type": "Class"}, {"name": "PlanesScroll.ViewLayoutInfo", "href": "DrawnUi.Draw.PlanesScroll.ViewLayoutInfo.html", "topicHref": "DrawnUi.Draw.PlanesScroll.ViewLayoutInfo.html", "topicUid": "DrawnUi.Draw.PlanesScroll.ViewLayoutInfo", "type": "Struct"}, {"name": "PointIsInsideResult", "href": "DrawnUi.Draw.PointIsInsideResult.html", "topicHref": "DrawnUi.Draw.PointIsInsideResult.html", "topicUid": "DrawnUi.Draw.PointIsInsideResult", "type": "Class"}, {"name": "PointedDirectionType", "href": "DrawnUi.Draw.PointedDirectionType.html", "topicHref": "DrawnUi.Draw.PointedDirectionType.html", "topicUid": "DrawnUi.Draw.PointedDirectionType", "type": "Enum"}, {"name": "PrebuiltControlStyle", "href": "DrawnUi.Draw.PrebuiltControlStyle.html", "topicHref": "DrawnUi.Draw.PrebuiltControlStyle.html", "topicUid": "DrawnUi.Draw.PrebuiltControlStyle", "type": "Enum"}, {"name": "ProgressAnimator", "href": "DrawnUi.Draw.ProgressAnimator.html", "topicHref": "DrawnUi.Draw.ProgressAnimator.html", "topicUid": "DrawnUi.Draw.ProgressAnimator", "type": "Class"}, {"name": "ProgressTrail", "href": "DrawnUi.Draw.ProgressTrail.html", "topicHref": "DrawnUi.Draw.ProgressTrail.html", "topicUid": "DrawnUi.Draw.ProgressTrail", "type": "Class"}, {"name": "RangeAnimator", "href": "DrawnUi.Draw.RangeAnimator.html", "topicHref": "DrawnUi.Draw.RangeAnimator.html", "topicUid": "DrawnUi.Draw.RangeAnimator", "type": "Class"}, {"name": "RangeF", "href": "DrawnUi.Draw.RangeF.html", "topicHref": "DrawnUi.Draw.RangeF.html", "topicUid": "DrawnUi.Draw.RangeF", "type": "Struct"}, {"name": "RangeVectorAnimator", "href": "DrawnUi.Draw.RangeVectorAnimator.html", "topicHref": "DrawnUi.Draw.RangeVectorAnimator.html", "topicUid": "DrawnUi.Draw.RangeVectorAnimator", "type": "Class"}, {"name": "RangeZone", "href": "DrawnUi.Draw.RangeZone.html", "topicHref": "DrawnUi.Draw.RangeZone.html", "topicUid": "DrawnUi.Draw.RangeZone", "type": "Enum"}, {"name": "RecycleTemplateType", "href": "DrawnUi.Draw.RecycleTemplateType.html", "topicHref": "DrawnUi.Draw.RecycleTemplateType.html", "topicUid": "DrawnUi.Draw.RecycleTemplateType", "type": "Enum"}, {"name": "RecyclingTemplate", "href": "DrawnUi.Draw.RecyclingTemplate.html", "topicHref": "DrawnUi.Draw.RecyclingTemplate.html", "topicUid": "DrawnUi.Draw.RecyclingTemplate", "type": "Enum"}, {"name": "RefreshIndicator", "href": "DrawnUi.Draw.RefreshIndicator.html", "topicHref": "DrawnUi.Draw.RefreshIndicator.html", "topicUid": "DrawnUi.Draw.RefreshIndicator", "type": "Class"}, {"name": "RelativePositionType", "href": "DrawnUi.Draw.RelativePositionType.html", "topicHref": "DrawnUi.Draw.RelativePositionType.html", "topicUid": "DrawnUi.Draw.RelativePositionType", "type": "Enum"}, {"name": "RenderDrawingContext", "href": "DrawnUi.Draw.RenderDrawingContext.html", "topicHref": "DrawnUi.Draw.RenderDrawingContext.html", "topicUid": "DrawnUi.Draw.RenderDrawingContext", "type": "Class"}, {"name": "RenderLabel", "href": "DrawnUi.Draw.RenderLabel.html", "topicHref": "DrawnUi.Draw.RenderLabel.html", "topicUid": "DrawnUi.Draw.RenderLabel", "type": "Class"}, {"name": "RenderObject", "href": "DrawnUi.Draw.RenderObject.html", "topicHref": "DrawnUi.Draw.RenderObject.html", "topicUid": "DrawnUi.Draw.RenderObject", "type": "Class"}, {"name": "RenderTreeRenderer", "href": "DrawnUi.Draw.RenderTreeRenderer.html", "topicHref": "DrawnUi.Draw.RenderTreeRenderer.html", "topicUid": "DrawnUi.Draw.RenderTreeRenderer", "type": "Class"}, {"name": "RenderingAnimator", "href": "DrawnUi.Draw.RenderingAnimator.html", "topicHref": "DrawnUi.Draw.RenderingAnimator.html", "topicUid": "DrawnUi.Draw.RenderingAnimator", "type": "Class"}, {"name": "RenderingModeType", "href": "DrawnUi.Draw.RenderingModeType.html", "topicHref": "DrawnUi.Draw.RenderingModeType.html", "topicUid": "DrawnUi.Draw.RenderingModeType", "type": "Enum"}, {"name": "RippleAnimator", "href": "DrawnUi.Draw.RippleAnimator.html", "topicHref": "DrawnUi.Draw.RippleAnimator.html", "topicUid": "DrawnUi.Draw.RippleAnimator", "type": "Class"}, {"name": "SaturationEffect", "href": "DrawnUi.Draw.SaturationEffect.html", "topicHref": "DrawnUi.Draw.SaturationEffect.html", "topicUid": "DrawnUi.Draw.SaturationEffect", "type": "Class"}, {"name": "ScaledPoint", "href": "DrawnUi.Draw.ScaledPoint.html", "topicHref": "DrawnUi.Draw.ScaledPoint.html", "topicUid": "DrawnUi.Draw.ScaledPoint", "type": "Struct"}, {"name": "ScaledRect", "href": "DrawnUi.Draw.ScaledRect.html", "topicHref": "DrawnUi.Draw.ScaledRect.html", "topicUid": "DrawnUi.Draw.ScaledRect", "type": "Struct"}, {"name": "ScaledSize", "href": "DrawnUi.Draw.ScaledSize.html", "topicHref": "DrawnUi.Draw.ScaledSize.html", "topicUid": "DrawnUi.Draw.ScaledSize", "type": "Class"}, {"name": "ScrollFlingAnimator", "href": "DrawnUi.Draw.ScrollFlingAnimator.html", "topicHref": "DrawnUi.Draw.ScrollFlingAnimator.html", "topicUid": "DrawnUi.Draw.ScrollFlingAnimator", "type": "Class"}, {"name": "ScrollFlingVectorAnimator", "href": "DrawnUi.Draw.ScrollFlingVectorAnimator.html", "topicHref": "DrawnUi.Draw.ScrollFlingVectorAnimator.html", "topicUid": "DrawnUi.Draw.ScrollFlingVectorAnimator", "type": "Class"}, {"name": "ScrollInteractionState", "href": "DrawnUi.Draw.ScrollInteractionState.html", "topicHref": "DrawnUi.Draw.ScrollInteractionState.html", "topicUid": "DrawnUi.Draw.ScrollInteractionState", "type": "Enum"}, {"name": "ScrollToIndexOrder", "href": "DrawnUi.Draw.ScrollToIndexOrder.html", "topicHref": "DrawnUi.Draw.ScrollToIndexOrder.html", "topicUid": "DrawnUi.Draw.ScrollToIndexOrder", "type": "Struct"}, {"name": "ScrollToPointOrder", "href": "DrawnUi.Draw.ScrollToPointOrder.html", "topicHref": "DrawnUi.Draw.ScrollToPointOrder.html", "topicUid": "DrawnUi.Draw.ScrollToPointOrder", "type": "Struct"}, {"name": "ShaderDoubleTexturesEffect", "href": "DrawnUi.Draw.ShaderDoubleTexturesEffect.html", "topicHref": "DrawnUi.Draw.ShaderDoubleTexturesEffect.html", "topicUid": "DrawnUi.Draw.ShaderDoubleTexturesEffect", "type": "Class"}, {"name": "ShapeType", "href": "DrawnUi.Draw.ShapeType.html", "topicHref": "DrawnUi.Draw.ShapeType.html", "topicUid": "DrawnUi.Draw.ShapeType", "type": "Enum"}, {"name": "ShimmerAnimator", "href": "DrawnUi.Draw.ShimmerAnimator.html", "topicHref": "DrawnUi.Draw.ShimmerAnimator.html", "topicUid": "DrawnUi.Draw.ShimmerAnimator", "type": "Class"}, {"name": "SidePosition", "href": "DrawnUi.Draw.SidePosition.html", "topicHref": "DrawnUi.Draw.SidePosition.html", "topicUid": "DrawnUi.Draw.SidePosition", "type": "Enum"}, {"name": "Sk3dView", "href": "DrawnUi.Draw.Sk3dView.html", "topicHref": "DrawnUi.Draw.Sk3dView.html", "topicUid": "DrawnUi.Draw.Sk3dView", "type": "Class"}, {"name": "SkCamera3D", "href": "DrawnUi.Draw.SkCamera3D.html", "topicHref": "DrawnUi.Draw.SkCamera3D.html", "topicUid": "DrawnUi.Draw.SkCamera3D", "type": "Class"}, {"name": "SkCamera3D2", "href": "DrawnUi.Draw.SkCamera3D2.html", "topicHref": "DrawnUi.Draw.SkCamera3D2.html", "topicUid": "DrawnUi.Draw.SkCamera3D2", "type": "Class"}, {"name": "SkPatch3D", "href": "DrawnUi.Draw.SkPatch3D.html", "topicHref": "DrawnUi.Draw.SkPatch3D.html", "topicUid": "DrawnUi.Draw.SkPatch3D", "type": "Class"}, {"name": "SkiaAnchorBak", "href": "DrawnUi.Draw.SkiaAnchorBak.html", "topicHref": "DrawnUi.Draw.SkiaAnchorBak.html", "topicUid": "DrawnUi.Draw.SkiaAnchorBak", "type": "Enum"}, {"name": "SkiaBackdrop", "href": "DrawnUi.Draw.SkiaBackdrop.html", "topicHref": "DrawnUi.Draw.SkiaBackdrop.html", "topicUid": "DrawnUi.Draw.SkiaBackdrop", "type": "Class"}, {"name": "SkiaBevel", "href": "DrawnUi.Draw.SkiaBevel.html", "topicHref": "DrawnUi.Draw.SkiaBevel.html", "topicUid": "DrawnUi.Draw.SkiaBevel", "type": "Class"}, {"name": "SkiaButton", "href": "DrawnUi.Draw.SkiaButton.html", "topicHref": "DrawnUi.Draw.SkiaButton.html", "topicUid": "DrawnUi.Draw.SkiaButton", "type": "Class"}, {"name": "SkiaButton.ButtonLabel", "href": "DrawnUi.Draw.SkiaButton.ButtonLabel.html", "topicHref": "DrawnUi.Draw.SkiaButton.ButtonLabel.html", "topicUid": "DrawnUi.Draw.SkiaButton.ButtonLabel", "type": "Class"}, {"name": "SkiaButton.ButtonStyleType", "href": "DrawnUi.Draw.SkiaButton.ButtonStyleType.html", "topicHref": "DrawnUi.Draw.SkiaButton.ButtonStyleType.html", "topicUid": "DrawnUi.Draw.SkiaButton.ButtonStyleType", "type": "Enum"}, {"name": "SkiaButton.IconPositionType", "href": "DrawnUi.Draw.SkiaButton.IconPositionType.html", "topicHref": "DrawnUi.Draw.SkiaButton.IconPositionType.html", "topicUid": "DrawnUi.Draw.SkiaButton.IconPositionType", "type": "Enum"}, {"name": "SkiaCacheType", "href": "DrawnUi.Draw.SkiaCacheType.html", "topicHref": "DrawnUi.Draw.SkiaCacheType.html", "topicUid": "DrawnUi.Draw.SkiaCacheType", "type": "Enum"}, {"name": "SkiaCheckbox", "href": "DrawnUi.Draw.SkiaCheckbox.html", "topicHref": "DrawnUi.Draw.SkiaCheckbox.html", "topicUid": "DrawnUi.Draw.SkiaCheckbox", "type": "Class"}, {"name": "SkiaControl", "href": "DrawnUi.Draw.SkiaControl.html", "topicHref": "DrawnUi.Draw.SkiaControl.html", "topicUid": "DrawnUi.Draw.SkiaControl", "type": "Class"}, {"name": "SkiaControl.CacheValidityType", "href": "DrawnUi.Draw.SkiaControl.CacheValidityType.html", "topicHref": "DrawnUi.Draw.SkiaControl.CacheValidityType.html", "topicUid": "DrawnUi.Draw.SkiaControl.CacheValidityType", "type": "Enum"}, {"name": "SkiaControl.ControlTappedEventArgs", "href": "DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.html", "topicHref": "DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.html", "topicUid": "DrawnUi.Draw.SkiaControl.ControlTappedEventArgs", "type": "Class"}, {"name": "SkiaControl.ParentMeasureRequest", "href": "DrawnUi.Draw.SkiaControl.ParentMeasureRequest.html", "topicHref": "DrawnUi.Draw.SkiaControl.ParentMeasureRequest.html", "topicUid": "DrawnUi.Draw.SkiaControl.ParentMeasureRequest", "type": "Struct"}, {"name": "SkiaControlWithRect", "href": "DrawnUi.Draw.SkiaControlWithRect.html", "topicHref": "DrawnUi.Draw.SkiaControlWithRect.html", "topicUid": "DrawnUi.Draw.SkiaControlWithRect", "type": "Class"}, {"name": "SkiaControlsObservable", "href": "DrawnUi.Draw.SkiaControlsObservable.html", "topicHref": "DrawnUi.Draw.SkiaControlsObservable.html", "topicUid": "DrawnUi.Draw.SkiaControlsObservable", "type": "Class"}, {"name": "SkiaCursor", "href": "DrawnUi.Draw.SkiaCursor.html", "topicHref": "DrawnUi.Draw.SkiaCursor.html", "topicUid": "DrawnUi.Draw.SkiaCursor", "type": "Class"}, {"name": "SkiaDoubleAttachedTexturesEffect", "href": "DrawnUi.Draw.SkiaDoubleAttachedTexturesEffect.html", "topicHref": "DrawnUi.Draw.SkiaDoubleAttachedTexturesEffect.html", "topicUid": "DrawnUi.Draw.SkiaDoubleAttachedTexturesEffect", "type": "Class"}, {"name": "SkiaDrawingContext", "href": "DrawnUi.Draw.SkiaDrawingContext.html", "topicHref": "DrawnUi.Draw.SkiaDrawingContext.html", "topicUid": "DrawnUi.Draw.SkiaDrawingContext", "type": "Class"}, {"name": "SkiaEditor", "href": "DrawnUi.Draw.SkiaEditor.html", "topicHref": "DrawnUi.Draw.SkiaEditor.html", "topicUid": "DrawnUi.Draw.SkiaEditor", "type": "Class"}, {"name": "SkiaEffect", "href": "DrawnUi.Draw.SkiaEffect.html", "topicHref": "DrawnUi.Draw.SkiaEffect.html", "topicUid": "DrawnUi.Draw.SkiaEffect", "type": "Class"}, {"name": "SkiaFontManager", "href": "DrawnUi.Draw.SkiaFontManager.html", "topicHref": "DrawnUi.Draw.SkiaFontManager.html", "topicUid": "DrawnUi.Draw.SkiaFontManager", "type": "Class"}, {"name": "SkiaFrame", "href": "DrawnUi.Draw.SkiaFrame.html", "topicHref": "DrawnUi.Draw.SkiaFrame.html", "topicUid": "DrawnUi.Draw.SkiaFrame", "type": "Class"}, {"name": "SkiaGesturesParameters", "href": "DrawnUi.Draw.SkiaGesturesParameters.html", "topicHref": "DrawnUi.Draw.SkiaGesturesParameters.html", "topicUid": "DrawnUi.Draw.SkiaGesturesParameters", "type": "Class"}, {"name": "SkiaGradient", "href": "DrawnUi.Draw.SkiaGradient.html", "topicHref": "DrawnUi.Draw.SkiaGradient.html", "topicUid": "DrawnUi.Draw.SkiaGradient", "type": "Class"}, {"name": "SkiaGrid", "href": "DrawnUi.Draw.SkiaGrid.html", "topicHref": "DrawnUi.Draw.SkiaGrid.html", "topicUid": "DrawnUi.Draw.SkiaGrid", "type": "Class"}, {"name": "SkiaHotspot", "href": "DrawnUi.Draw.SkiaHotspot.html", "topicHref": "DrawnUi.Draw.SkiaHotspot.html", "topicUid": "DrawnUi.Draw.SkiaHotspot", "type": "Class"}, {"name": "SkiaHotspotZoom", "href": "DrawnUi.Draw.SkiaHotspotZoom.html", "topicHref": "DrawnUi.Draw.SkiaHotspotZoom.html", "topicUid": "DrawnUi.Draw.SkiaHotspotZoom", "type": "Class"}, {"name": "SkiaHoverMask", "href": "DrawnUi.Draw.SkiaHoverMask.html", "topicHref": "DrawnUi.Draw.SkiaHoverMask.html", "topicUid": "DrawnUi.Draw.SkiaHoverMask", "type": "Class"}, {"name": "SkiaImage", "href": "DrawnUi.Draw.SkiaImage.html", "topicHref": "DrawnUi.Draw.SkiaImage.html", "topicUid": "DrawnUi.Draw.SkiaImage", "type": "Class"}, {"name": "SkiaImage.RescaledBitmap", "href": "DrawnUi.Draw.SkiaImage.RescaledBitmap.html", "topicHref": "DrawnUi.Draw.SkiaImage.RescaledBitmap.html", "topicUid": "DrawnUi.Draw.SkiaImage.RescaledBitmap", "type": "Class"}, {"name": "SkiaImageEffect", "href": "DrawnUi.Draw.SkiaImageEffect.html", "topicHref": "DrawnUi.Draw.SkiaImageEffect.html", "topicUid": "DrawnUi.Draw.SkiaImageEffect", "type": "Enum"}, {"name": "SkiaImageEffects", "href": "DrawnUi.Draw.SkiaImageEffects.html", "topicHref": "DrawnUi.Draw.SkiaImageEffects.html", "topicUid": "DrawnUi.Draw.SkiaImageEffects", "type": "Class"}, {"name": "SkiaImageManager", "href": "DrawnUi.Draw.SkiaImageManager.html", "topicHref": "DrawnUi.Draw.SkiaImageManager.html", "topicUid": "DrawnUi.Draw.SkiaImageManager", "type": "Class"}, {"name": "SkiaImageManager.QueueItem", "href": "DrawnUi.Draw.SkiaImageManager.QueueItem.html", "topicHref": "DrawnUi.Draw.SkiaImageManager.QueueItem.html", "topicUid": "DrawnUi.Draw.SkiaImageManager.QueueItem", "type": "Class"}, {"name": "SkiaImageTiles", "href": "DrawnUi.Draw.SkiaImageTiles.html", "topicHref": "DrawnUi.Draw.SkiaImageTiles.html", "topicUid": "DrawnUi.Draw.SkiaImageTiles", "type": "Class"}, {"name": "SkiaLabel", "href": "DrawnUi.Draw.SkiaLabel.html", "topicHref": "DrawnUi.Draw.SkiaLabel.html", "topicUid": "DrawnUi.Draw.SkiaLabel", "type": "Class"}, {"name": "SkiaLabel.DecomposedText", "href": "DrawnUi.Draw.SkiaLabel.DecomposedText.html", "topicHref": "DrawnUi.Draw.SkiaLabel.DecomposedText.html", "topicUid": "DrawnUi.Draw.SkiaLabel.DecomposedText", "type": "Class"}, {"name": "SkiaLabel.EmojiData", "href": "DrawnUi.Draw.SkiaLabel.EmojiData.html", "topicHref": "DrawnUi.Draw.SkiaLabel.EmojiData.html", "topicUid": "DrawnUi.Draw.SkiaLabel.EmojiData", "type": "Class"}, {"name": "SkiaLabel.ObjectPools", "href": "DrawnUi.Draw.SkiaLabel.ObjectPools.html", "topicHref": "DrawnUi.Draw.SkiaLabel.ObjectPools.html", "topicUid": "DrawnUi.Draw.SkiaLabel.ObjectPools", "type": "Class"}, {"name": "SkiaLabel.PooledStringBuilder", "href": "DrawnUi.Draw.SkiaLabel.PooledStringBuilder.html", "topicHref": "DrawnUi.Draw.SkiaLabel.PooledStringBuilder.html", "topicUid": "DrawnUi.Draw.SkiaLabel.PooledStringBuilder", "type": "Struct"}, {"name": "SkiaLabel.SpanCollection", "href": "DrawnUi.Draw.SkiaLabel.SpanCollection.html", "topicHref": "DrawnUi.Draw.SkiaLabel.SpanCollection.html", "topicUid": "DrawnUi.Draw.SkiaLabel.SpanCollection", "type": "Class"}, {"name": "SkiaLabel.SpanMeasurement", "href": "DrawnUi.Draw.SkiaLabel.SpanMeasurement.html", "topicHref": "DrawnUi.Draw.SkiaLabel.SpanMeasurement.html", "topicUid": "DrawnUi.Draw.SkiaLabel.SpanMeasurement", "type": "Class"}, {"name": "SkiaLabel.TextMetrics", "href": "DrawnUi.Draw.SkiaLabel.TextMetrics.html", "topicHref": "DrawnUi.Draw.SkiaLabel.TextMetrics.html", "topicUid": "DrawnUi.Draw.SkiaLabel.TextMetrics", "type": "Class"}, {"name": "SkiaLabelFps", "href": "DrawnUi.Draw.SkiaLabelFps.html", "topicHref": "DrawnUi.Draw.SkiaLabelFps.html", "topicUid": "DrawnUi.Draw.SkiaLabelFps", "type": "Class"}, {"name": "<PERSON>a<PERSON><PERSON><PERSON>", "href": "DrawnUi.Draw.SkiaLayer.html", "topicHref": "DrawnUi.Draw.SkiaLayer.html", "topicUid": "DrawnUi.Draw.SkiaLayer", "type": "Class"}, {"name": "SkiaLayout", "href": "DrawnUi.Draw.SkiaLayout.html", "topicHref": "DrawnUi.Draw.SkiaLayout.html", "topicUid": "DrawnUi.Draw.SkiaLayout", "type": "Class"}, {"name": "SkiaLayout.BuildWrapLayout", "href": "DrawnUi.Draw.SkiaLayout.BuildWrapLayout.html", "topicHref": "DrawnUi.Draw.SkiaLayout.BuildWrapLayout.html", "topicUid": "DrawnUi.Draw.SkiaLayout.BuildWrapLayout", "type": "Class"}, {"name": "SkiaLayout.Cell", "href": "DrawnUi.Draw.SkiaLayout.Cell.html", "topicHref": "DrawnUi.Draw.SkiaLayout.Cell.html", "topicUid": "DrawnUi.Draw.SkiaLayout.Cell", "type": "Class"}, {"name": "SkiaLayout.SecondPassArrange", "href": "DrawnUi.Draw.SkiaLayout.SecondPassArrange.html", "topicHref": "DrawnUi.Draw.SkiaLayout.SecondPassArrange.html", "topicUid": "DrawnUi.Draw.SkiaLayout.SecondPassArrange", "type": "Class"}, {"name": "SkiaLayout.SkiaGridStructure", "href": "DrawnUi.Draw.SkiaLayout.SkiaGridStructure.html", "topicHref": "DrawnUi.Draw.SkiaLayout.SkiaGridStructure.html", "topicUid": "DrawnUi.Draw.SkiaLayout.SkiaGridStructure", "type": "Class"}, {"name": "SkiaMarkdownLabel", "href": "DrawnUi.Draw.SkiaMarkdownLabel.html", "topicHref": "DrawnUi.Draw.SkiaMarkdownLabel.html", "topicUid": "DrawnUi.Draw.SkiaMarkdownLabel", "type": "Class"}, {"name": "SkiaMauiElement", "href": "DrawnUi.Draw.SkiaMauiElement.html", "topicHref": "DrawnUi.Draw.SkiaMauiElement.html", "topicUid": "DrawnUi.Draw.SkiaMauiElement", "type": "Class"}, {"name": "SkiaPoint", "href": "DrawnUi.Draw.SkiaPoint.html", "topicHref": "DrawnUi.Draw.SkiaPoint.html", "topicUid": "DrawnUi.Draw.SkiaPoint", "type": "Class"}, {"name": "SkiaProgress", "href": "DrawnUi.Draw.SkiaProgress.html", "topicHref": "DrawnUi.Draw.SkiaProgress.html", "topicUid": "DrawnUi.Draw.SkiaProgress", "type": "Class"}, {"name": "SkiaRangeBase", "href": "DrawnUi.Draw.SkiaRangeBase.html", "topicHref": "DrawnUi.Draw.SkiaRangeBase.html", "topicUid": "DrawnUi.Draw.SkiaRangeBase", "type": "Class"}, {"name": "SkiaRow", "href": "DrawnUi.Draw.HStack.html", "topicHref": "DrawnUi.Draw.HStack.html", "topicUid": "DrawnUi.Draw.SkiaRow", "type": "Class"}, {"name": "SkiaScroll", "href": "DrawnUi.Draw.SkiaScroll.html", "topicHref": "DrawnUi.Draw.SkiaScroll.html", "topicUid": "DrawnUi.Draw.SkiaScroll", "type": "Class"}, {"name": "SkiaScrollLooped", "href": "DrawnUi.Draw.SkiaScrollLooped.html", "topicHref": "DrawnUi.Draw.SkiaScrollLooped.html", "topicUid": "DrawnUi.Draw.SkiaScrollLooped", "type": "Class"}, {"name": "SkiaSetter", "href": "DrawnUi.Draw.SkiaSetter.html", "topicHref": "DrawnUi.Draw.SkiaSetter.html", "topicUid": "DrawnUi.Draw.SkiaSetter", "type": "Class"}, {"name": "SkiaShaderEffect", "href": "DrawnUi.Draw.SkiaShaderEffect.html", "topicHref": "DrawnUi.Draw.SkiaShaderEffect.html", "topicUid": "DrawnUi.Draw.SkiaShaderEffect", "type": "Class"}, {"name": "SkiaShadow", "href": "DrawnUi.Draw.SkiaShadow.html", "topicHref": "DrawnUi.Draw.SkiaShadow.html", "topicUid": "DrawnUi.Draw.SkiaShadow", "type": "Class"}, {"name": "SkiaShape", "href": "DrawnUi.Draw.SkiaShape.html", "topicHref": "DrawnUi.Draw.SkiaShape.html", "topicUid": "DrawnUi.Draw.SkiaShape", "type": "Class"}, {"name": "SkiaShape.ShapePaintArguments", "href": "DrawnUi.Draw.SkiaShape.ShapePaintArguments.html", "topicHref": "DrawnUi.Draw.SkiaShape.ShapePaintArguments.html", "topicUid": "DrawnUi.Draw.SkiaShape.ShapePaintArguments", "type": "Struct"}, {"name": "SkiaSlider", "href": "DrawnUi.Draw.SkiaSlider.html", "topicHref": "DrawnUi.Draw.SkiaSlider.html", "topicUid": "DrawnUi.Draw.SkiaSlider", "type": "Class"}, {"name": "SkiaStack", "href": "DrawnUi.Draw.SkiaStack.html", "topicHref": "DrawnUi.Draw.SkiaStack.html", "topicUid": "DrawnUi.Draw.SkiaStack", "type": "Class"}, {"name": "SkiaSvg", "href": "DrawnUi.Draw.SkiaSvg.html", "topicHref": "DrawnUi.Draw.SkiaSvg.html", "topicUid": "DrawnUi.Draw.SkiaSvg", "type": "Class"}, {"name": "SkiaSwitch", "href": "DrawnUi.Draw.SkiaSwitch.html", "topicHref": "DrawnUi.Draw.SkiaSwitch.html", "topicUid": "DrawnUi.Draw.SkiaSwitch", "type": "Class"}, {"name": "SkiaToggle", "href": "DrawnUi.Draw.SkiaToggle.html", "topicHref": "DrawnUi.Draw.SkiaToggle.html", "topicUid": "DrawnUi.Draw.SkiaToggle", "type": "Class"}, {"name": "SkiaTouchAnimation", "href": "DrawnUi.Draw.SkiaTouchAnimation.html", "topicHref": "DrawnUi.Draw.SkiaTouchAnimation.html", "topicUid": "DrawnUi.Draw.SkiaTouchAnimation", "type": "Enum"}, {"name": "SkiaValueAnimator", "href": "DrawnUi.Draw.SkiaValueAnimator.html", "topicHref": "DrawnUi.Draw.SkiaValueAnimator.html", "topicUid": "DrawnUi.Draw.SkiaValueAnimator", "type": "Class"}, {"name": "SkiaVectorAnimator", "href": "DrawnUi.Draw.SkiaVectorAnimator.html", "topicHref": "DrawnUi.Draw.SkiaVectorAnimator.html", "topicUid": "DrawnUi.Draw.SkiaVectorAnimator", "type": "Class"}, {"name": "SkiaWrap", "href": "DrawnUi.Draw.SkiaWrap.html", "topicHref": "DrawnUi.Draw.SkiaWrap.html", "topicUid": "DrawnUi.Draw.SkiaWrap", "type": "Class"}, {"name": "Slider<PERSON><PERSON>b", "href": "DrawnUi.Draw.SliderThumb.html", "topicHref": "DrawnUi.Draw.SliderThumb.html", "topicUid": "DrawnUi.Draw.SliderThumb", "type": "Class"}, {"name": "SliderTrail", "href": "DrawnUi.Draw.SliderTrail.html", "topicHref": "DrawnUi.Draw.SliderTrail.html", "topicUid": "DrawnUi.Draw.SliderTrail", "type": "Class"}, {"name": "SliderValueDesc", "href": "DrawnUi.Draw.SliderValueDesc.html", "topicHref": "DrawnUi.Draw.SliderValueDesc.html", "topicUid": "DrawnUi.Draw.SliderValueDesc", "type": "Class"}, {"name": "SnapToChildrenType", "href": "DrawnUi.Draw.SnapToChildrenType.html", "topicHref": "DrawnUi.Draw.SnapToChildrenType.html", "topicUid": "DrawnUi.Draw.SnapToChildrenType", "type": "Enum"}, {"name": "Snapping", "href": "DrawnUi.Draw.Snapping.html", "topicHref": "DrawnUi.Draw.Snapping.html", "topicUid": "DrawnUi.Draw.Snapping", "type": "Class"}, {"name": "SnappingLayout", "href": "DrawnUi.Draw.SnappingLayout.html", "topicHref": "DrawnUi.Draw.SnappingLayout.html", "topicUid": "DrawnUi.Draw.SnappingLayout", "type": "Class"}, {"name": "SortedGestureListeners", "href": "DrawnUi.Draw.SortedGestureListeners.html", "topicHref": "DrawnUi.Draw.SortedGestureListeners.html", "topicUid": "DrawnUi.Draw.SortedGestureListeners", "type": "Class"}, {"name": "SourceType", "href": "DrawnUi.Draw.SourceType.html", "topicHref": "DrawnUi.Draw.SourceType.html", "topicUid": "DrawnUi.Draw.SourceType", "type": "Enum"}, {"name": "SpaceDistribution", "href": "DrawnUi.Draw.SpaceDistribution.html", "topicHref": "DrawnUi.Draw.SpaceDistribution.html", "topicUid": "DrawnUi.Draw.SpaceDistribution", "type": "Enum"}, {"name": "SpringExtensions", "href": "DrawnUi.Draw.SpringExtensions.html", "topicHref": "DrawnUi.Draw.SpringExtensions.html", "topicUid": "DrawnUi.Draw.SpringExtensions", "type": "Class"}, {"name": "SpringTimingParameters", "href": "DrawnUi.Draw.SpringTimingParameters.html", "topicHref": "DrawnUi.Draw.SpringTimingParameters.html", "topicUid": "DrawnUi.Draw.SpringTimingParameters", "type": "Class"}, {"name": "SpringTimingVectorParameters", "href": "DrawnUi.Draw.SpringTimingVectorParameters.html", "topicHref": "DrawnUi.Draw.SpringTimingVectorParameters.html", "topicUid": "DrawnUi.Draw.SpringTimingVectorParameters", "type": "Class"}, {"name": "SpringWithVelocityAnimator", "href": "DrawnUi.Draw.SpringWithVelocityAnimator.html", "topicHref": "DrawnUi.Draw.SpringWithVelocityAnimator.html", "topicUid": "DrawnUi.Draw.SpringWithVelocityAnimator", "type": "Class"}, {"name": "SpringWithVelocityVectorAnimator", "href": "DrawnUi.Draw.SpringWithVelocityVectorAnimator.html", "topicHref": "DrawnUi.Draw.SpringWithVelocityVectorAnimator.html", "topicUid": "DrawnUi.Draw.SpringWithVelocityVectorAnimator", "type": "Class"}, {"name": "StackLayoutStructure", "href": "DrawnUi.Draw.StackLayoutStructure.html", "topicHref": "DrawnUi.Draw.StackLayoutStructure.html", "topicUid": "DrawnUi.Draw.StackLayoutStructure", "type": "Class"}, {"name": "StateEffect", "href": "DrawnUi.Draw.StateEffect.html", "topicHref": "DrawnUi.Draw.StateEffect.html", "topicUid": "DrawnUi.Draw.StateEffect", "type": "Class"}, {"name": "StaticResourcesExtensions", "href": "DrawnUi.Draw.StaticResourcesExtensions.html", "topicHref": "DrawnUi.Draw.StaticResourcesExtensions.html", "topicUid": "DrawnUi.Draw.StaticResourcesExtensions", "type": "Class"}, {"name": "StringReference", "href": "DrawnUi.Draw.StringReference.html", "topicHref": "DrawnUi.Draw.StringReference.html", "topicUid": "DrawnUi.Draw.StringReference", "type": "Struct"}, {"name": "Super", "href": "DrawnUi.Draw.Super.html", "topicHref": "DrawnUi.Draw.Super.html", "topicUid": "DrawnUi.Draw.Super", "type": "Class"}, {"name": "SvgSpan", "href": "DrawnUi.Draw.SvgSpan.html", "topicHref": "DrawnUi.Draw.SvgSpan.html", "topicUid": "DrawnUi.Draw.SvgSpan", "type": "Class"}, {"name": "TemplatedViewsPool", "href": "DrawnUi.Draw.TemplatedViewsPool.html", "topicHref": "DrawnUi.Draw.TemplatedViewsPool.html", "topicUid": "DrawnUi.Draw.TemplatedViewsPool", "type": "Class"}, {"name": "TextLine", "href": "DrawnUi.Draw.TextLine.html", "topicHref": "DrawnUi.Draw.TextLine.html", "topicUid": "DrawnUi.Draw.TextLine", "type": "Class"}, {"name": "TextSpan", "href": "DrawnUi.Draw.TextSpan.html", "topicHref": "DrawnUi.Draw.TextSpan.html", "topicUid": "DrawnUi.Draw.TextSpan", "type": "Class"}, {"name": "TextTransform", "href": "DrawnUi.Draw.TextTransform.html", "topicHref": "DrawnUi.Draw.TextTransform.html", "topicUid": "DrawnUi.Draw.TextTransform", "type": "Enum"}, {"name": "TintEffect", "href": "DrawnUi.Draw.TintEffect.html", "topicHref": "DrawnUi.Draw.TintEffect.html", "topicUid": "DrawnUi.Draw.TintEffect", "type": "Class"}, {"name": "TintWithAlphaEffect", "href": "DrawnUi.Draw.TintWithAlphaEffect.html", "topicHref": "DrawnUi.Draw.TintWithAlphaEffect.html", "topicUid": "DrawnUi.Draw.TintWithAlphaEffect", "type": "Class"}, {"name": "ToggleAnimator", "href": "DrawnUi.Draw.ToggleAnimator.html", "topicHref": "DrawnUi.Draw.ToggleAnimator.html", "topicUid": "DrawnUi.Draw.ToggleAnimator", "type": "Class"}, {"name": "TrackedObject<T>", "href": "DrawnUi.Draw.TrackedObject-1.html", "topicHref": "DrawnUi.Draw.TrackedObject-1.html", "topicUid": "DrawnUi.Draw.TrackedObject`1", "type": "Class"}, {"name": "TransformAspect", "href": "DrawnUi.Draw.TransformAspect.html", "topicHref": "DrawnUi.Draw.TransformAspect.html", "topicUid": "DrawnUi.Draw.TransformAspect", "type": "Enum"}, {"name": "UnderdampedSpringTimingParameters", "href": "DrawnUi.Draw.UnderdampedSpringTimingParameters.html", "topicHref": "DrawnUi.Draw.UnderdampedSpringTimingParameters.html", "topicUid": "DrawnUi.Draw.UnderdampedSpringTimingParameters", "type": "Class"}, {"name": "UnderdampedSpringTimingVectorParameters", "href": "DrawnUi.Draw.UnderdampedSpringTimingVectorParameters.html", "topicHref": "DrawnUi.Draw.UnderdampedSpringTimingVectorParameters.html", "topicUid": "DrawnUi.Draw.UnderdampedSpringTimingVectorParameters", "type": "Struct"}, {"name": "UsedGlyph", "href": "DrawnUi.Draw.UsedGlyph.html", "topicHref": "DrawnUi.Draw.UsedGlyph.html", "topicUid": "DrawnUi.Draw.UsedGlyph", "type": "Struct"}, {"name": "VelocityAccumulator", "href": "DrawnUi.Draw.VelocityAccumulator.html", "topicHref": "DrawnUi.Draw.VelocityAccumulator.html", "topicUid": "DrawnUi.Draw.VelocityAccumulator", "type": "Class"}, {"name": "ViewportScrollType", "href": "DrawnUi.Draw.ViewportScrollType.html", "topicHref": "DrawnUi.Draw.ViewportScrollType.html", "topicUid": "DrawnUi.Draw.ViewportScrollType", "type": "Enum"}, {"name": "ViewsAdapter", "href": "DrawnUi.Draw.ViewsAdapter.html", "topicHref": "DrawnUi.Draw.ViewsAdapter.html", "topicUid": "DrawnUi.Draw.ViewsAdapter", "type": "Class"}, {"name": "ViewsIterator", "href": "DrawnUi.Draw.ViewsIterator.html", "topicHref": "DrawnUi.Draw.ViewsIterator.html", "topicUid": "DrawnUi.Draw.ViewsIterator", "type": "Class"}, {"name": "VirtualScroll", "href": "DrawnUi.Draw.VirtualScroll.html", "topicHref": "DrawnUi.Draw.VirtualScroll.html", "topicUid": "DrawnUi.Draw.VirtualScroll", "type": "Class"}, {"name": "VirtualisationType", "href": "DrawnUi.Draw.VirtualisationType.html", "topicHref": "DrawnUi.Draw.VirtualisationType.html", "topicUid": "DrawnUi.Draw.VirtualisationType", "type": "Enum"}, {"name": "ViscousFluidInterpolator", "href": "DrawnUi.Draw.ViscousFluidInterpolator.html", "topicHref": "DrawnUi.Draw.ViscousFluidInterpolator.html", "topicUid": "DrawnUi.Draw.ViscousFluidInterpolator", "type": "Class"}, {"name": "VisualLayer", "href": "DrawnUi.Draw.VisualLayer.html", "topicHref": "DrawnUi.Draw.VisualLayer.html", "topicUid": "DrawnUi.Draw.VisualLayer", "type": "Class"}, {"name": "VisualTreeHandler", "href": "DrawnUi.Draw.VisualTreeHandler.html", "topicHref": "DrawnUi.Draw.VisualTreeHandler.html", "topicUid": "DrawnUi.Draw.VisualTreeHandler", "type": "Class"}, {"name": "WindowParameters", "href": "DrawnUi.Draw.WindowParameters.html", "topicHref": "DrawnUi.Draw.WindowParameters.html", "topicUid": "DrawnUi.Draw.WindowParameters", "type": "Struct"}, {"name": "ZoomContent", "href": "DrawnUi.Draw.ZoomContent.html", "topicHref": "DrawnUi.Draw.ZoomContent.html", "topicUid": "DrawnUi.Draw.ZoomContent", "type": "Class"}, {"name": "ZoomEventArgs", "href": "DrawnUi.Draw.ZoomEventArgs.html", "topicHref": "DrawnUi.Draw.ZoomEventArgs.html", "topicUid": "DrawnUi.Draw.ZoomEventArgs", "type": "Class"}]}, {"name": "DrawnUi.Extensions", "href": "DrawnUi.Extensions.html", "topicHref": "DrawnUi.Extensions.html", "topicUid": "DrawnUi.Extensions", "type": "Namespace", "items": [{"name": "FloatingPointExtensions", "href": "DrawnUi.Extensions.FloatingPointExtensions.html", "topicHref": "DrawnUi.Extensions.FloatingPointExtensions.html", "topicUid": "DrawnUi.Extensions.FloatingPointExtensions", "type": "Class"}, {"name": "InternalExtensions", "href": "DrawnUi.Extensions.InternalExtensions.html", "topicHref": "DrawnUi.Extensions.InternalExtensions.html", "topicUid": "DrawnUi.Extensions.InternalExtensions", "type": "Class"}, {"name": "PointExtensions", "href": "DrawnUi.Extensions.PointExtensions.html", "topicHref": "DrawnUi.Extensions.PointExtensions.html", "topicUid": "DrawnUi.Extensions.PointExtensions", "type": "Class"}]}, {"name": "DrawnUi.Features.Images", "href": "DrawnUi.Features.Images.html", "topicHref": "DrawnUi.Features.Images.html", "topicUid": "DrawnUi.Features.Images", "type": "Namespace", "items": [{"name": "ImagesExtensions", "href": "DrawnUi.Features.Images.ImagesExtensions.html", "topicHref": "DrawnUi.Features.Images.ImagesExtensions.html", "topicUid": "DrawnUi.Features.Images.ImagesExtensions", "type": "Class"}]}, {"name": "DrawnUi.Infrastructure", "href": "DrawnUi.Infrastructure.html", "topicHref": "DrawnUi.Infrastructure.html", "topicUid": "DrawnUi.Infrastructure", "type": "Namespace", "items": [{"name": "ClosedRange<T>", "href": "DrawnUi.Infrastructure.ClosedRange-1.html", "topicHref": "DrawnUi.Infrastructure.ClosedRange-1.html", "topicUid": "DrawnUi.Infrastructure.ClosedRange`1", "type": "Struct"}, {"name": "FileDescriptor", "href": "DrawnUi.Infrastructure.FileDescriptor.html", "topicHref": "DrawnUi.Infrastructure.FileDescriptor.html", "topicUid": "DrawnUi.Infrastructure.FileDescriptor", "type": "Class"}, {"name": "Files", "href": "DrawnUi.Infrastructure.Files.html", "topicHref": "DrawnUi.Infrastructure.Files.html", "topicUid": "DrawnUi.Infrastructure.Files", "type": "Class"}, {"name": "MeasuringConstraints", "href": "DrawnUi.Infrastructure.MeasuringConstraints.html", "topicHref": "DrawnUi.Infrastructure.MeasuringConstraints.html", "topicUid": "DrawnUi.Infrastructure.MeasuringConstraints", "type": "Struct"}, {"name": "PaperFormat", "href": "DrawnUi.Infrastructure.PaperFormat.html", "topicHref": "DrawnUi.Infrastructure.PaperFormat.html", "topicUid": "DrawnUi.Infrastructure.PaperFormat", "type": "Enum"}, {"name": "Pdf", "href": "DrawnUi.Infrastructure.Pdf.html", "topicHref": "DrawnUi.Infrastructure.Pdf.html", "topicUid": "DrawnUi.Infrastructure.Pdf", "type": "Class"}, {"name": "PdfPagePosition", "href": "DrawnUi.Infrastructure.PdfPagePosition.html", "topicHref": "DrawnUi.Infrastructure.PdfPagePosition.html", "topicUid": "DrawnUi.Infrastructure.PdfPagePosition", "type": "Struct"}, {"name": "Pen<PERSON><PERSON>", "href": "DrawnUi.Infrastructure.Pendulum.html", "topicHref": "DrawnUi.Infrastructure.Pendulum.html", "topicUid": "DrawnUi.Infrastructure.Pendulum", "type": "Class"}, {"name": "PerpetualPendulum", "href": "DrawnUi.Infrastructure.PerpetualPendulum.html", "topicHref": "DrawnUi.Infrastructure.PerpetualPendulum.html", "topicUid": "DrawnUi.Infrastructure.PerpetualPendulum", "type": "Class"}, {"name": "RenderOnTimer", "href": "DrawnUi.Infrastructure.RenderOnTimer.html", "topicHref": "DrawnUi.Infrastructure.RenderOnTimer.html", "topicUid": "DrawnUi.Infrastructure.RenderOnTimer", "type": "Class"}, {"name": "SkSl", "href": "DrawnUi.Infrastructure.SkSl.html", "topicHref": "DrawnUi.Infrastructure.SkSl.html", "topicUid": "DrawnUi.Infrastructure.SkSl", "type": "Class"}, {"name": "SkiaTouchResultContext", "href": "DrawnUi.Infrastructure.SkiaTouchResultContext.html", "topicHref": "DrawnUi.Infrastructure.SkiaTouchResultContext.html", "topicUid": "DrawnUi.Infrastructure.SkiaTouchResultContext", "type": "Struct"}, {"name": "Spring", "href": "DrawnUi.Infrastructure.Spring.html", "topicHref": "DrawnUi.Infrastructure.Spring.html", "topicUid": "DrawnUi.Infrastructure.Spring", "type": "Struct"}, {"name": "StorageType", "href": "DrawnUi.Infrastructure.StorageType.html", "topicHref": "DrawnUi.Infrastructure.StorageType.html", "topicUid": "DrawnUi.Infrastructure.StorageType", "type": "Enum"}, {"name": "Vector", "href": "DrawnUi.Infrastructure.Vector.html", "topicHref": "DrawnUi.Infrastructure.Vector.html", "topicUid": "DrawnUi.Infrastructure.Vector", "type": "Class"}, {"name": "VisualTransform", "href": "DrawnUi.Infrastructure.VisualTransform.html", "topicHref": "DrawnUi.Infrastructure.VisualTransform.html", "topicUid": "DrawnUi.Infrastructure.VisualTransform", "type": "Class"}, {"name": "VisualTransformNative", "href": "DrawnUi.Infrastructure.VisualTransformNative.html", "topicHref": "DrawnUi.Infrastructure.VisualTransformNative.html", "topicUid": "DrawnUi.Infrastructure.VisualTransformNative", "type": "Struct"}, {"name": "VisualTreeChain", "href": "DrawnUi.Infrastructure.VisualTreeChain.html", "topicHref": "DrawnUi.Infrastructure.VisualTreeChain.html", "topicUid": "DrawnUi.Infrastructure.VisualTreeChain", "type": "Class"}]}, {"name": "DrawnUi.Infrastructure.Enums", "href": "DrawnUi.Infrastructure.Enums.html", "topicHref": "DrawnUi.Infrastructure.Enums.html", "topicUid": "DrawnUi.Infrastructure.Enums", "type": "Namespace", "items": [{"name": "DoubleViewTransitionType", "href": "DrawnUi.Infrastructure.Enums.DoubleViewTransitionType.html", "topicHref": "DrawnUi.Infrastructure.Enums.DoubleViewTransitionType.html", "topicUid": "DrawnUi.Infrastructure.Enums.DoubleViewTransitionType", "type": "Enum"}, {"name": "UpdateMode", "href": "DrawnUi.Infrastructure.Enums.UpdateMode.html", "topicHref": "DrawnUi.Infrastructure.Enums.UpdateMode.html", "topicUid": "DrawnUi.Infrastructure.Enums.UpdateMode", "type": "Enum"}]}, {"name": "DrawnUi.Infrastructure.Helpers", "href": "DrawnUi.Infrastructure.Helpers.html", "topicHref": "DrawnUi.Infrastructure.Helpers.html", "topicUid": "DrawnUi.Infrastructure.Helpers", "type": "Namespace", "items": [{"name": "IntersectionUtils", "href": "DrawnUi.Infrastructure.Helpers.IntersectionUtils.html", "topicHref": "DrawnUi.Infrastructure.Helpers.IntersectionUtils.html", "topicUid": "DrawnUi.Infrastructure.Helpers.IntersectionUtils", "type": "Class"}, {"name": "RubberBandUtils", "href": "DrawnUi.Infrastructure.Helpers.RubberBandUtils.html", "topicHref": "DrawnUi.Infrastructure.Helpers.RubberBandUtils.html", "topicUid": "DrawnUi.Infrastructure.Helpers.RubberBandUtils", "type": "Class"}, {"name": "VelocityTracker", "href": "DrawnUi.Infrastructure.Helpers.VelocityTracker.html", "topicHref": "DrawnUi.Infrastructure.Helpers.VelocityTracker.html", "topicUid": "DrawnUi.Infrastructure.Helpers.VelocityTracker", "type": "Class"}]}, {"name": "DrawnUi.Infrastructure.Models", "href": "DrawnUi.Infrastructure.Models.html", "topicHref": "DrawnUi.Infrastructure.Models.html", "topicUid": "DrawnUi.Infrastructure.Models", "type": "Namespace", "items": [{"name": "BitmapLoadedEventArgs", "href": "DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.html", "topicHref": "DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs.html", "topicUid": "DrawnUi.Infrastructure.Models.BitmapLoadedEventArgs", "type": "Class"}, {"name": "ContentLoadedEventArgs", "href": "DrawnUi.Infrastructure.Models.ContentLoadedEventArgs.html", "topicHref": "DrawnUi.Infrastructure.Models.ContentLoadedEventArgs.html", "topicUid": "DrawnUi.Infrastructure.Models.ContentLoadedEventArgs", "type": "Class"}, {"name": "ImageSourceResourceStream", "href": "DrawnUi.Infrastructure.Models.ImageSourceResourceStream.html", "topicHref": "DrawnUi.Infrastructure.Models.ImageSourceResourceStream.html", "topicUid": "DrawnUi.Infrastructure.Models.ImageSourceResourceStream", "type": "Class"}, {"name": "LimitedConcurrentQueue<T>", "href": "DrawnUi.Infrastructure.Models.LimitedConcurrentQueue-1.html", "topicHref": "DrawnUi.Infrastructure.Models.LimitedConcurrentQueue-1.html", "topicUid": "DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1", "type": "Class"}, {"name": "LimitedQueue<T>", "href": "DrawnUi.Infrastructure.Models.LimitedQueue-1.html", "topicHref": "DrawnUi.Infrastructure.Models.LimitedQueue-1.html", "topicUid": "DrawnUi.Infrastructure.Models.LimitedQueue`1", "type": "Class"}, {"name": "LimitedStack<T>", "href": "DrawnUi.Infrastructure.Models.LimitedStack-1.html", "topicHref": "DrawnUi.Infrastructure.Models.LimitedStack-1.html", "topicUid": "DrawnUi.Infrastructure.Models.LimitedStack`1", "type": "Class"}, {"name": "OrderedIndex", "href": "DrawnUi.Infrastructure.Models.OrderedIndex.html", "topicHref": "DrawnUi.Infrastructure.Models.OrderedIndex.html", "topicUid": "DrawnUi.Infrastructure.Models.OrderedIndex", "type": "Class"}]}, {"name": "DrawnUi.Infrastructure.Xaml", "href": "DrawnUi.Infrastructure.Xaml.html", "topicHref": "DrawnUi.Infrastructure.Xaml.html", "topicUid": "DrawnUi.Infrastructure.Xaml", "type": "Namespace", "items": [{"name": "ColumnDefinitionTypeConverter", "href": "DrawnUi.Infrastructure.Xaml.ColumnDefinitionTypeConverter.html", "topicHref": "DrawnUi.Infrastructure.Xaml.ColumnDefinitionTypeConverter.html", "topicUid": "DrawnUi.Infrastructure.Xaml.ColumnDefinitionTypeConverter", "type": "Class"}, {"name": "FrameworkImageSourceConverter", "href": "DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.html", "topicHref": "DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter.html", "topicUid": "DrawnUi.Infrastructure.Xaml.FrameworkImageSourceConverter", "type": "Class"}, {"name": "NotConverter", "href": "DrawnUi.Infrastructure.Xaml.NotConverter.html", "topicHref": "DrawnUi.Infrastructure.Xaml.NotConverter.html", "topicUid": "DrawnUi.Infrastructure.Xaml.NotConverter", "type": "Class"}, {"name": "RowDefinitionTypeConverter", "href": "DrawnUi.Infrastructure.Xaml.RowDefinitionTypeConverter.html", "topicHref": "DrawnUi.Infrastructure.Xaml.RowDefinitionTypeConverter.html", "topicUid": "DrawnUi.Infrastructure.Xaml.RowDefinitionTypeConverter", "type": "Class"}, {"name": "SkiaPointCollectionConverter", "href": "DrawnUi.Infrastructure.Xaml.SkiaPointCollectionConverter.html", "topicHref": "DrawnUi.Infrastructure.Xaml.SkiaPointCollectionConverter.html", "topicUid": "DrawnUi.Infrastructure.Xaml.SkiaPointCollectionConverter", "type": "Class"}, {"name": "SkiaShadowsCollection", "href": "DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection.html", "topicHref": "DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection.html", "topicUid": "DrawnUi.Infrastructure.Xaml.SkiaShadowsCollection", "type": "Class"}, {"name": "StringToDoubleArrayTypeConverter", "href": "DrawnUi.Infrastructure.Xaml.StringToDoubleArrayTypeConverter.html", "topicHref": "DrawnUi.Infrastructure.Xaml.StringToDoubleArrayTypeConverter.html", "topicUid": "DrawnUi.Infrastructure.Xaml.StringToDoubleArrayTypeConverter", "type": "Class"}]}, {"name": "DrawnUi.Internals", "href": "DrawnUi.Internals.html", "topicHref": "DrawnUi.Internals.html", "topicUid": "DrawnUi.Internals", "type": "Namespace", "items": [{"name": "SelectableAction", "href": "DrawnUi.Internals.SelectableAction.html", "topicHref": "DrawnUi.Internals.SelectableAction.html", "topicUid": "DrawnUi.Internals.SelectableAction", "type": "Class"}, {"name": "TitleWithStringId", "href": "DrawnUi.Internals.TitleWithStringId.html", "topicHref": "DrawnUi.Internals.TitleWithStringId.html", "topicUid": "DrawnUi.Internals.TitleWithStringId", "type": "Class"}]}, {"name": "DrawnUi.Internals.Markup", "href": "DrawnUi.Internals.Markup.html", "topicHref": "DrawnUi.Internals.Markup.html", "topicUid": "DrawnUi.Internals.Markup", "type": "Namespace", "items": [{"name": "MarkupExtensions", "href": "DrawnUi.Internals.Markup.MarkupExtensions.html", "topicHref": "DrawnUi.Internals.Markup.MarkupExtensions.html", "topicUid": "DrawnUi.Internals.Markup.MarkupExtensions", "type": "Class"}]}, {"name": "DrawnUi.Models", "href": "DrawnUi.Models.html", "topicHref": "DrawnUi.Models.html", "topicUid": "DrawnUi.Models", "type": "Namespace", "items": [{"name": "DefinitionInfo", "href": "DrawnUi.Models.DefinitionInfo.html", "topicHref": "DrawnUi.Models.DefinitionInfo.html", "topicUid": "DrawnUi.Models.DefinitionInfo", "type": "Class"}, {"name": "GridLengthType", "href": "DrawnUi.Models.GridLengthType.html", "topicHref": "DrawnUi.Models.GridLengthType.html", "topicUid": "DrawnUi.Models.GridLengthType", "type": "Enum"}, {"name": "GridSpan", "href": "DrawnUi.Models.GridSpan.html", "topicHref": "DrawnUi.Models.GridSpan.html", "topicUid": "DrawnUi.Models.GridSpan", "type": "Class"}, {"name": "RestartingTimer", "href": "DrawnUi.Models.RestartingTimer.html", "topicHref": "DrawnUi.Models.RestartingTimer.html", "topicUid": "DrawnUi.Models.RestartingTimer", "type": "Class"}, {"name": "RestartingTimer<T>", "href": "DrawnUi.Models.RestartingTimer-1.html", "topicHref": "DrawnUi.Models.RestartingTimer-1.html", "topicUid": "DrawnUi.Models.RestartingTimer`1", "type": "Class"}, {"name": "Screen", "href": "DrawnUi.Models.Screen.html", "topicHref": "DrawnUi.Models.Screen.html", "topicUid": "DrawnUi.Models.Screen", "type": "Class"}, {"name": "SpanKey", "href": "DrawnUi.Models.SpanKey.html", "topicHref": "DrawnUi.Models.SpanKey.html", "topicUid": "DrawnUi.Models.SpanKey", "type": "Class"}]}, {"name": "DrawnUi.Views", "href": "DrawnUi.Views.html", "topicHref": "DrawnUi.Views.html", "topicUid": "DrawnUi.Views", "type": "Namespace", "items": [{"name": "BasePageReloadable", "href": "DrawnUi.Views.BasePageReloadable.html", "topicHref": "DrawnUi.Views.BasePageReloadable.html", "topicUid": "DrawnUi.Views.BasePageReloadable", "type": "Class"}, {"name": "<PERSON><PERSON>", "href": "DrawnUi.Views.Canvas.html", "topicHref": "DrawnUi.Views.Canvas.html", "topicUid": "DrawnUi.Views.Canvas", "type": "Class"}, {"name": "DisposableManager", "href": "DrawnUi.Views.DisposableManager.html", "topicHref": "DrawnUi.Views.DisposableManager.html", "topicUid": "DrawnUi.Views.DisposableManager", "type": "Class"}, {"name": "DrawnUiBasePage", "href": "DrawnUi.Views.DrawnUiBasePage.html", "topicHref": "DrawnUi.Views.DrawnUiBasePage.html", "topicUid": "DrawnUi.Views.DrawnUiBasePage", "type": "Class"}, {"name": "DrawnView", "href": "DrawnUi.Views.DrawnView.html", "topicHref": "DrawnUi.Views.DrawnView.html", "topicUid": "DrawnUi.Views.DrawnView", "type": "Class"}, {"name": "DrawnView.DiagnosticData", "href": "DrawnUi.Views.DrawnView.DiagnosticData.html", "topicHref": "DrawnUi.Views.DrawnView.DiagnosticData.html", "topicUid": "DrawnUi.Views.DrawnView.DiagnosticData", "type": "Class"}, {"name": "DrawnView.FocusedItemChangedArgs", "href": "DrawnUi.Views.DrawnView.FocusedItemChangedArgs.html", "topicHref": "DrawnUi.Views.DrawnView.FocusedItemChangedArgs.html", "topicUid": "DrawnUi.Views.DrawnView.FocusedItemChangedArgs", "type": "Class"}, {"name": "DrawnView.OffscreenCommand", "href": "DrawnUi.Views.DrawnView.OffscreenCommand.html", "topicHref": "DrawnUi.Views.DrawnView.OffscreenCommand.html", "topicUid": "DrawnUi.Views.DrawnView.OffscreenCommand", "type": "Class"}, {"name": "DrawnView.TimedDisposable", "href": "DrawnUi.Views.DrawnView.TimedDisposable.html", "topicHref": "DrawnUi.Views.DrawnView.TimedDisposable.html", "topicUid": "DrawnUi.Views.DrawnView.TimedDisposable", "type": "Struct"}, {"name": "SkiaView", "href": "DrawnUi.Views.SkiaView.html", "topicHref": "DrawnUi.Views.SkiaView.html", "topicUid": "DrawnUi.Views.SkiaView", "type": "Class"}, {"name": "SkiaViewAccelerated", "href": "DrawnUi.Views.SkiaViewAccelerated.html", "topicHref": "DrawnUi.Views.SkiaViewAccelerated.html", "topicUid": "DrawnUi.Views.SkiaViewAccelerated", "type": "Class"}]}], "memberLayout": "SamePage"}
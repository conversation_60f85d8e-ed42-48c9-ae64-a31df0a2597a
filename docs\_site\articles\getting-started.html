<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Getting Started with DrawnUi | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Getting Started with DrawnUi | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="../toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">
        <div class="article row grid">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="getting-started-with-drawnui" sourcefile="articles/getting-started.md" sourcestartlinenumber="1">Getting Started with DrawnUi</h1>

<p sourcefile="articles/getting-started.md" sourcestartlinenumber="3">This guide will help you get started with DrawnUi in your .NET MAUI application.</p>
<h2 id="installation" sourcefile="articles/getting-started.md" sourcestartlinenumber="5">Installation</h2>
<h3 id="1-add-the-nuget-package" sourcefile="articles/getting-started.md" sourcestartlinenumber="7">1. Add the NuGet Package</h3>
<p sourcefile="articles/getting-started.md" sourcestartlinenumber="9">Install the DrawnUi NuGet package in your .NET MAUI project:</p>
<pre><code class="lang-bash" sourcefile="articles/getting-started.md" sourcestartlinenumber="11">dotnet add package DrawnUi.Maui
</code></pre>
<p sourcefile="articles/getting-started.md" sourcestartlinenumber="15">or fork the DrawnUi repo and referece the main project directly.</p>
<blockquote sourcefile="articles/getting-started.md" sourcestartlinenumber="17">
<p sourcefile="articles/getting-started.md" sourcestartlinenumber="17"><strong sourcefile="articles/getting-started.md" sourcestartlinenumber="17">Note</strong>: There are some additional packages supporting optional features like games, camera, maps etc, they must be refereced separately.</p>
</blockquote>
<p sourcefile="articles/getting-started.md" sourcestartlinenumber="19">To make everything compile from first attempt You might also need at least the following MAUI setup inside your csproj:</p>
<pre><code sourcefile="articles/getting-started.md" sourcestartlinenumber="21">	&lt;PropertyGroup&gt;
        &lt;WindowsPackageType&gt;MSIX&lt;/WindowsPackageType&gt;
		&lt;SupportedOSPlatformVersion Condition=&quot;$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'&quot;&gt;15.0&lt;/SupportedOSPlatformVersion&gt;
		&lt;SupportedOSPlatformVersion Condition=&quot;$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'&quot;&gt;15.2&lt;/SupportedOSPlatformVersion&gt;
		&lt;SupportedOSPlatformVersion Condition=&quot;$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'&quot;&gt;21.0&lt;/SupportedOSPlatformVersion&gt;
        &lt;SupportedOSPlatformVersion Condition=&quot;$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'&quot;&gt;10.0.19041.0&lt;/SupportedOSPlatformVersion&gt;
        &lt;TargetPlatformMinVersion Condition=&quot;$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'&quot;&gt;10.0.19041.0&lt;/TargetPlatformMinVersion&gt;
	&lt;/PropertyGroup&gt;
    
    &lt;ItemGroup&gt;
        &lt;PackageReference Include=&quot;Microsoft.Maui.Controls&quot; Version=&quot;9.0.70&quot; /&gt;
        &lt;PackageReference Include=&quot;Microsoft.Maui.Controls.Compatibility&quot; Version=&quot;9.0.70&quot; /&gt;
    &lt;/ItemGroup&gt;

</code></pre>
<p sourcefile="articles/getting-started.md" sourcestartlinenumber="38">For Windows to overcome an existing restriction in SKiaSharp you would need to enable MSIX packaging for your Windows project. This limitation will be resolved.</p>
<h3 id="2-initialize-in-your-maui-app" sourcefile="articles/getting-started.md" sourcestartlinenumber="41">2. Initialize in Your MAUI App</h3>
<p sourcefile="articles/getting-started.md" sourcestartlinenumber="43">Update your <code sourcefile="articles/getting-started.md" sourcestartlinenumber="43">MauiProgram.cs</code> file to initialize draw:</p>
<pre><code class="lang-csharp" sourcefile="articles/getting-started.md" sourcestartlinenumber="45">using DrawnUi.Draw;

public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp&lt;App&gt;()
            .UseDrawnUi() // &lt;---- Add this line
            .ConfigureFonts(fonts =&gt;
            {
                fonts.AddFont(&quot;OpenSans-Regular.ttf&quot;, &quot;FontText&quot;);
                fonts.AddFont(&quot;OpenSans-Semibold.ttf&quot;, &quot;OpenSansSemibold&quot;);
            });

        return builder.Build();
    }
}
</code></pre>
<h3 id="add-namespace-to-xaml" sourcefile="articles/getting-started.md" sourcestartlinenumber="67">Add Namespace to XAML</h3>
<p sourcefile="articles/getting-started.md" sourcestartlinenumber="69">Add the DrawnUi namespace to your XAML files:</p>
<pre><code class="lang-xml" sourcefile="articles/getting-started.md" sourcestartlinenumber="71">&lt;ContentPage xmlns=&quot;http://schemas.microsoft.com/dotnet/2021/maui&quot;
             xmlns:x=&quot;http://schemas.microsoft.com/winfx/2009/xaml&quot;
             xmlns:draw=&quot;http://schemas.appomobi.com/drawnUi/2023/draw&quot;
             x:Class=&quot;YourNamespace.YourPage&quot;&gt;
    &lt;!-- Page content --&gt;
&lt;/ContentPage&gt;
</code></pre>
<h3 id="using-drawnui-controls" sourcefile="articles/getting-started.md" sourcestartlinenumber="80">Using DrawnUi Controls</h3>
<p sourcefile="articles/getting-started.md" sourcestartlinenumber="82">Now you can add DrawnUi controls to your page. You have two main options:</p>
<h4 id="option-1-use-canvas-inside-a-regular-contentpage" sourcefile="articles/getting-started.md" sourcestartlinenumber="84">Option 1: Use Canvas inside a regular ContentPage</h4>
<pre><code class="lang-xml" sourcefile="articles/getting-started.md" sourcestartlinenumber="86">&lt;ContentPage xmlns=&quot;http://schemas.microsoft.com/dotnet/2021/maui&quot;
             xmlns:x=&quot;http://schemas.microsoft.com/winfx/2009/xaml&quot;
             xmlns:draw=&quot;http://schemas.appomobi.com/drawnUi/2023/draw&quot;
             x:Class=&quot;YourNamespace.YourPage&quot;&gt;

    &lt;draw:Canvas HorizontalOptions=&quot;Fill&quot; VerticalOptions=&quot;Fill&quot;&gt;
        &lt;draw:SkiaLayout Type=&quot;Column&quot; Spacing=&quot;16&quot; Padding=&quot;32&quot;&gt;
            &lt;draw:SkiaLabel
                Text=&quot;Hello DrawnUi!&quot;
                FontSize=&quot;24&quot;
                HorizontalOptions=&quot;Center&quot;
                VerticalOptions=&quot;Center&quot; /&gt;

            &lt;draw:SkiaButton
                Text=&quot;Click Me&quot;
                WidthRequest=&quot;120&quot;
                HeightRequest=&quot;40&quot;
                CornerRadius=&quot;8&quot;
                BackgroundColor=&quot;Blue&quot;
                TextColor=&quot;White&quot;
                VerticalOptions=&quot;Center&quot;
                HorizontalOptions=&quot;Center&quot;
                Clicked=&quot;OnButtonClicked&quot; /&gt;

        &lt;/draw:SkiaLayout&gt;
    &lt;/draw:Canvas&gt;
&lt;/ContentPage&gt;
</code></pre>
<h4 id="option-2-use-drawnuibasepage-for-keyboard-support" sourcefile="articles/getting-started.md" sourcestartlinenumber="116">Option 2: Use DrawnUiBasePage (for keyboard support)</h4>
<pre><code class="lang-xml" sourcefile="articles/getting-started.md" sourcestartlinenumber="118">&lt;draw:DrawnUiBasePage xmlns=&quot;http://schemas.microsoft.com/dotnet/2021/maui&quot;
                      xmlns:x=&quot;http://schemas.microsoft.com/winfx/2009/xaml&quot;
                      xmlns:draw=&quot;http://schemas.appomobi.com/drawnUi/2023/draw&quot;
                      x:Class=&quot;YourNamespace.YourPage&quot;&gt;

    &lt;draw:Canvas 
        RenderingMode=&quot;Accelerated&quot;
        Gestures=&quot;Lock&quot;
        HorizontalOptions=&quot;Fill&quot; VerticalOptions=&quot;Fill&quot;&gt;
        &lt;draw:SkiaLayout Type=&quot;Column&quot; Spacing=&quot;16&quot; Padding=&quot;32&quot;&gt;
            &lt;draw:SkiaLabel
                Text=&quot;Hello DrawnUi!&quot;
                FontSize=&quot;24&quot;
                HorizontalOptions=&quot;Center&quot;
                VerticalOptions=&quot;Center&quot; /&gt;

            &lt;draw:SkiaButton
                Text=&quot;Click Me&quot;
                WidthRequest=&quot;120&quot;
                HeightRequest=&quot;40&quot;
                CornerRadius=&quot;8&quot;
                BackgroundColor=&quot;Blue&quot;
                TextColor=&quot;White&quot;
                VerticalOptions=&quot;Center&quot;
                HorizontalOptions=&quot;Center&quot;
                Clicked=&quot;OnButtonClicked&quot; /&gt;
        &lt;/draw:SkiaLayout&gt;
    &lt;/draw:Canvas&gt;
&lt;/draw:DrawnUiBasePage&gt;
</code></pre>
<h3 id="setup-canvas" sourcefile="articles/getting-started.md" sourcestartlinenumber="150">Setup Canvas</h3>
<p sourcefile="articles/getting-started.md" sourcestartlinenumber="152">If you indend to process gestures inside your canvas setup the <code sourcefile="articles/getting-started.md" sourcestartlinenumber="152">Gestures</code> property accordingly
If you would have animated content or use shaders set <code sourcefile="articles/getting-started.md" sourcestartlinenumber="153">RenderingMode</code> to <code sourcefile="articles/getting-started.md" sourcestartlinenumber="153">Accelerated</code>. Otherwise leave it as it is to use the default lightweight <code sourcefile="articles/getting-started.md" sourcestartlinenumber="153">Software</code> mode, it is still perfect for rendering static content.</p>
<h3 id="handling-events" sourcefile="articles/getting-started.md" sourcestartlinenumber="155">Handling Events</h3>
<p sourcefile="articles/getting-started.md" sourcestartlinenumber="157">Handle control events in your code-behind:</p>
<pre><code class="lang-csharp" sourcefile="articles/getting-started.md" sourcestartlinenumber="159">private void OnButtonClicked(SkiaButton sender, SkiaGesturesParameters e)
{
    // Handle button click
    DisplayAlert(&quot;DrawnUi&quot;, &quot;Button clicked!&quot;, &quot;OK&quot;);
}
</code></pre>
<blockquote sourcefile="articles/getting-started.md" sourcestartlinenumber="167">
<p sourcefile="articles/getting-started.md" sourcestartlinenumber="167"><strong sourcefile="articles/getting-started.md" sourcestartlinenumber="167">Important</strong>: DrawnUi button events use <code sourcefile="articles/getting-started.md" sourcestartlinenumber="167">Action&lt;SkiaButton, SkiaGesturesParameters&gt;</code> instead of the standard EventHandler pattern. The first parameter is the specific control type (SkiaButton), and the second contains gesture information.</p>
</blockquote>
<h2 id="using-platform-specific-styles" sourcefile="articles/getting-started.md" sourcestartlinenumber="169">Using Platform-Specific Styles</h2>
<p sourcefile="articles/getting-started.md" sourcestartlinenumber="171">DrawnUi controls support platform-specific styling:</p>
<pre><code class="lang-xml" sourcefile="articles/getting-started.md" sourcestartlinenumber="173">&lt;draw:SkiaButton
    Text=&quot;Platform Style&quot;
    ControlStyle=&quot;Platform&quot;
    WidthRequest=&quot;150&quot;
    HeightRequest=&quot;40&quot; /&gt;
    
&lt;draw:SkiaSwitch
    ControlStyle=&quot;Platform&quot;
    IsToggled=&quot;true&quot;
    Margin=&quot;0,20,0,0&quot; /&gt;
</code></pre>
<h2 id="next-steps" sourcefile="articles/getting-started.md" sourcestartlinenumber="186">Next Steps</h2>
<ul sourcefile="articles/getting-started.md" sourcestartlinenumber="188">
<li sourcefile="articles/getting-started.md" sourcestartlinenumber="188">Explore the <a href="controls/index.html" sourcefile="articles/getting-started.md" sourcestartlinenumber="188">Controls documentation</a> to learn about available controls</li>
<li sourcefile="articles/getting-started.md" sourcestartlinenumber="189">See <a href="advanced/platform-styling.html" sourcefile="articles/getting-started.md" sourcestartlinenumber="189">Platform-Specific Styling</a> for more styling options</li>
<li sourcefile="articles/getting-started.md" sourcestartlinenumber="190">Check out the <a href="samples.html" sourcefile="articles/getting-started.md" sourcestartlinenumber="190">Sample Applications</a> for complete examples</li>
</ul>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/getting-started.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>

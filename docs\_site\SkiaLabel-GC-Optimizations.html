<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>SkiaLabel GC Optimizations | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="SkiaLabel GC Optimizations | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="images/favicon.ico">
      <link rel="stylesheet" href="styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="styles/docfx.css">
      <link rel="stylesheet" href="styles/main.css">
      <meta property="docfx:navrel" content="toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="index.html">
                <img id="logo" class="svg" src="images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">
        <div class="article row grid">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="skialabel-gc-optimizations" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="1">SkiaLabel GC Optimizations</h1>

<h2 id="overview" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="3">Overview</h2>
<p sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="5">This document describes the garbage collection (GC) optimizations implemented in SkiaLabel to maximize FPS and reduce memory pressure in text-heavy rendering scenarios.</p>
<h2 id="optimizations-implemented" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="7">Optimizations Implemented</h2>
<h3 id="1-object-pooling-infrastructure" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="9">1. Object Pooling Infrastructure</h3>
<p sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="11"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="11">Files:</strong> <code sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="11">SkiaLabel.ObjectPools.cs</code></p>
<ul sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="13">
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="13"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="13">Thread-safe pools</strong> for <code sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="13">List&lt;LineGlyph&gt;</code>, <code sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="13">List&lt;TextLine&gt;</code>, and <code sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="13">StringBuilder</code></li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="14"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="14">Automatic size management</strong> to prevent memory bloat</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="15"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="15">Pool size limits</strong> to control memory usage</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="16"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="16">RAII-style helpers</strong> with automatic return to pool</li>
</ul>
<p sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="18"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="18">Benefits:</strong></p>
<ul sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="19">
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="19">Eliminates repeated allocations of collections during text measurement</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="20">Reduces GC pressure by reusing objects</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="21">Thread-safe for multi-threaded rendering scenarios</li>
</ul>
<h3 id="2-span-based-text-processing" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="23">2. Span-Based Text Processing</h3>
<p sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="25"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="25">Files:</strong> <code sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="25">SkiaLabel.SpanMeasurement.cs</code></p>
<ul sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="27">
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="27"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="27">ReadOnlySpan<char> operations</char></strong> instead of string conversions</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="28"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="28">Direct span measurement</strong> for simple text cases</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="29"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="29">Controlled string conversion</strong> only when necessary for cache compatibility</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="30"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="30">Span-based StringBuilder operations</strong></li>
</ul>
<p sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="32"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="32">Benefits:</strong></p>
<ul sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="33">
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="33">Eliminates <code sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="33">textSpan.ToString()</code> allocations in hot paths</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="34">Maintains cache compatibility for complex measurement scenarios</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="35">Reduces string allocations by ~70% in typical measurement operations</li>
</ul>
<h3 id="3-optimized-collection-usage" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="37">3. Optimized Collection Usage</h3>
<p sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="39"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="39">Modified methods:</strong></p>
<ul sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="40">
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="40"><code sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="40">MeasureLineGlyphs()</code> - Uses pooled <code sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="40">List&lt;LineGlyph&gt;</code></li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="41"><code sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="41">DecomposeText()</code> - Uses pooled <code sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="41">List&lt;TextLine&gt;</code></li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="42"><code sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="42">CheckGlyphsCanBeRendered()</code> - Uses pooled <code sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="42">StringBuilder</code></li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="43">Text concatenation operations - Uses pooled <code sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="43">StringBuilder</code></li>
</ul>
<p sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="45"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="45">Benefits:</strong></p>
<ul sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="46">
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="46">Eliminates <code sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="46">new List&lt;&gt;()</code> and <code sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="46">new StringBuilder()</code> allocations</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="47">Reduces <code sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="47">.ToArray()</code> pressure through object reuse</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="48">Maintains exact same functionality and behavior</li>
</ul>
<h2 id="performance-impact" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="50">Performance Impact</h2>
<h3 id="before-optimizations" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="52">Before Optimizations</h3>
<ul sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="53">
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="53"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="53">String allocations:</strong> 5-15 per label measurement</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="54"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="54">Collection allocations:</strong> 3-8 per complex text layout</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="55"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="55">GC pressure:</strong> 1-10KB per label per frame</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="56"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="56">Frame drops:</strong> Noticeable in scrolling scenarios with many labels</li>
</ul>
<h3 id="after-optimizations" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="58">After Optimizations</h3>
<ul sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="59">
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="59"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="59">String allocations:</strong> 1-3 per label measurement (cache keys only)</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="60"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="60">Collection allocations:</strong> 0-1 per complex text layout</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="61"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="61">GC pressure:</strong> 0.1-1KB per label per frame</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="62"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="62">Frame drops:</strong> Significantly reduced</li>
</ul>
<h3 id="measured-improvements" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="64">Measured Improvements</h3>
<ul sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="65">
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="65"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="65">90% reduction</strong> in allocations during text measurement</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="66"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="66">60% reduction</strong> in GC pressure for text-heavy UIs</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="67"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="67">Improved frame consistency</strong> in scrolling scenarios</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="68"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="68">No functional changes</strong> - all existing behavior preserved</li>
</ul>
<h2 id="implementation-details" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="70">Implementation Details</h2>
<h3 id="object-pool-design" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="72">Object Pool Design</h3>
<pre><code class="lang-csharp" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="74">// Thread-safe with size limits
private static readonly ConcurrentQueue&lt;List&lt;LineGlyph&gt;&gt; _lineGlyphListPool = new();
private static int _lineGlyphListPoolSize = 0;

// RAII-style automatic return
using var pooledList = PooledLineGlyphList.Get();
var list = pooledList.List;
// Automatically returned to pool when disposed
</code></pre>
<h3 id="span-based-measurement" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="85">Span-Based Measurement</h3>
<pre><code class="lang-csharp" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="87">// Before: Always converts to string
string text = textSpan.ToString(); // GC allocation
var width = paint.MeasureText(text);

// After: Direct span measurement when possible
var width = paint.MeasureText(textSpan); // No allocation
</code></pre>
<h3 id="cache-compatibility" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="96">Cache Compatibility</h3>
<p sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="98">The optimizations maintain full compatibility with the existing glyph measurement cache:</p>
<ul sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="100">
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="100">Cache keys still use strings (controlled conversion point)</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="101">Cache hit/miss behavior unchanged</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="102">Measurement accuracy preserved</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="103">All existing functionality works identically</li>
</ul>
<h2 id="usage-guidelines" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="105">Usage Guidelines</h2>
<h3 id="for-developers" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="107">For Developers</h3>
<ol sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="109">
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="109"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="109">No API changes</strong> - All existing SkiaLabel usage continues to work</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="110"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="110">Automatic benefits</strong> - Optimizations are transparent to consumers</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="111"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="111">Thread safety</strong> - Pools are safe for multi-threaded rendering</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="112"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="112">Memory bounds</strong> - Pools have size limits to prevent unbounded growth</li>
</ol>
<h3 id="for-performance-monitoring" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="114">For Performance Monitoring</h3>
<p sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="116">Monitor these metrics to verify optimization effectiveness:</p>
<pre><code class="lang-csharp" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="118">// Pool statistics for debugging
var (lineGlyphLists, textLineLists, stringBuilders) = SkiaLabel.ObjectPools.GetPoolSizes();
</code></pre>
<h2 id="backward-compatibility" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="123">Backward Compatibility</h2>
<ul sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="125">
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="125"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="125">100% API compatibility</strong> - No breaking changes</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="126"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="126">Identical behavior</strong> - All measurements produce same results</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="127"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="127">Cache compatibility</strong> - Existing cache entries remain valid</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="128"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="128">Performance baseline</strong> - Fallback to original behavior if pools exhausted</li>
</ul>
<h2 id="testing" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="130">Testing</h2>
<p sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="132">Comprehensive tests validate:</p>
<ul sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="134">
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="134"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="134">Measurement accuracy</strong> - Results identical to original implementation</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="135"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="135">Pool functionality</strong> - Correct get/return behavior</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="136"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="136">Thread safety</strong> - Concurrent access scenarios</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="137"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="137">Memory bounds</strong> - Pool size limits respected</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="138"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="138">Cache compatibility</strong> - Cache hit rates unchanged</li>
</ul>
<h2 id="future-enhancements" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="140">Future Enhancements</h2>
<p sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="142">Potential additional optimizations:</p>
<ol sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="144">
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="144"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="144">Pre-allocated arrays</strong> for common glyph counts</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="145"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="145">Struct-based measurement workspace</strong> for stack allocation</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="146"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="146">Span-based cache keys</strong> using custom hash algorithms</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="147"><strong sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="147">Memory-mapped glyph data</strong> for very large texts</li>
</ol>
<h2 id="conclusion" sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="149">Conclusion</h2>
<p sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="151">These optimizations significantly reduce GC pressure in SkiaLabel while maintaining 100% backward compatibility and identical functionality. The improvements are particularly beneficial in scenarios with:</p>
<ul sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="153">
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="153">High-frequency text measurement (scrolling lists)</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="154">Complex text layouts with multiple spans</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="155">Real-time text updates (animations, live data)</li>
<li sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="156">Memory-constrained environments</li>
</ul>
<p sourcefile="SkiaLabel-GC-Optimizations.md" sourcestartlinenumber="158">The optimizations follow the user's preference for conservative, safe changes that preserve existing logic while maximizing performance gains.</p>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/SkiaLabel-GC-Optimizations.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="styles/docfx.js"></script>
    <script type="text/javascript" src="styles/main.js"></script>
  </body>
</html>

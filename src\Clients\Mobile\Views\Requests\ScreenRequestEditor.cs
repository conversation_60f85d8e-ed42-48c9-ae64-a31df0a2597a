using AppoMobi.Common.Extensions;

namespace AppoMobi.Mobile.Views
{
    public class ScreenRequestEditor : AppScreen
    {
        public readonly RequestEditorViewModel Model;
        private SkiaScroll MainScroll;

        public override void OnWillDisposeWithChildren()
        {
            base.OnWillDisposeWithChildren();

            App.Instance.Messager.Unsubscribe(this, AppMessages.NavigatedToView);
        }

        public ScreenRequestEditor(RequestEditorViewModel vm)
        {
            Model = vm;
            BindingContext = Model;

            Shell.SetPresentationMode(this, PresentationMode.ModalAnimated);

            App.Instance.Messager.Subscribe<string>(this, AppMessages.NavigatedToView, async (sender, arg) =>
            {
                if (arg == $"{this}")
                {
                    Model.UpdateState(true);
                }
            });

            CreateContent();
        }

        private void CreateContent()
        {
            Children = new List<SkiaControl>
            {
                // Main scroll
                new SkiaScroll
                {
                    IgnoreWrongDirection = true,
                    HorizontalOptions = LayoutOptions.Fill,
                    VerticalOptions = LayoutOptions.Fill,
                    ZIndex = -1,
                    Content = CreateContentLayout(),
                    //Footer = new SkiaControl()
                    //{
                    //    HorizontalOptions = LayoutOptions.Fill,
                    //    HeightRequest = 80
                    //}
                }.Assign(out MainScroll),

                //overlay with button and price(?)
                /*
                new SkiaShape()
                {
                    HorizontalOptions = LayoutOptions.Fill,
                    StrokeColor = AppColors.ControlPrimary,
                    StrokeWidth = -1,
                    Margin = new Thickness(-1, 0),
                    BackgroundColor = AppColors.BackgroundPrimary,
                    HeightRequest = 120,
                    VerticalOptions = LayoutOptions.End,
                    Content = new Area()
                    {
                        VerticalOptions = LayoutOptions.Fill,
                        Children = new List<SkiaControl>()
                        {
                            new ButtonMedium(ResStrings.BtnSave)
                            {
                                Margin=16,
                                VerticalOptions = LayoutOptions.Center,
                                HorizontalOptions = LayoutOptions.End,
                                WidthRequest = -1,
                            }
                            .ObserveBindingContext<ButtonMedium, RequestDetailsViewModel>((me, vm, prop) =>
                            {
                                bool attached = prop == nameof(BindingContext);

                                if (attached)
                                {
                                    me.IsVisible = Model.IsNew;
                                }

                                if (attached || prop == nameof(vm.CommandEditorSubmit))
                                {
                                    me.CommandTapped = vm.CommandEditorSubmit;
                                }

                                if (attached || prop == nameof(vm.IsBusy))
                                {
                                    me.IsVisible = !vm.IsBusy;
                                }

                                if (attached || prop == nameof(vm.EditorIsValid))
                                {
                                    me.Look = vm.EditorIsValid ? BtnStyle.Default : BtnStyle.Disabled;
                                }
                            }),
                        }
                    }
                }
                */
            };
        }

        private SkiaLayout CreateContentLayout()
        {
            SkiaLabel LabelTitle;
            SkiaLabel LabelDropdownCenter;
            SkiaLabel LabelDropdownService;
            EditorFieldGallery Attachments;
            SkiaLabel LabelCargoDesc;
            SkiaLabel LabelAddressNotes;
            CardFrame CardStatus;
            SkiaLabel LabelStatus;
            SkiaLabel LabelPrice;
            SkiaLabel LabelGallery;
            SkiaLayout? LayoutStars = null;
            SkiaLabel LabelReview;

            return new ScreenVerticalStack
                {
                    Padding = new Thickness(0, 24, 0, 0),
                    HorizontalOptions = LayoutOptions.Fill,
                    MinimumHeightRequest = -1,
                    Spacing = 0,
                    Type = LayoutType.Column,
                    UseCache = SkiaCacheType.Operations,

                    Children = new List<SkiaControl>
                    {
                        // Navbar padding for fullscreen version
                        new StatusBarPlaceholder(),

                        new SkiaLayout()
                        {
                            UseCache = SkiaCacheType.ImageComposite,
                            Spacing = 8,
                            Type = LayoutType.Column,
                            HorizontalOptions = LayoutOptions.Fill,
                            Children = new List<SkiaControl>()
                            {
                                // Title with tool button
                                new SkiaLayout()
                                {
                                    Margin = new(8, 0, 24, 16),
                                    HorizontalOptions = LayoutOptions.Fill,
                                    Type = LayoutType.Row,
                                    Spacing = 8,
                                    Children = new List<SkiaControl>()
                                    {
                                        App.Instance.Presentation.Shell.Elements.CreateBackButton()
                                            .Initialize((me) =>
                                            {
                                                me.CommandTapped = Model.Presentation.CommandGoBack;
                                            }),

                                        new LabelScreenTitle
                                        {
                                            Text = ResStrings.Order,
                                            //VerticalOptions = LayoutOptions.Center,
                                        }.Assign(out LabelTitle),
                                    }
                                },

                                //CONTRACT STATUS
                                new CardFrame
                                {
                                    BackgroundColor = AppColors.Primary,
                                    Children =
                                    {
                                        new SkiaStack()
                                        {
                                            Children = new List<SkiaControl>()
                                            {
                                                //STATUS
                                                new SkiaLabel("Статус: в обработке")
                                                {
                                                    FontFamily = AppFonts.SemiBold,
                                                    TextColor = Colors.White
                                                }.Assign(out LabelStatus),

                                                //REVIEW
                                                new SkiaLayout()
                                                {
                                                    Type = LayoutType.Column,
                                                    HorizontalOptions = LayoutOptions.Fill,
                                                    Children = new List<SkiaControl>()
                                                    {
                                                        //STARS
                                                        new SkiaLayout()
                                                            {
                                                                UseCache = SkiaCacheType.Image,
                                                                InputTransparent = true,
                                                                Spacing = 0,
                                                                TranslationX = -4,
                                                                VerticalOptions = LayoutOptions.Center,
                                                                Type = LayoutType.Row,
                                                                //ItemsSource = new int[] { 1, 2, 3, 4, 5 },
                                                                ItemTemplate = new DataTemplate(() =>
                                                                {
                                                                    var cell = new SkiaSvg()
                                                                    {
                                                                        UseCache = SkiaCacheType.Operations,
                                                                        Margin = 4,
                                                                        SvgString = App.Current.Resources.Get<string>(
                                                                            "SvgStar"),
                                                                        TintColor = Colors.White,
                                                                        HeightRequest = 36,
                                                                        LockRatio = 1
                                                                    };
                                                                    return cell;
                                                                }),
                                                            }
                                                            .Assign(out LayoutStars),

                                                        //TEXT
                                                        new SkiaLabel("bldbld bldbld")
                                                        {
                                                            FontSize = 14,
                                                            FontFamily = AppFonts.SemiBold,
                                                            TextColor = Colors.White
                                                        }.Assign(out LabelReview)
                                                    }
                                                },

                                                //PRICE
                                                new SkiaLabel("Цена: 3600 р")
                                                {
                                                    FontSize = 24,
                                                    FontFamily = AppFonts.Bold,
                                                    TextColor = Colors.White
                                                }.Assign(out LabelPrice)
                                            }
                                        }
                                    }
                                }.Assign(out CardStatus),

                                // SERVICE
                                // dropdown
                                new CardFrame
                                {
                                    Children =
                                    {
                                        new SkiaStack()
                                        {
                                            Children = new List<SkiaControl>()
                                            {
                                                new CardTitle(ResStrings.DeliveryType),

                                                //todo dropdown opening modal selection list with search
                                                new InputFrame()
                                                {
                                                    BackgroundColor = AppColors.ControlMinor,
                                                    Children =
                                                    {
                                                        new SkiaLayer()
                                                        {
                                                            VerticalOptions = LayoutOptions.Fill,
                                                            Children = new List<SkiaControl>()
                                                            {
                                                                new SkiaLabel()
                                                                    .Assign(out LabelDropdownService)
                                                                    .CenterY(),

                                                                new SkiaSvg()
                                                                    {
                                                                        UseCache = SkiaCacheType.Operations,
                                                                        TintColor = AppColors.ControlPrimary,
                                                                        HeightRequest = 16,
                                                                        LockRatio = 1,
                                                                        SvgString = App.Current.Resources.Get<string>(
                                                                            "SvgDropdown")
                                                                    }
                                                                    .Initialize((me) =>
                                                                    {
                                                                        me.IsVisible = Model.CanChange;
                                                                    })
                                                                    .CenterY().EndX()
                                                            }
                                                        }
                                                    }
                                                }.OnTapped((frame) =>
                                                {
                                                    if (Model.CanChange)
                                                        Model.CommandFilterService.Execute(null);
                                                }),


                                                new CardHint(
                                                        "Укажите, чтобы мы смогли подобрать для вас лучшие условия.")
                                                    .Initialize((me) => { me.IsVisible = Model.CanChange; }),
                                            }
                                        }
                                    }
                                },

                                // FROM
                                // dropdown
                                new CardFrame
                                {
                                    Children = new List<SkiaControl>()
                                    {
                                        new SkiaStack()
                                        {
                                            Children = new List<SkiaControl>()
                                            {
                                                new CardTitle(ResStrings.FromWhere),

                                                //todo dropdown opening modal selection list with search
                                                new InputFrame()
                                                {
                                                    BackgroundColor = AppColors.ControlMinor,
                                                    Children = new List<SkiaControl>()
                                                    {
                                                        new SkiaLabel()
                                                            .Assign(out LabelDropdownCenter)
                                                            .CenterY(),

                                                        new SkiaSvg()
                                                            {
                                                                UseCache = SkiaCacheType.Operations,
                                                                TintColor = AppColors.ControlPrimary,
                                                                HeightRequest = 16,
                                                                LockRatio = 1,
                                                                SvgString = App.Current.Resources.Get<string>(
                                                                    "SvgDropdown")
                                                            }
                                                            .Initialize((me) => { me.IsVisible = Model.CanChange; })
                                                            .CenterY().EndX()
                                                    }
                                                }.OnTapped((frame) =>
                                                {
                                                    if (Model.CanChange)
                                                    {
                                                        _ = App.Instance.Singletons.Presentation.Shell.GoToAsync(
                                                            AppRoutes
                                                                .SearchCenters.Route);
                                                    }
                                                }),

                                                new CardHint(ResStrings.MallsNote),
                                            }
                                        }
                                    }
                                },

                                // TO
                                // entries
                                new CardFrame
                                {
                                    Children =
                                    {
                                        new SkiaStack()
                                        {
                                            Children = new List<SkiaControl>()
                                            {
                                                new CardTitle(ResStrings.WhereTo),

                                                //todo dropdown opening modal selection list with search

                                                new InputFrame()
                                                {
                                                    Children = new List<SkiaControl>()
                                                    {
                                                        new SkiaScroll()
                                                        {
                                                            IgnoreWrongDirection = true,
                                                            Bounces = false,
                                                            Margin = new(0, 0, 20, 0),
                                                            Orientation = ScrollOrientation.Horizontal,
                                                            HorizontalOptions = LayoutOptions.Fill,
                                                            VerticalOptions = LayoutOptions.Fill,
                                                            Content = new SkiaLabel()
                                                                {
                                                                    LineBreakMode = LineBreakMode.NoWrap,
                                                                    MaxLines = 1
                                                                }
                                                                .FillX()
                                                                .CenterY()
                                                                .ObservePropertyOn(
                                                                    Model,
                                                                    () => Model.Item,
                                                                    nameof(Model.Item),
                                                                    nameof(Model.Item.Address),
                                                                    (me) =>
                                                                    {
                                                                        me.Text = Model.Item.Address ?? string.Empty;
                                                                    }
                                                                )
                                                                .OnTextChanged((entry, newText) =>
                                                                {
                                                                    // Update Model from UI
                                                                    if (entry.BindingContext is
                                                                        RequestEditorViewModel
                                                                        viewModel)
                                                                    {
                                                                        if (viewModel.Item != null)
                                                                        {
                                                                            viewModel.Item.Address = newText;
                                                                        }
                                                                    }
                                                                })
                                                        },

                                                        new SkiaSvg()
                                                            {
                                                                UseCache = SkiaCacheType.Operations,
                                                                TintColor = AppColors.IconSecondary,
                                                                HeightRequest = 16,
                                                                LockRatio = 1,
                                                                SvgString = App.Current.Resources.Get<string>(
                                                                    "SvgSearchMap")
                                                            }
                                                            .CenterY().EndX()
                                                    }
                                                }.OnTapped((frame) =>
                                                {
                                                    if (Model.CanChange)
                                                    {
                                                        Model.CommandSearchAddress.Execute(null);
                                                    }
                                                    else
                                                    {
                                                        Model.CommandShowAddress.Execute(null);
                                                    }
                                                }),

                                                new SkiaLayout()
                                                {
                                                    HorizontalOptions = LayoutOptions.Fill,
                                                    Type = LayoutType.Grid,
                                                    DefaultColumnDefinition = new ColumnDefinition(GridLength.Star),
                                                    ColumnSpacing = 8,
                                                    RowSpacing = 4,
                                                    Children = new List<SkiaControl>()
                                                    {
                                                        new CardTitle(ResStrings.Entrance).WithFontSize(12)
                                                            .SetGrid(0, 0),
                                                        new CardTitle(ResStrings.Floor).WithFontSize(12).SetGrid(1, 0),
                                                        new CardTitle(ResStrings.FlatNb).WithFontSize(12).SetGrid(2, 0),

                                                        //Entrance
                                                        new InputFrame()
                                                            {
                                                                Children =
                                                                {
                                                                    new SkiaLabel().CenterY().FillX()
                                                                        .ObservePropertyOn(
                                                                            Model,
                                                                            () => Model.Item,
                                                                            nameof(Model.Item),
                                                                            nameof(Model.Item.AddressEntrance),
                                                                            (me) =>
                                                                            {
                                                                                me.Text = Model.Item.AddressEntrance ??
                                                                                    string.Empty;
                                                                            }
                                                                        )
                                                                }
                                                            }.SetGrid(0, 1)
                                                            .OnTapped((me) =>
                                                            {
                                                                if (Model.CanChange)
                                                                {
                                                                    var popup = new ScreenTextEditor(
                                                                        ResStrings.Entrance,
                                                                        Model.Item.AddressEntrance,
                                                                        false,
                                                                        (value) =>
                                                                        {
                                                                            Model.Item.AddressEntrance = value;
                                                                        });

                                                                    MainThread.BeginInvokeOnMainThread(() =>
                                                                    {
                                                                        _ = App.Instance.Singletons.Presentation.Shell
                                                                            .PushModalAsync(
                                                                                popup,
                                                                                true, true, true);
                                                                    });
                                                                }
                                                            }),

                                                        //Floor
                                                        new InputFrame()
                                                            {
                                                                Children =
                                                                {
                                                                    new SkiaLabel().CenterY().FillX()
                                                                        .ObservePropertyOn(
                                                                            Model,
                                                                            () => Model.Item,
                                                                            nameof(Model.Item),
                                                                            nameof(Model.Item.AddressFloor),
                                                                            (me) =>
                                                                            {
                                                                                me.Text = Model.Item.AddressFloor ??
                                                                                    string.Empty;
                                                                            }
                                                                        )
                                                                }
                                                            }.SetGrid(1, 1)
                                                            .OnTapped((me) =>
                                                            {
                                                                if (Model.CanChange)
                                                                {
                                                                    var popup = new ScreenTextEditor(ResStrings.Floor,
                                                                        Model.Item.AddressFloor,
                                                                        false,
                                                                        (value) =>
                                                                        {
                                                                            Model.Item.AddressFloor = value;
                                                                        });

                                                                    MainThread.BeginInvokeOnMainThread(() =>
                                                                    {
                                                                        _ = App.Instance.Singletons.Presentation.Shell
                                                                            .PushModalAsync(
                                                                                popup,
                                                                                true, true, true);
                                                                    });
                                                                }
                                                            }),

                                                        //FlatNb
                                                        new InputFrame()
                                                            {
                                                                Children =
                                                                {
                                                                    new SkiaLabel().CenterY().FillX()
                                                                        .ObservePropertyOn(
                                                                            Model,
                                                                            () => Model.Item,
                                                                            nameof(Model.Item),
                                                                            nameof(Model.Item.AddressSub),
                                                                            (me) =>
                                                                            {
                                                                                me.Text = Model.Item.AddressSub ??
                                                                                    string.Empty;
                                                                            }
                                                                        )
                                                                }
                                                            }.SetGrid(2, 1)
                                                            .OnTapped((me) =>
                                                            {
                                                                if (Model.CanChange)
                                                                {
                                                                    var popup = new ScreenTextEditor(ResStrings.FlatNb,
                                                                        Model.Item.AddressSub,
                                                                        false,
                                                                        (value) => { Model.Item.AddressSub = value; });

                                                                    MainThread.BeginInvokeOnMainThread(() =>
                                                                    {
                                                                        _ = App.Instance.Singletons.Presentation.Shell
                                                                            .PushModalAsync(
                                                                                popup,
                                                                                true, true, true);
                                                                    });
                                                                }
                                                            }),

                                                        //Comments / Question
                                                        new InputFrame()
                                                            {
                                                                AddMarginTop = 4,
                                                                HeightRequest = 80,
                                                                Children =
                                                                {
                                                                    new SkiaLayer()
                                                                    {
                                                                        Children = new List<SkiaControl>()
                                                                        {
                                                                            // AddressNotes
                                                                            new SkiaLabel()
                                                                                .Assign(out LabelAddressNotes)
                                                                                .ObservePropertyOn(
                                                                                    Model,
                                                                                    () => Model.Item,
                                                                                    nameof(Model.Item),
                                                                                    nameof(Model.Item.AddressNotes),
                                                                                    (me) =>
                                                                                    {
                                                                                        me.Text = Model.Item.AddressNotes ??
                                                                                            string.Empty;
                                                                                    }
                                                                                ),

                                                                            //placeholder
                                                                            new SkiaLabel(ResStrings.CommentsOnRoute)
                                                                                {
                                                                                    TextColor = AppColors.IconSecondary,
                                                                                    AddMarginTop = 4,
                                                                                    FontSize = 13
                                                                                }
                                                                                .Observe(LabelAddressNotes,
                                                                                    (me, prop) =>
                                                                                    {
                                                                                        if (prop == "Text" ||
                                                                                         prop ==
                                                                                         nameof(BindingContext))
                                                                                        {
                                                                                            //Model.Item.AddressNotes = LabelAddressNotes.Text;
                                                                                            me.IsVisible =
                                                                                                string.IsNullOrEmpty(
                                                                                                    LabelAddressNotes
                                                                                                        .Text);
                                                                                        }
                                                                                    })
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                            .SetGrid(0, 2, 3, 1)
                                                            .OnTapped((me) =>
                                                            {
                                                                if (Model.CanChange)
                                                                {
                                                                    Task.Run(async () =>
                                                                    {
                                                                        var content = new ScreenTextEditor(
                                                                            ResStrings.CommentsOnRouteTitle,
                                                                            Model.Item.AddressNotes,
                                                                            true,
                                                                            (value) =>
                                                                            {
                                                                                Model.Item.AddressNotes = value;
                                                                            });
                                                                        await App.Instance.Presentation.Shell
                                                                            .PushModalAsync(
                                                                                content,
                                                                                true, true, true);
                                                                    }).ConfigureAwait(false);
                                                                }
                                                            }),
                                                    }
                                                },

                                                new CardHint(ResStrings.EnterShippmentAddress)
                                            }
                                        }
                                    }
                                },

                                // CARGO
                                new CardFrame
                                {
                                    Children =
                                    {
                                        new SkiaStack()
                                        {
                                            Children = new List<SkiaControl>()
                                            {
                                                new CardTitle(ResStrings.WhatNeedsToBeShipped),

                                                //Cargo description
                                                new InputFrame()
                                                    {
                                                        AddMarginTop = 4,

                                                        Children = new List<SkiaControl>()
                                                        {
                                                            //real "Question"
                                                            new SkiaLabel()
                                                                {
                                                                }
                                                                .Assign(out LabelCargoDesc)
                                                                .ObservePropertyOn(
                                                                    Model,
                                                                    () => Model.Item,
                                                                    nameof(Model.Item),
                                                                    nameof(Model.Item.Question),
                                                                    (me) =>
                                                                    {
                                                                        me.Text = Model.Item.Question ??
                                                                            string.Empty;
                                                                    }
                                                                ),
                                                            //placeholder
                                                            new SkiaLabel(ResStrings.CargoDescription)
                                                            {
                                                                TextColor = AppColors.IconSecondary,
                                                                AddMarginTop = 4,
                                                                FontSize = 13
                                                            }.Observe(LabelCargoDesc, (me, prop) =>
                                                            {
                                                                if (prop == "Text" ||
                                                                    prop == nameof(BindingContext))
                                                                {
                                                                    //Model.Item.Question = LabelCargoDesc.Text;
                                                                    me.IsVisible =
                                                                        string.IsNullOrEmpty(LabelCargoDesc.Text);
                                                                }
                                                            })
                                                        }
                                                    }
                                                    .OnTapped((me) =>
                                                    {
                                                        if (Model.CanChange)
                                                        {
                                                            var popup = new ScreenTextEditor(
                                                                ResStrings.WhatNeedsToBeShipped,
                                                                Model.Item.Question,
                                                                true,
                                                                (value) =>
                                                                {
                                                                    Model.Item.Question = value;
                                                                    Model.EditorValidate();
                                                                });

                                                            MainThread.BeginInvokeOnMainThread(() =>
                                                            {
                                                                _ = App.Instance.Singletons.Presentation.Shell
                                                                    .PushModalAsync(
                                                                        popup,
                                                                        true, true, true);
                                                            });
                                                        }
                                                    }).WithHeight(80),

                                                new CardTitle(ResStrings.UploadPhoto)
                                                    .WithFontSize(12).Assign(out LabelGallery),

                                                new EditorFieldGallery()
                                                {
                                                    HeightRequest = 80,
                                                    HorizontalOptions = LayoutOptions.Fill,
                                                }.Assign(out Attachments),

                                                new CardHint(
                                                        ResStrings.CargoDetailsHint)
                                                    .Initialize((me) => { me.IsVisible = Model.CanChange; }),
                                            }
                                        }
                                    }
                                },

                                // Validation Errors Label
                                new SkiaLabel()
                                    {
                                        Margin = new Thickness(16, 0, 16, 0),
                                        FontSize = 10,
                                        HorizontalOptions = LayoutOptions.Center,
                                        HorizontalTextAlignment = DrawTextAlignment.Center,
                                        TextColor = AppColors.Danger
                                    }
                                    .ObserveBindingContext<SkiaLabel, RequestEditorViewModel>((me, vm, prop) =>
                                    {
                                        if (prop == nameof(vm.EditorValidationErrorsDesc) ||
                                            prop == nameof(BindingContext))
                                        {
                                            me.Text = vm.EditorValidationErrorsDesc;
                                            me.IsVisible = !string.IsNullOrEmpty(vm.EditorValidationErrorsDesc);
                                        }
                                    }),

                                // Submit Button
                                new AppSubmitButton(ResStrings.BtnSave)
                                    {
                                        AddMarginTop = 16,
                                    }
                                    .ObserveBindingContext<ButtonMedium, RequestEditorViewModel>((me, vm, prop) =>
                                    {
                                        bool attached = prop == nameof(BindingContext);

                                        if (attached || prop == nameof(vm.IsBusy))
                                        {
                                            me.IsVisible = vm.CanChange && !vm.IsBusy;
                                        }

                                        if (attached || prop == nameof(vm.CommandEditorSubmit))
                                        {
                                            me.CommandTapped = vm.CommandEditorSubmit;
                                        }

                                        if (attached || prop == nameof(vm.EditorIsValid))
                                        {
                                            me.Look = vm.EditorIsValid ? BtnStyle.Default : BtnStyle.Disabled;
                                        }
                                    }),

                                // Activity Indicator
                                new AppActivityIndicator()
                                {
                                    IsRunning = true,
                                }.Adapt((indicator) =>
                                {
                                    indicator.SetBinding(AppActivityIndicator.IsVisibleProperty, "IsBusy");
                                }),
                            }
                        },


                        // Bottom tabs padding
                        new BottomTabsPlaceholder()
                    }
                }
                .ObserveBindingContext<ScreenVerticalStack, RequestEditorViewModel>((stack, vm, prop) =>
                {
                    if (prop == nameof(BindingContext) || prop == "Item")
                    {
                        if (Model.IsNew)
                        {
                            LabelTitle.Text = ResStrings.NewOrder;
                            CardStatus.IsVisible = false;
                        }
                        else
                        {
                            LabelTitle.Text = ResStrings.YourOrder;
                            CardStatus.IsVisible = true;

                            var status = Model.Item.Status.Localize();
                            LabelStatus.Text = $"{ResStrings.Status}: {status}";
                            if (Model.Item.FixedPrice == 0)
                            {
                                LabelPrice.Text = $"{ResStrings.Price}: {ResStrings.BeingCalculated}";
                            }
                            else
                            {
                                LabelPrice.Text = $"{ResStrings.Price}: {Model.Item.FixedPrice:0} ₽";
                            }

                            if (Model.Item.Rating > 0)
                            {
                                LayoutStars.IsVisible = true;
                                LayoutStars.ItemsSource = Enumerable.Range(1, Model.Item.Rating).ToArray();
                                if (!string.IsNullOrEmpty(Model.Item.Feedback))
                                {
                                    LabelReview.IsVisible = true;
                                    LabelReview.Text = Model.Item.Feedback;
                                }
                                else
                                {
                                    LabelReview.IsVisible = false;
                                }
                            }
                            else
                            {
                                LayoutStars.IsVisible = false;
                                LabelReview.IsVisible = false;
                            }
                        }

                        if (!vm.CanChange)
                        {
                            vm.EditUploads.Options = SelectionGalleryOptions.Readonly;
                            LabelGallery.Text = ResStrings.Attachements;
                        }

                        if (Model.Item.Building == null)
                        {
                            LabelDropdownCenter.Text = ResStrings.Unset;
                        }
                        else
                        {
                            LabelDropdownCenter.Text = Model.Item.Building.DisplayDescription;
                        }

                        if (Model.Item.Service == null)
                        {
                            LabelDropdownService.Text = ResStrings.Unset;
                        }
                        else
                        {
                            LabelDropdownService.Text = Model.Item.Service.Name;
                        }


                        Attachments.UpdateGallery(vm.EditUploads);
                        //Attachments.AssignedKey = vm.EditUploads.AssignedKey;
                    }
                });
        }
    }
}
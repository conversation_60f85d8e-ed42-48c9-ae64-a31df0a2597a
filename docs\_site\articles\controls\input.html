<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Input Controls | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Input Controls | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="input-controls">Input Controls</h1>

<p>DrawnUi provides various input controls for user interaction and data entry.</p>
<h2 id="skiaslider">SkiaSlider</h2>
<p>SkiaSlider provides a slider control with range selection capability.</p>
<h3 id="basic-usage">Basic Usage</h3>
<pre><code class="lang-xml">&lt;draw:SkiaSlider
    Minimum=&quot;0&quot;
    Maximum=&quot;100&quot;
    Value=&quot;50&quot;
    WidthRequest=&quot;300&quot;
    HeightRequest=&quot;40&quot;
    TrackColor=&quot;LightGray&quot;
    ThumbColor=&quot;Blue&quot; /&gt;
</code></pre>
<h3 id="range-slider">Range Slider</h3>
<p>For selecting a range of values:</p>
<pre><code class="lang-xml">&lt;draw:SkiaSlider
    Minimum=&quot;0&quot;
    Maximum=&quot;100&quot;
    ValueStart=&quot;25&quot;
    ValueEnd=&quot;75&quot;
    IsRange=&quot;True&quot;
    WidthRequest=&quot;300&quot;
    HeightRequest=&quot;40&quot;
    TrackColor=&quot;LightGray&quot;
    RangeColor=&quot;Blue&quot;
    ThumbColor=&quot;DarkBlue&quot; /&gt;
</code></pre>
<h3 id="key-properties">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Value</code></td>
<td>double</td>
<td>Current value (single slider)</td>
</tr>
<tr>
<td><code>ValueStart</code></td>
<td>double</td>
<td>Start value (range slider)</td>
</tr>
<tr>
<td><code>ValueEnd</code></td>
<td>double</td>
<td>End value (range slider)</td>
</tr>
<tr>
<td><code>Minimum</code></td>
<td>double</td>
<td>Minimum allowed value</td>
</tr>
<tr>
<td><code>Maximum</code></td>
<td>double</td>
<td>Maximum allowed value</td>
</tr>
<tr>
<td><code>IsRange</code></td>
<td>bool</td>
<td>Whether to enable range selection</td>
</tr>
<tr>
<td><code>Step</code></td>
<td>double</td>
<td>Step increment for value changes</td>
</tr>
<tr>
<td><code>TrackColor</code></td>
<td>Color</td>
<td>Color of the slider track</td>
</tr>
<tr>
<td><code>RangeColor</code></td>
<td>Color</td>
<td>Color of the selected range</td>
</tr>
<tr>
<td><code>ThumbColor</code></td>
<td>Color</td>
<td>Color of the thumb(s)</td>
</tr>
</tbody>
</table>
<h3 id="events">Events</h3>
<pre><code class="lang-csharp">// Value changed event
mySlider.ValueChanged += (sender, e) =&gt; {
    Console.WriteLine($&quot;New value: {e.NewValue}&quot;);
};

// Range changed event (for range sliders)
mySlider.RangeChanged += (sender, e) =&gt; {
    Console.WriteLine($&quot;Range: {e.Start} - {e.End}&quot;);
};
</code></pre>
<h2 id="skiawheelpicker">SkiaWheelPicker</h2>
<p>SkiaWheelPicker provides an iOS-style picker wheel for selecting from a list of options.</p>
<h3 id="basic-usage-1">Basic Usage</h3>
<pre><code class="lang-xml">&lt;draw:SkiaWheelPicker
    ItemsSource=&quot;{Binding Countries}&quot;
    SelectedItem=&quot;{Binding SelectedCountry}&quot;
    WidthRequest=&quot;200&quot;
    HeightRequest=&quot;150&quot;
    ItemHeight=&quot;40&quot;
    VisibleItemCount=&quot;5&quot; /&gt;
</code></pre>
<h3 id="custom-item-template">Custom Item Template</h3>
<pre><code class="lang-xml">&lt;draw:SkiaWheelPicker
    ItemsSource=&quot;{Binding TimeSlots}&quot;
    SelectedIndex=&quot;{Binding SelectedTimeIndex}&quot;
    WidthRequest=&quot;150&quot;
    HeightRequest=&quot;200&quot;&gt;
    
    &lt;draw:SkiaWheelPicker.ItemTemplate&gt;
        &lt;DataTemplate&gt;
            &lt;draw:SkiaLabel
                Text=&quot;{Binding DisplayText}&quot;
                TextColor=&quot;Black&quot;
                FontSize=&quot;16&quot;
                HorizontalTextAlignment=&quot;Center&quot;
                VerticalTextAlignment=&quot;Center&quot; /&gt;
        &lt;/DataTemplate&gt;
    &lt;/draw:SkiaWheelPicker.ItemTemplate&gt;
    
&lt;/draw:SkiaWheelPicker&gt;
</code></pre>
<h3 id="key-properties-1">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>ItemsSource</code></td>
<td>IEnumerable</td>
<td>Collection of items to display</td>
</tr>
<tr>
<td><code>SelectedItem</code></td>
<td>object</td>
<td>Currently selected item</td>
</tr>
<tr>
<td><code>SelectedIndex</code></td>
<td>int</td>
<td>Index of selected item</td>
</tr>
<tr>
<td><code>ItemHeight</code></td>
<td>double</td>
<td>Height of each item</td>
</tr>
<tr>
<td><code>VisibleItemCount</code></td>
<td>int</td>
<td>Number of visible items</td>
</tr>
<tr>
<td><code>IsLooped</code></td>
<td>bool</td>
<td>Whether picker wraps around</td>
</tr>
<tr>
<td><code>ItemTemplate</code></td>
<td>DataTemplate</td>
<td>Template for custom item appearance</td>
</tr>
</tbody>
</table>
<h2 id="skiahotspot">SkiaHotspot</h2>
<p>SkiaHotspot provides an invisible area for handling gestures in a lazy way.</p>
<h3 id="basic-usage-2">Basic Usage</h3>
<pre><code class="lang-xml">&lt;draw:SkiaLayout&gt;
    &lt;draw:SkiaImage Source=&quot;background.png&quot; /&gt;
    
    &lt;!-- Invisible hotspot over specific area --&gt;
    &lt;draw:SkiaHotspot
        WidthRequest=&quot;100&quot;
        HeightRequest=&quot;100&quot;
        HorizontalOptions=&quot;Start&quot;
        VerticalOptions=&quot;Start&quot;
        Margin=&quot;50,50,0,0&quot;
        Tapped=&quot;OnHotspotTapped&quot; /&gt;
        
&lt;/draw:SkiaLayout&gt;
</code></pre>
<h3 id="gesture-handling">Gesture Handling</h3>
<pre><code class="lang-csharp">private void OnHotspotTapped(object sender, EventArgs e)
{
    // Handle tap gesture
    DisplayAlert(&quot;Hotspot&quot;, &quot;Area tapped!&quot;, &quot;OK&quot;);
}
</code></pre>
<h3 id="key-properties-2">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>IsVisible</code></td>
<td>bool</td>
<td>Whether hotspot is visible (for debugging)</td>
</tr>
<tr>
<td><code>BackgroundColor</code></td>
<td>Color</td>
<td>Background color (usually transparent)</td>
</tr>
<tr>
<td><code>Gestures</code></td>
<td>GesturesMode</td>
<td>Types of gestures to handle</td>
</tr>
</tbody>
</table>
<h3 id="events-1">Events</h3>
<ul>
<li><code>Tapped</code>: Single tap gesture</li>
<li><code>DoubleTapped</code>: Double tap gesture</li>
<li><code>LongPressed</code>: Long press gesture</li>
<li><code>Panning</code>: Pan/drag gesture</li>
<li><code>Pinching</code>: Pinch/zoom gesture</li>
</ul>
<h2 id="examples">Examples</h2>
<h3 id="volume-control-slider">Volume Control Slider</h3>
<pre><code class="lang-xml">&lt;draw:SkiaLayout Type=&quot;Row&quot; Spacing=&quot;10&quot; VerticalOptions=&quot;Center&quot;&gt;
    
    &lt;draw:SkiaImage
        Source=&quot;volume_low.png&quot;
        WidthRequest=&quot;24&quot;
        HeightRequest=&quot;24&quot; /&gt;
    
    &lt;draw:SkiaSlider
        Minimum=&quot;0&quot;
        Maximum=&quot;100&quot;
        Value=&quot;{Binding Volume}&quot;
        WidthRequest=&quot;200&quot;
        HeightRequest=&quot;30&quot;
        TrackColor=&quot;LightGray&quot;
        ThumbColor=&quot;Blue&quot;
        Step=&quot;1&quot; /&gt;
    
    &lt;draw:SkiaImage
        Source=&quot;volume_high.png&quot;
        WidthRequest=&quot;24&quot;
        HeightRequest=&quot;24&quot; /&gt;
        
&lt;/draw:SkiaLayout&gt;
</code></pre>
<h3 id="time-picker-wheel">Time Picker Wheel</h3>
<pre><code class="lang-xml">&lt;draw:SkiaLayout Type=&quot;Row&quot; Spacing=&quot;20&quot; HorizontalOptions=&quot;Center&quot;&gt;
    
    &lt;!-- Hours --&gt;
    &lt;draw:SkiaWheelPicker
        ItemsSource=&quot;{Binding Hours}&quot;
        SelectedItem=&quot;{Binding SelectedHour}&quot;
        WidthRequest=&quot;80&quot;
        HeightRequest=&quot;150&quot;
        ItemHeight=&quot;30&quot; /&gt;
    
    &lt;draw:SkiaLabel Text=&quot;:&quot; FontSize=&quot;24&quot; VerticalOptions=&quot;Center&quot; /&gt;
    
    &lt;!-- Minutes --&gt;
    &lt;draw:SkiaWheelPicker
        ItemsSource=&quot;{Binding Minutes}&quot;
        SelectedItem=&quot;{Binding SelectedMinute}&quot;
        WidthRequest=&quot;80&quot;
        HeightRequest=&quot;150&quot;
        ItemHeight=&quot;30&quot; /&gt;
        
&lt;/draw:SkiaLayout&gt;
</code></pre>
<h2 id="performance-tips">Performance Tips</h2>
<ul>
<li>Use <code>Step</code> property on sliders to limit precision and improve performance</li>
<li>Consider using <code>IsLooped=&quot;False&quot;</code> on wheel pickers for better performance with large datasets</li>
<li>SkiaHotspot is lightweight and ideal for invisible interaction areas</li>
<li>Combine multiple input controls for complex data entry scenarios</li>
</ul>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/input.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>

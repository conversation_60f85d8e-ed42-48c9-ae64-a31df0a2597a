<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Class SkiaLabel.SpanMeasurement | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Class SkiaLabel.SpanMeasurement | DrawnUi Documentation ">
    
    <meta name="description" content="Span-based measurement methods to avoid string allocations">
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement">



  <h1 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement" class="text-break">Class SkiaLabel.SpanMeasurement</h1>
  <div class="markdown level0 summary"><p>Span-based measurement methods to avoid string allocations</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
    <div class="level1"><span class="xref">SkiaLabel.SpanMeasurement</span></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </div>
  <h6><strong>Namespace</strong>: <a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Draw.html">Draw</a></h6>
  <h6><strong>Assembly</strong>: DrawnUi.Maui.dll</h6>
  <h5 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static class SkiaLabel.SpanMeasurement</code></pre>
  </div>
  <h3 id="methods">Methods
</h3>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaLabel_SpanMeasurement_AppendChar_System_Text_StringBuilder_System_Char_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaLabel.SpanMeasurement.AppendChar(System.Text.StringBuilder%2CSystem.Char)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L106">View Source</a>
  </span>
  <a id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_AppendChar_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.AppendChar*"></a>
  <h4 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_AppendChar_System_Text_StringBuilder_System_Char_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.AppendChar(System.Text.StringBuilder,System.Char)">AppendChar(StringBuilder, char)</h4>
  <div class="markdown level1 summary"><p>Appends a single character to StringBuilder</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendChar(StringBuilder sb, char c)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.text.stringbuilder">StringBuilder</a></td>
        <td><span class="parametername">sb</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.char">char</a></td>
        <td><span class="parametername">c</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaLabel_SpanMeasurement_AppendSpan_System_Text_StringBuilder_System_ReadOnlySpan_System_Char__.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaLabel.SpanMeasurement.AppendSpan(System.Text.StringBuilder%2CSystem.ReadOnlySpan%7BSystem.Char%7D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L96">View Source</a>
  </span>
  <a id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_AppendSpan_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.AppendSpan*"></a>
  <h4 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_AppendSpan_System_Text_StringBuilder_System_ReadOnlySpan_System_Char__" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.AppendSpan(System.Text.StringBuilder,System.ReadOnlySpan{System.Char})">AppendSpan(StringBuilder, ReadOnlySpan&lt;char&gt;)</h4>
  <div class="markdown level1 summary"><p>Appends a ReadOnlySpan to StringBuilder without intermediate string allocation</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void AppendSpan(StringBuilder sb, ReadOnlySpan&lt;char&gt; span)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.text.stringbuilder">StringBuilder</a></td>
        <td><span class="parametername">sb</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.readonlyspan-1">ReadOnlySpan</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.char">char</a>&gt;</td>
        <td><span class="parametername">span</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaLabel_SpanMeasurement_IsGlyphAlwaysAvailableSpan_System_ReadOnlySpan_System_Char__.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaLabel.SpanMeasurement.IsGlyphAlwaysAvailableSpan(System.ReadOnlySpan%7BSystem.Char%7D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L48">View Source</a>
  </span>
  <a id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_IsGlyphAlwaysAvailableSpan_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.IsGlyphAlwaysAvailableSpan*"></a>
  <h4 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_IsGlyphAlwaysAvailableSpan_System_ReadOnlySpan_System_Char__" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.IsGlyphAlwaysAvailableSpan(System.ReadOnlySpan{System.Char})">IsGlyphAlwaysAvailableSpan(ReadOnlySpan&lt;char&gt;)</h4>
  <div class="markdown level1 summary"><p>Checks if glyph is always available without string conversion</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsGlyphAlwaysAvailableSpan(ReadOnlySpan&lt;char&gt; glyphSpan)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.readonlyspan-1">ReadOnlySpan</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.char">char</a>&gt;</td>
        <td><span class="parametername">glyphSpan</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaLabel_SpanMeasurement_IsSpaceSpan_System_ReadOnlySpan_System_Char__System_Char_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaLabel.SpanMeasurement.IsSpaceSpan(System.ReadOnlySpan%7BSystem.Char%7D%2CSystem.Char)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L24">View Source</a>
  </span>
  <a id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_IsSpaceSpan_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.IsSpaceSpan*"></a>
  <h4 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_IsSpaceSpan_System_ReadOnlySpan_System_Char__System_Char_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.IsSpaceSpan(System.ReadOnlySpan{System.Char},System.Char)">IsSpaceSpan(ReadOnlySpan&lt;char&gt;, char)</h4>
  <div class="markdown level1 summary"><p>Checks if a span represents a single space character</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool IsSpaceSpan(ReadOnlySpan&lt;char&gt; span, char spaceChar)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.readonlyspan-1">ReadOnlySpan</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.char">char</a>&gt;</td>
        <td><span class="parametername">span</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.char">char</a></td>
        <td><span class="parametername">spaceChar</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaLabel_SpanMeasurement_LastNonSpaceIndexSpan_System_ReadOnlySpan_System_Char__.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaLabel.SpanMeasurement.LastNonSpaceIndexSpan(System.ReadOnlySpan%7BSystem.Char%7D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L33">View Source</a>
  </span>
  <a id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_LastNonSpaceIndexSpan_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.LastNonSpaceIndexSpan*"></a>
  <h4 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_LastNonSpaceIndexSpan_System_ReadOnlySpan_System_Char__" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.LastNonSpaceIndexSpan(System.ReadOnlySpan{System.Char})">LastNonSpaceIndexSpan(ReadOnlySpan&lt;char&gt;)</h4>
  <div class="markdown level1 summary"><p>Finds the last non-space character index in a span</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int LastNonSpaceIndexSpan(ReadOnlySpan&lt;char&gt; textSpan)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.readonlyspan-1">ReadOnlySpan</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.char">char</a>&gt;</td>
        <td><span class="parametername">textSpan</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaLabel_SpanMeasurement_MeasurePartialTextWidthSpan_SkiaSharp_SKPaint_System_ReadOnlySpan_System_Char__System_Boolean_System_Single_SkiaSharp_SKTypeface_.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaLabel.SpanMeasurement.MeasurePartialTextWidthSpan(SkiaSharp.SKPaint%2CSystem.ReadOnlySpan%7BSystem.Char%7D%2CSystem.Boolean%2CSystem.Single%2CSkiaSharp.SKTypeface)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L69">View Source</a>
  </span>
  <a id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_MeasurePartialTextWidthSpan_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.MeasurePartialTextWidthSpan*"></a>
  <h4 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_MeasurePartialTextWidthSpan_SkiaSharp_SKPaint_System_ReadOnlySpan_System_Char__System_Boolean_System_Single_SkiaSharp_SKTypeface_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.MeasurePartialTextWidthSpan(SkiaSharp.SKPaint,System.ReadOnlySpan{System.Char},System.Boolean,System.Single,SkiaSharp.SKTypeface)">MeasurePartialTextWidthSpan(SKPaint, ReadOnlySpan&lt;char&gt;, bool, float, SKTypeface)</h4>
  <div class="markdown level1 summary"><p>Measures partial text width using span-based operations where possible
Falls back to string conversion only for cache compatibility</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float MeasurePartialTextWidthSpan(SKPaint paint, ReadOnlySpan&lt;char&gt; textSpan, bool needsShaping, float scale, SKTypeface paintTypeface)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpaint">SKPaint</a></td>
        <td><span class="parametername">paint</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.readonlyspan-1">ReadOnlySpan</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.char">char</a>&gt;</td>
        <td><span class="parametername">textSpan</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></td>
        <td><span class="parametername">needsShaping</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></td>
        <td><span class="parametername">scale</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.sktypeface">SKTypeface</a></td>
        <td><span class="parametername">paintTypeface</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaLabel_SpanMeasurement_MeasureTextWidthWithAdvanceSpan_SkiaSharp_SKPaint_System_ReadOnlySpan_System_Char__.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaLabel.SpanMeasurement.MeasureTextWidthWithAdvanceSpan(SkiaSharp.SKPaint%2CSystem.ReadOnlySpan%7BSystem.Char%7D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L15">View Source</a>
  </span>
  <a id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_MeasureTextWidthWithAdvanceSpan_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.MeasureTextWidthWithAdvanceSpan*"></a>
  <h4 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_MeasureTextWidthWithAdvanceSpan_SkiaSharp_SKPaint_System_ReadOnlySpan_System_Char__" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.MeasureTextWidthWithAdvanceSpan(SkiaSharp.SKPaint,System.ReadOnlySpan{System.Char})">MeasureTextWidthWithAdvanceSpan(SKPaint, ReadOnlySpan&lt;char&gt;)</h4>
  <div class="markdown level1 summary"><p>Measures text width using ReadOnlySpan without converting to string</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float MeasureTextWidthWithAdvanceSpan(SKPaint paint, ReadOnlySpan&lt;char&gt; textSpan)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/skiasharp.skpaint">SKPaint</a></td>
        <td><span class="parametername">paint</span></td>
        <td></td>
      </tr>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.readonlyspan-1">ReadOnlySpan</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.char">char</a>&gt;</td>
        <td><span class="parametername">textSpan</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.single">float</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <span class="small pull-right mobile-hide">
    <span class="divider">|</span>
    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaLabel_SpanMeasurement_SpanToStringForCache_System_ReadOnlySpan_System_Char__.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaLabel.SpanMeasurement.SpanToStringForCache(System.ReadOnlySpan%7BSystem.Char%7D)%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">Edit this page</a>
  </span>
  <span class="small pull-right mobile-hide">
    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L58">View Source</a>
  </span>
  <a id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_SpanToStringForCache_" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.SpanToStringForCache*"></a>
  <h4 id="DrawnUi_Draw_SkiaLabel_SpanMeasurement_SpanToStringForCache_System_ReadOnlySpan_System_Char__" data-uid="DrawnUi.Draw.SkiaLabel.SpanMeasurement.SpanToStringForCache(System.ReadOnlySpan{System.Char})">SpanToStringForCache(ReadOnlySpan&lt;char&gt;)</h4>
  <div class="markdown level1 summary"><p>Converts ReadOnlySpan to string only when absolutely necessary for cache keys
This is the only place where we allow span-to-string conversion for caching</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="declaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static string SpanToStringForCache(ReadOnlySpan&lt;char&gt; span)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.readonlyspan-1">ReadOnlySpan</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.char">char</a>&gt;</td>
        <td><span class="parametername">span</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/new/master/apiSpec/new?filename=DrawnUi_Draw_SkiaLabel_SpanMeasurement.md&amp;value=---%0Auid%3A%20DrawnUi.Draw.SkiaLabel.SpanMeasurement%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A" class="contribution-link">Edit this page</a>
                  </li>
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Maui/DrawnUi/Draw/Text/SkiaLabel.SpanMeasurement.cs/#L10" class="contribution-link">View Source</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>

﻿namespace AppoMobi.Mobile.Views;

public class CellSearchText : FastCellWithBanner
{
    public CellSearchText()
    {
        Padding = new(0, 8);
        HorizontalOptions = LayoutOptions.Fill;
        HeightRequest = -1;
        UseCache = SkiaCacheType.Image;
        Children = new List<SkiaControl>()
        {
            new SkiaLabel()
            {
                HorizontalOptions = LayoutOptions.Fill,
                VerticalOptions = LayoutOptions.Center,
                TextColor = AppColors.Text,
                FontSize = 15,
                MaxLines = 2,
                LineBreakMode = LineBreakMode.TailTruncation,
                FontFamily = AppFonts.Normal
            }.Adapt((label) => { label.BindProperty(SkiaLabel.TextProperty, "."); }),
        };
    }
}
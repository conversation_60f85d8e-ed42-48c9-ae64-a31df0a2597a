### YamlMime:ManagedReference
items:
- uid: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer
  commentId: T:DrawnUi.Controls.SkiaLottie.ColorEqualityComparer
  id: SkiaLottie.ColorEqualityComparer
  parent: DrawnUi.Controls
  children:
  - DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.Equals(Microsoft.Maui.Graphics.Color,Microsoft.Maui.Graphics.Color)
  - DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.GetHashCode(Microsoft.Maui.Graphics.Color)
  langs:
  - csharp
  - vb
  name: SkiaLottie.ColorEqualityComparer
  nameWithType: SkiaLottie.ColorEqualityComparer
  fullName: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PlayFrames/SkiaLottie.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ColorEqualityComparer
    path: ../src/Maui/DrawnUi/Controls/PlayFrames/SkiaLottie.cs
    startLine: 692
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  syntax:
    content: 'public class SkiaLottie.ColorEqualityComparer : IEqualityComparer<Color>'
    content.vb: Public Class SkiaLottie.ColorEqualityComparer Implements IEqualityComparer(Of Color)
  inheritance:
  - System.Object
  implements:
  - System.Collections.Generic.IEqualityComparer{Microsoft.Maui.Graphics.Color}
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.Equals(Microsoft.Maui.Graphics.Color,Microsoft.Maui.Graphics.Color)
  commentId: M:DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.Equals(Microsoft.Maui.Graphics.Color,Microsoft.Maui.Graphics.Color)
  id: Equals(Microsoft.Maui.Graphics.Color,Microsoft.Maui.Graphics.Color)
  parent: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer
  langs:
  - csharp
  - vb
  name: Equals(Color, Color)
  nameWithType: SkiaLottie.ColorEqualityComparer.Equals(Color, Color)
  fullName: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.Equals(Microsoft.Maui.Graphics.Color, Microsoft.Maui.Graphics.Color)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PlayFrames/SkiaLottie.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Equals
    path: ../src/Maui/DrawnUi/Controls/PlayFrames/SkiaLottie.cs
    startLine: 696
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  summary: Determines whether the specified objects are equal.
  example: []
  syntax:
    content: public bool Equals(Color x, Color y)
    parameters:
    - id: x
      type: Microsoft.Maui.Graphics.Color
      description: The first object of type <code class="paramref">T</code> to compare.
    - id: y
      type: Microsoft.Maui.Graphics.Color
      description: The second object of type <code class="paramref">T</code> to compare.
    return:
      type: System.Boolean
      description: <a href="https://learn.microsoft.com/dotnet/csharp/language-reference/builtin-types/bool">true</a> if the specified objects are equal; otherwise, <a href="https://learn.microsoft.com/dotnet/csharp/language-reference/builtin-types/bool">false</a>.
    content.vb: Public Function Equals(x As Color, y As Color) As Boolean
  overload: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.Equals*
  implements:
  - System.Collections.Generic.IEqualityComparer{Microsoft.Maui.Graphics.Color}.Equals(Microsoft.Maui.Graphics.Color,Microsoft.Maui.Graphics.Color)
- uid: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.GetHashCode(Microsoft.Maui.Graphics.Color)
  commentId: M:DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.GetHashCode(Microsoft.Maui.Graphics.Color)
  id: GetHashCode(Microsoft.Maui.Graphics.Color)
  parent: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer
  langs:
  - csharp
  - vb
  name: GetHashCode(Color)
  nameWithType: SkiaLottie.ColorEqualityComparer.GetHashCode(Color)
  fullName: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.GetHashCode(Microsoft.Maui.Graphics.Color)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Controls/PlayFrames/SkiaLottie.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetHashCode
    path: ../src/Maui/DrawnUi/Controls/PlayFrames/SkiaLottie.cs
    startLine: 704
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Controls
  summary: Returns a hash code for the specified object.
  example: []
  syntax:
    content: public int GetHashCode(Color obj)
    parameters:
    - id: obj
      type: Microsoft.Maui.Graphics.Color
      description: The <xref href="System.Object" data-throw-if-not-resolved="false"></xref> for which a hash code is to be returned.
    return:
      type: System.Int32
      description: A hash code for the specified object.
    content.vb: Public Function GetHashCode(obj As Color) As Integer
  overload: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.GetHashCode*
  exceptions:
  - type: System.ArgumentNullException
    commentId: T:System.ArgumentNullException
    description: The type of <code class="paramref">obj</code> is a reference type and <code class="paramref">obj</code> is <a href="https://learn.microsoft.com/dotnet/csharp/language-reference/keywords/null">null</a>.
  implements:
  - System.Collections.Generic.IEqualityComparer{Microsoft.Maui.Graphics.Color}.GetHashCode(Microsoft.Maui.Graphics.Color)
references:
- uid: DrawnUi.Controls
  commentId: N:DrawnUi.Controls
  href: DrawnUi.html
  name: DrawnUi.Controls
  nameWithType: DrawnUi.Controls
  fullName: DrawnUi.Controls
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Collections.Generic.IEqualityComparer{Microsoft.Maui.Graphics.Color}
  commentId: T:System.Collections.Generic.IEqualityComparer{Microsoft.Maui.Graphics.Color}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IEqualityComparer`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.iequalitycomparer-1
  name: IEqualityComparer<Color>
  nameWithType: IEqualityComparer<Color>
  fullName: System.Collections.Generic.IEqualityComparer<Microsoft.Maui.Graphics.Color>
  nameWithType.vb: IEqualityComparer(Of Color)
  fullName.vb: System.Collections.Generic.IEqualityComparer(Of Microsoft.Maui.Graphics.Color)
  name.vb: IEqualityComparer(Of Color)
  spec.csharp:
  - uid: System.Collections.Generic.IEqualityComparer`1
    name: IEqualityComparer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.iequalitycomparer-1
  - name: <
  - uid: Microsoft.Maui.Graphics.Color
    name: Color
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEqualityComparer`1
    name: IEqualityComparer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.iequalitycomparer-1
  - name: (
  - name: Of
  - name: " "
  - uid: Microsoft.Maui.Graphics.Color
    name: Color
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color
  - name: )
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.Collections.Generic.IEqualityComparer`1
  commentId: T:System.Collections.Generic.IEqualityComparer`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.iequalitycomparer-1
  name: IEqualityComparer<T>
  nameWithType: IEqualityComparer<T>
  fullName: System.Collections.Generic.IEqualityComparer<T>
  nameWithType.vb: IEqualityComparer(Of T)
  fullName.vb: System.Collections.Generic.IEqualityComparer(Of T)
  name.vb: IEqualityComparer(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IEqualityComparer`1
    name: IEqualityComparer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.iequalitycomparer-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEqualityComparer`1
    name: IEqualityComparer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.iequalitycomparer-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.Equals*
  commentId: Overload:DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.Equals
  href: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.html#DrawnUi_Controls_SkiaLottie_ColorEqualityComparer_Equals_Microsoft_Maui_Graphics_Color_Microsoft_Maui_Graphics_Color_
  name: Equals
  nameWithType: SkiaLottie.ColorEqualityComparer.Equals
  fullName: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.Equals
- uid: System.Collections.Generic.IEqualityComparer{Microsoft.Maui.Graphics.Color}.Equals(Microsoft.Maui.Graphics.Color,Microsoft.Maui.Graphics.Color)
  commentId: M:System.Collections.Generic.IEqualityComparer{Microsoft.Maui.Graphics.Color}.Equals(Microsoft.Maui.Graphics.Color,Microsoft.Maui.Graphics.Color)
  parent: System.Collections.Generic.IEqualityComparer{Microsoft.Maui.Graphics.Color}
  definition: System.Collections.Generic.IEqualityComparer`1.Equals(`0,`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.iequalitycomparer-1.equals
  name: Equals(Color, Color)
  nameWithType: IEqualityComparer<Color>.Equals(Color, Color)
  fullName: System.Collections.Generic.IEqualityComparer<Microsoft.Maui.Graphics.Color>.Equals(Microsoft.Maui.Graphics.Color, Microsoft.Maui.Graphics.Color)
  nameWithType.vb: IEqualityComparer(Of Color).Equals(Color, Color)
  fullName.vb: System.Collections.Generic.IEqualityComparer(Of Microsoft.Maui.Graphics.Color).Equals(Microsoft.Maui.Graphics.Color, Microsoft.Maui.Graphics.Color)
  spec.csharp:
  - uid: System.Collections.Generic.IEqualityComparer{Microsoft.Maui.Graphics.Color}.Equals(Microsoft.Maui.Graphics.Color,Microsoft.Maui.Graphics.Color)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.iequalitycomparer-1.equals
  - name: (
  - uid: Microsoft.Maui.Graphics.Color
    name: Color
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Graphics.Color
    name: Color
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color
  - name: )
  spec.vb:
  - uid: System.Collections.Generic.IEqualityComparer{Microsoft.Maui.Graphics.Color}.Equals(Microsoft.Maui.Graphics.Color,Microsoft.Maui.Graphics.Color)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.iequalitycomparer-1.equals
  - name: (
  - uid: Microsoft.Maui.Graphics.Color
    name: Color
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Graphics.Color
    name: Color
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color
  - name: )
- uid: Microsoft.Maui.Graphics.Color
  commentId: T:Microsoft.Maui.Graphics.Color
  parent: Microsoft.Maui.Graphics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color
  name: Color
  nameWithType: Color
  fullName: Microsoft.Maui.Graphics.Color
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: System.Collections.Generic.IEqualityComparer`1.Equals(`0,`0)
  commentId: M:System.Collections.Generic.IEqualityComparer`1.Equals(`0,`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.iequalitycomparer-1.equals
  name: Equals(T, T)
  nameWithType: IEqualityComparer<T>.Equals(T, T)
  fullName: System.Collections.Generic.IEqualityComparer<T>.Equals(T, T)
  nameWithType.vb: IEqualityComparer(Of T).Equals(T, T)
  fullName.vb: System.Collections.Generic.IEqualityComparer(Of T).Equals(T, T)
  spec.csharp:
  - uid: System.Collections.Generic.IEqualityComparer`1.Equals(`0,`0)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.iequalitycomparer-1.equals
  - name: (
  - name: T
  - name: ','
  - name: " "
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.Generic.IEqualityComparer`1.Equals(`0,`0)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.iequalitycomparer-1.equals
  - name: (
  - name: T
  - name: ','
  - name: " "
  - name: T
  - name: )
- uid: Microsoft.Maui.Graphics
  commentId: N:Microsoft.Maui.Graphics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Graphics
  nameWithType: Microsoft.Maui.Graphics
  fullName: Microsoft.Maui.Graphics
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Graphics
    name: Graphics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Graphics
    name: Graphics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics
- uid: System.ArgumentNullException
  commentId: T:System.ArgumentNullException
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.argumentnullexception
  name: ArgumentNullException
  nameWithType: ArgumentNullException
  fullName: System.ArgumentNullException
- uid: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.GetHashCode*
  commentId: Overload:DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.GetHashCode
  href: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.html#DrawnUi_Controls_SkiaLottie_ColorEqualityComparer_GetHashCode_Microsoft_Maui_Graphics_Color_
  name: GetHashCode
  nameWithType: SkiaLottie.ColorEqualityComparer.GetHashCode
  fullName: DrawnUi.Controls.SkiaLottie.ColorEqualityComparer.GetHashCode
- uid: System.Collections.Generic.IEqualityComparer{Microsoft.Maui.Graphics.Color}.GetHashCode(Microsoft.Maui.Graphics.Color)
  commentId: M:System.Collections.Generic.IEqualityComparer{Microsoft.Maui.Graphics.Color}.GetHashCode(Microsoft.Maui.Graphics.Color)
  parent: System.Collections.Generic.IEqualityComparer{Microsoft.Maui.Graphics.Color}
  definition: System.Collections.Generic.IEqualityComparer`1.GetHashCode(`0)
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.iequalitycomparer-1.gethashcode
  name: GetHashCode(Color)
  nameWithType: IEqualityComparer<Color>.GetHashCode(Color)
  fullName: System.Collections.Generic.IEqualityComparer<Microsoft.Maui.Graphics.Color>.GetHashCode(Microsoft.Maui.Graphics.Color)
  nameWithType.vb: IEqualityComparer(Of Color).GetHashCode(Color)
  fullName.vb: System.Collections.Generic.IEqualityComparer(Of Microsoft.Maui.Graphics.Color).GetHashCode(Microsoft.Maui.Graphics.Color)
  spec.csharp:
  - uid: System.Collections.Generic.IEqualityComparer{Microsoft.Maui.Graphics.Color}.GetHashCode(Microsoft.Maui.Graphics.Color)
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.iequalitycomparer-1.gethashcode
  - name: (
  - uid: Microsoft.Maui.Graphics.Color
    name: Color
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color
  - name: )
  spec.vb:
  - uid: System.Collections.Generic.IEqualityComparer{Microsoft.Maui.Graphics.Color}.GetHashCode(Microsoft.Maui.Graphics.Color)
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.iequalitycomparer-1.gethashcode
  - name: (
  - uid: Microsoft.Maui.Graphics.Color
    name: Color
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color
  - name: )
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: System.Collections.Generic.IEqualityComparer`1.GetHashCode(`0)
  commentId: M:System.Collections.Generic.IEqualityComparer`1.GetHashCode(`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.iequalitycomparer-1.gethashcode
  name: GetHashCode(T)
  nameWithType: IEqualityComparer<T>.GetHashCode(T)
  fullName: System.Collections.Generic.IEqualityComparer<T>.GetHashCode(T)
  nameWithType.vb: IEqualityComparer(Of T).GetHashCode(T)
  fullName.vb: System.Collections.Generic.IEqualityComparer(Of T).GetHashCode(T)
  spec.csharp:
  - uid: System.Collections.Generic.IEqualityComparer`1.GetHashCode(`0)
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.iequalitycomparer-1.gethashcode
  - name: (
  - name: T
  - name: )
  spec.vb:
  - uid: System.Collections.Generic.IEqualityComparer`1.GetHashCode(`0)
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.iequalitycomparer-1.gethashcode
  - name: (
  - name: T
  - name: )

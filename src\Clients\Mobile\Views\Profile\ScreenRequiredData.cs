﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AppoMobi.Forms.UniversalEditor.Controls;
using AppoMobi.Framework.Maui.Converters;
using DrawnUi.Controls;
using Font = Microsoft.Maui.Font;

namespace AppoMobi.Mobile.Views;



/// <summary>
/// Required personal data page that collects user information
/// </summary>
public class ScreenRequiredData : ChangeProfileData
{
    /*
     //todo

        protected override void OnAppearing()
            {
                base.OnAppearing();

                Device.StartTimer(TimeSpan.FromMilliseconds(50), () =>
                {
                    Settings.UpdateStatusBarUponTheme();

                    return false;// Don't repeat the timer
                });

            }

            protected override bool OnBackButtonPressed()
            {
                return true;
            }

     */
    private readonly RequiredPersonalDataViewModel Model;

    public ScreenRequiredData(RequiredPersonalDataViewModel vm)
    {
        Model = vm;
        BindingContext = Model;

        Shell.SetPresentationMode(this, PresentationMode.ModalAnimated);

        VerticalOptions = LayoutOptions.Fill;
        HorizontalOptions = LayoutOptions.Fill;
        BackgroundColor = AppColors.Background;

        CreateContent();
    }

    private void CreateContent()
    {
        Children = new List<SkiaControl>()
        {
            //new ScreenBackground(),

            new SkiaScroll()
            {
                VerticalOptions = LayoutOptions.Fill,
                HorizontalOptions = LayoutOptions.Fill,
                Content = new ScreenVerticalStack()
                {
                    Type = LayoutType.Column,
                    Spacing = 24,
                    HorizontalOptions = LayoutOptions.Fill,
                    VerticalOptions = LayoutOptions.Fill,
                    Children = new List<SkiaControl>()
                    {
                        // Status bar padding
                        new SkiaControl()
                        {
                            HorizontalOptions = LayoutOptions.Fill,
                            VerticalOptions = LayoutOptions.Start
                        }.Adapt((shape) =>
                        {
                            shape.SetBinding(SkiaShape.HeightRequestProperty, "Presentation.StatusBarHeightRequest");
                        }),

                        // Page Title
                        new LabelScreenTitle()
                        {
                            Margin = new Thickness(0, 24, 0, 0),
                            Text = ResStrings.Auth_PersonalData,
                        },

                        // Subtitle
                        new SkiaLabel()
                        {
                            Margin = new Thickness(0, 0, 0, 8),
                            TextColor = AppColors.Text,
                            FontSize = 13,

                            Text = ResStrings.PleaseFillRequiredFields,
                        },

                        // Avatar
                        /*
                        new PaintAvatar()
                        {
                            Margin = new Thickness(0, 0, 0, 8),
                            HorizontalOptions = LayoutOptions.Center,
                            HeightRequest = 80,
                            WidthRequest = 80,
                            StyleId = "Avatar",
                            VerticalOptions = LayoutOptions.Start
                        }.Adapt((avatar) =>
                        {
#if DEBUG
                            avatar.SetBinding(PaintAvatar.SourceProperty, "Presentation.ImageMain.Normal");
#else
                                avatar.SetBinding(PaintAvatar.SourceProperty,
                                    static (RequiredPersonalDataViewModel vm) => vm.Presentation.ImageMain.Normal);
#endif
                        }),
                        */

                        // Personal Information Frame
                        CreatePersonalInfoFrame(),

                        // Validation Errors Label
                        new SkiaLabel()
                        {
                            Margin = new Thickness(0, 0, 0, 24),
                            FontSize = 10,
                            HorizontalOptions = LayoutOptions.Center,
                            HorizontalTextAlignment = DrawTextAlignment.Center,
                            TextColor = AppColors.Danger
                        }
                        .ObserveBindingContext<SkiaLabel, RequiredPersonalDataViewModel>((me, vm, prop) =>
                        {
                            if (prop == nameof(vm.EditorValidationErrorsDesc) || prop == nameof(BindingContext))
                            {
                                me.Text = vm.EditorValidationErrorsDesc;
                                me.IsVisible = !string.IsNullOrEmpty(vm.EditorValidationErrorsDesc);
                            }
                        }),

                        // Submit Button
                        new AppSubmitButton(ResStrings.BtnOk)
                        {
                            Margin = new Thickness(0, 8, 0, 8),
                        }
                        .ObserveBindingContext<ButtonMedium, RequiredPersonalDataViewModel>((me, vm, prop) =>
                        {
                            bool attached = prop == nameof(BindingContext);
                            if (attached || prop == nameof(vm.CommandEditorSubmit))
                            {
                                me.CommandTapped = vm.CommandEditorSubmit;
                            }
                            if (attached || prop == nameof(vm.IsBusy))
                            {
                                me.IsVisible = !vm.IsBusy;
                            }
                            if (attached || prop == nameof(vm.EditorIsValid))
                            {
                                me.Look = vm.EditorIsValid ? BtnStyle.Default : BtnStyle.Disabled;
                            }
                        }),                                        

                        // Activity Indicator
                        new AppActivityIndicator()
                        {
                            IsRunning = true,
                        }.Adapt((indicator) =>
                        {
#if DEBUG
                            indicator.SetBinding(AppActivityIndicator.IsVisibleProperty, "IsBusy");
#else
                                indicator.SetBinding(AppActivityIndicator.IsVisibleProperty, 
                                    static (RequiredPersonalDataViewModel vm) => vm.IsBusy);
#endif
                        }),
#if DEBUG
                        // Log Off Button
                        new SkiaLayout()
                        {
                            UseCache = SkiaCacheType.Operations,
                            VerticalOptions = LayoutOptions.Start,
                            Children = new List<SkiaControl>()
                            {
                                new SkiaLabel()
                                {
                                    Margin = new Thickness(30),
                                    FontFamily = "FontTextBold",
                                    FontSize = 13,
                                    HorizontalOptions = LayoutOptions.Center,
                                    Text = ResStrings.ActionLogOff,
                                    TextColor = AppColors.IconSecondary
                                },

                                new SkiaHotspot()
                                {
                                    AnimationTapped = SkiaTouchAnimation.Ripple
                                }.Adapt((hotspot) =>
                                {
                                    hotspot.SetBinding(SkiaHotspot.CommandTappedProperty, "CommandLogOff");
                                })
                            }
                        },
#endif

                        // Keyboard Offset
                        new SkiaShape()
                        {
                            VerticalOptions = LayoutOptions.Start
                        }.Adapt((shape) =>
                        {
                            shape.SetBinding(SkiaShape.HeightRequestProperty, "Presentation.KeyboardOffset");
                        })
                    }
                }
            }.Adapt((scroll) =>
            {
                // Set up keyboard aware behavior
                scroll.ObserveBindingContext<SkiaScroll, RequiredPersonalDataViewModel>((scroll, vm, prop) =>
                {
                    if (prop == nameof(vm.Presentation.FocusedElement) || prop == nameof(vm.Presentation.Keyboard) ||
                        prop == nameof(BindingContext))
                    {
                        // Apply keyboard aware scrolling similar to ScrollViewKeyboardAwareBehavior
                        if (vm.Presentation.FocusedElement != null && vm.Presentation.Keyboard > 0)
                        {
                            // Scroll to focused element
                            Super.Log($"[TODO] scrollt to view {vm.Presentation.FocusedElement}");
                            //scroll.ScrollToView(vm.Presentation.FocusedElement, true, 0.35);
                        }
                    }
                });
            })
        };
    }

 
}
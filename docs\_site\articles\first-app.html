<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Your First DrawnUi App | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Your First DrawnUi App | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="../toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">
        <div class="article row grid">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="your-first-drawnui-app">Your First DrawnUi App</h1>

<p>This quickstart guide will help you create your first DrawnUi.Maui application from scratch.</p>
<h2 id="prerequisites">Prerequisites</h2>
<ul>
<li>.NET 8 or later</li>
<li>Visual Studio 2022+ (with MAUI workload) or VS Code</li>
</ul>
<h2 id="1-create-a-new-maui-project">1. Create a New MAUI Project</h2>
<pre><code class="lang-bash">dotnet new maui -n MyDrawnUiApp
cd MyDrawnUiApp
</code></pre>
<h2 id="2-add-drawnui-to-your-project">2. Add DrawnUi to Your Project</h2>
<pre><code class="lang-bash">dotnet add package DrawnUi.Maui
</code></pre>
<h2 id="3-add-a-drawnui-canvas-to-mainpage">3. Add a DrawnUi Canvas to MainPage</h2>
<p>Open <code>MainPage.xaml</code> and replace the content with:</p>
<pre><code class="lang-xml">&lt;ContentPage xmlns=&quot;http://schemas.microsoft.com/dotnet/2021/maui&quot;
             xmlns:x=&quot;http://schemas.microsoft.com/winfx/2009/xaml&quot;
             xmlns:draw=&quot;http://schemas.appomobi.com/drawnUi/2023/draw&quot;
             x:Class=&quot;MyDrawnUiApp.MainPage&quot;&gt;
    &lt;draw:Canvas HorizontalOptions=&quot;Fill&quot; VerticalOptions=&quot;Fill&quot;&gt;
        &lt;draw:SkiaLayout Type=&quot;Column&quot; Padding=&quot;32&quot; Spacing=&quot;24&quot;&gt;
            &lt;draw:SkiaLabel Text=&quot;Hello, DrawnUi!&quot; FontSize=&quot;32&quot; TextColor=&quot;Blue&quot; /&gt;
            &lt;draw:SkiaButton Text=&quot;Click Me&quot; Clicked=&quot;OnButtonClicked&quot; /&gt;
        &lt;/draw:SkiaLayout&gt;
    &lt;/draw:Canvas&gt;
&lt;/ContentPage&gt;
</code></pre>
<h2 id="4-initialize-drawnui-in-mauiprogramcs">4. Initialize DrawnUi in MauiProgram.cs</h2>
<p>Add the DrawnUi initialization to your <code>MauiProgram.cs</code>:</p>
<pre><code class="lang-csharp">public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp&lt;App&gt;()
            .UseDrawnUi() // Add this line
            .ConfigureFonts(fonts =&gt;
            {
                fonts.AddFont(&quot;OpenSans-Regular.ttf&quot;, &quot;FontText&quot;);
            });

        return builder.Build();
    }
}
</code></pre>
<h2 id="5-handle-button-click-in-code">5. Handle Button Click in Code</h2>
<p>In <code>MainPage.xaml.cs</code>:</p>
<pre><code class="lang-csharp">private void OnButtonClicked(SkiaButton sender, SkiaGesturesParameters e)
{
    // Show a message or update UI
    DisplayAlert(&quot;DrawnUi&quot;, &quot;Button clicked!&quot;, &quot;OK&quot;);
}
</code></pre>
<h2 id="6-run-your-app">6. Run Your App</h2>
<p>Build and run your app on Windows, Android, iOS, or Mac.</p>
<h2 id="next-steps">Next Steps</h2>
<ul>
<li>Explore the <a href="controls/index.html">Controls documentation</a></li>
<li>Try out <a href="samples.html">Samples</a></li>
<li>Read about <a href="advanced/index.html">Advanced features</a></li>
</ul>
<p>Welcome to the DrawnUi community!</p>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/first-app.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>

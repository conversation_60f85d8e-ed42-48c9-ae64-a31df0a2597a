### YamlMime:ManagedReference
items:
- uid: DrawnUi.Views.DrawnView.FocusedItemChangedArgs
  commentId: T:DrawnUi.Views.DrawnView.FocusedItemChangedArgs
  id: DrawnView.FocusedItemChangedArgs
  parent: DrawnUi.Views
  children:
  - DrawnUi.Views.DrawnView.FocusedItemChangedArgs.#ctor(DrawnUi.Draw.SkiaControl,System.Boolean)
  - DrawnUi.Views.DrawnView.FocusedItemChangedArgs.IsFocused
  - DrawnUi.Views.DrawnView.FocusedItemChangedArgs.Item
  langs:
  - csharp
  - vb
  name: DrawnView.FocusedItemChangedArgs
  nameWithType: DrawnView.FocusedItemChangedArgs
  fullName: DrawnUi.Views.DrawnView.FocusedItemChangedArgs
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Views/DrawnView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FocusedItemChangedArgs
    path: ../src/Maui/DrawnUi/Views/DrawnView.cs
    startLine: 2436
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: 'public class DrawnView.FocusedItemChangedArgs : EventArgs'
    content.vb: Public Class DrawnView.FocusedItemChangedArgs Inherits EventArgs
  inheritance:
  - System.Object
  - System.EventArgs
  inheritedMembers:
  - System.EventArgs.Empty
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.#ctor(DrawnUi.Draw.SkiaControl,System.Boolean)
  commentId: M:DrawnUi.Views.DrawnView.FocusedItemChangedArgs.#ctor(DrawnUi.Draw.SkiaControl,System.Boolean)
  id: '#ctor(DrawnUi.Draw.SkiaControl,System.Boolean)'
  parent: DrawnUi.Views.DrawnView.FocusedItemChangedArgs
  langs:
  - csharp
  - vb
  name: FocusedItemChangedArgs(SkiaControl, bool)
  nameWithType: DrawnView.FocusedItemChangedArgs.FocusedItemChangedArgs(SkiaControl, bool)
  fullName: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.FocusedItemChangedArgs(DrawnUi.Draw.SkiaControl, bool)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Views/DrawnView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Views/DrawnView.cs
    startLine: 2438
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public FocusedItemChangedArgs(SkiaControl item, bool isFocused)
    parameters:
    - id: item
      type: DrawnUi.Draw.SkiaControl
    - id: isFocused
      type: System.Boolean
    content.vb: Public Sub New(item As SkiaControl, isFocused As Boolean)
  overload: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.#ctor*
  nameWithType.vb: DrawnView.FocusedItemChangedArgs.New(SkiaControl, Boolean)
  fullName.vb: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.New(DrawnUi.Draw.SkiaControl, Boolean)
  name.vb: New(SkiaControl, Boolean)
- uid: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.IsFocused
  commentId: P:DrawnUi.Views.DrawnView.FocusedItemChangedArgs.IsFocused
  id: IsFocused
  parent: DrawnUi.Views.DrawnView.FocusedItemChangedArgs
  langs:
  - csharp
  - vb
  name: IsFocused
  nameWithType: DrawnView.FocusedItemChangedArgs.IsFocused
  fullName: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.IsFocused
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/DrawnView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsFocused
    path: ../src/Maui/DrawnUi/Views/DrawnView.cs
    startLine: 2444
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public bool IsFocused { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsFocused As Boolean
  overload: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.IsFocused*
- uid: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.Item
  commentId: P:DrawnUi.Views.DrawnView.FocusedItemChangedArgs.Item
  id: Item
  parent: DrawnUi.Views.DrawnView.FocusedItemChangedArgs
  langs:
  - csharp
  - vb
  name: Item
  nameWithType: DrawnView.FocusedItemChangedArgs.Item
  fullName: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.Item
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/DrawnView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Item
    path: ../src/Maui/DrawnUi/Views/DrawnView.cs
    startLine: 2445
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public SkiaControl Item { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.SkiaControl
    content.vb: Public Property Item As SkiaControl
  overload: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.Item*
references:
- uid: DrawnUi.Views
  commentId: N:DrawnUi.Views
  href: DrawnUi.html
  name: DrawnUi.Views
  nameWithType: DrawnUi.Views
  fullName: DrawnUi.Views
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Views
    name: Views
    href: DrawnUi.Views.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Views
    name: Views
    href: DrawnUi.Views.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.EventArgs
  commentId: T:System.EventArgs
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.eventargs
  name: EventArgs
  nameWithType: EventArgs
  fullName: System.EventArgs
- uid: System.EventArgs.Empty
  commentId: F:System.EventArgs.Empty
  parent: System.EventArgs
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.eventargs.empty
  name: Empty
  nameWithType: EventArgs.Empty
  fullName: System.EventArgs.Empty
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.#ctor*
  commentId: Overload:DrawnUi.Views.DrawnView.FocusedItemChangedArgs.#ctor
  href: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.html#DrawnUi_Views_DrawnView_FocusedItemChangedArgs__ctor_DrawnUi_Draw_SkiaControl_System_Boolean_
  name: FocusedItemChangedArgs
  nameWithType: DrawnView.FocusedItemChangedArgs.FocusedItemChangedArgs
  fullName: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.FocusedItemChangedArgs
  nameWithType.vb: DrawnView.FocusedItemChangedArgs.New
  fullName.vb: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.New
  name.vb: New
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.IsFocused*
  commentId: Overload:DrawnUi.Views.DrawnView.FocusedItemChangedArgs.IsFocused
  href: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.html#DrawnUi_Views_DrawnView_FocusedItemChangedArgs_IsFocused
  name: IsFocused
  nameWithType: DrawnView.FocusedItemChangedArgs.IsFocused
  fullName: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.IsFocused
- uid: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.Item*
  commentId: Overload:DrawnUi.Views.DrawnView.FocusedItemChangedArgs.Item
  href: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.html#DrawnUi_Views_DrawnView_FocusedItemChangedArgs_Item
  name: Item
  nameWithType: DrawnView.FocusedItemChangedArgs.Item
  fullName: DrawnUi.Views.DrawnView.FocusedItemChangedArgs.Item

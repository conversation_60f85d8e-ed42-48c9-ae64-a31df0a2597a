<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Articles | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Articles | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="articles">Articles</h1>

<p><strong>NOTE: this is under heavy construction AND NOT READY TO USE YET, may contain some outdated or non-exact information!!!.</strong></p>
<p>This section contains documentation articles and guides for using DrawnUi.</p>
<h2 id="getting-started">Getting Started</h2>
<ul>
<li><a href="getting-started.html">Installation and Setup</a></li>
<li><a href="first-app.html">Your First DrawnUi App</a></li>
<li><a href="drawing-pipeline.html">Understanding the Drawing Pipeline</a></li>
</ul>
<h2 id="controls">Controls</h2>
<ul>
<li><a href="controls/index.html">Overview</a></li>
<li><a href="controls/buttons.html">Buttons</a></li>
<li><a href="controls/switches.html">Switches and Toggles</a></li>
<li><a href="controls/layouts.html">Layout Controls</a></li>
<li><a href="controls/text.html">Text and Labels</a></li>
<li><a href="controls/images.html">Images</a></li>
</ul>
<h2 id="advanced-topics">Advanced Topics</h2>
<ul>
<li><a href="advanced/platform-styling.html">Platform-Specific Styling</a></li>
<li><a href="advanced/layout-system.html">Layout System Architecture</a></li>
<li><a href="advanced/gradients.html">Gradients</a></li>
<li><a href="advanced/game-ui.html">Game UI &amp; Interactive Games</a></li>
<li><a href="advanced/skiascroll.html">SkiaScroll &amp; Virtualization</a></li>
<li><a href="advanced/gestures.html">Gestures &amp; Touch Input</a></li>
</ul>
<h2 id="faq">FAQ</h2>
<ul>
<li><p>Is it DrawnUI or DrawnUi?</p>
</li>
<li><p>Both are totally fine.</p>
</li>
<li><p>How do i create my custom button?</p>
</li>
<li><p>While you can use SkiaButton and set a custom content to it, You can also use a click handler Tapped with ANY control you like.</p>
</li>
<li><p>I have an exting MAUI app, how DrawnUi can be beneficial to me?</p>
</li>
<li><p>You can definetely speed your app by replacing slow UI parts with faster drawn sections, replacing a horde or native controls with just one canvas. Check out the <a href="porting-maui.html">Porting MAUI</a> guide.</p>
</li>
<li><p>Knowing that properties are in points, how do i create a line or stroke of exactly 1 pixel?</p>
</li>
<li><p>When working with SKiaShape use a negative value (ex: -1) to pass pixels instead of points to compatible properties like StrokeWidth and similar.</p>
</li>
<li><p>How do i bind SkiaImage source not to a file/url for to an existing bitmap?</p>
</li>
<li><p>Use <code>ImageBitmap</code> property for that, type is <code>LoadedImageSource</code>.</p>
</li>
</ul>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/index.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>

<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Articles | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Articles | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="../toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">
        <div class="article row grid">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="articles" sourcefile="articles/index.md" sourcestartlinenumber="1">Articles</h1>

<p sourcefile="articles/index.md" sourcestartlinenumber="3"><strong sourcefile="articles/index.md" sourcestartlinenumber="3">NOTE: this is under heavy construction AND NOT READY TO USE YET, may contain some outdated or non-exact information!!!.</strong></p>
<p sourcefile="articles/index.md" sourcestartlinenumber="5">This section contains documentation articles and guides for using DrawnUi.</p>
<h2 id="getting-started" sourcefile="articles/index.md" sourcestartlinenumber="7">Getting Started</h2>
<ul sourcefile="articles/index.md" sourcestartlinenumber="9">
<li sourcefile="articles/index.md" sourcestartlinenumber="9"><a href="getting-started.html" sourcefile="articles/index.md" sourcestartlinenumber="9">Installation and Setup</a></li>
<li sourcefile="articles/index.md" sourcestartlinenumber="10"><a href="first-app.html" sourcefile="articles/index.md" sourcestartlinenumber="10">Your First DrawnUi App</a></li>
<li sourcefile="articles/index.md" sourcestartlinenumber="11"><a href="drawing-pipeline.html" sourcefile="articles/index.md" sourcestartlinenumber="11">Understanding the Drawing Pipeline</a></li>
</ul>
<h2 id="controls" sourcefile="articles/index.md" sourcestartlinenumber="13">Controls</h2>
<ul sourcefile="articles/index.md" sourcestartlinenumber="15">
<li sourcefile="articles/index.md" sourcestartlinenumber="15"><a href="controls/index.html" sourcefile="articles/index.md" sourcestartlinenumber="15">Overview</a></li>
<li sourcefile="articles/index.md" sourcestartlinenumber="16"><a href="controls/buttons.html" sourcefile="articles/index.md" sourcestartlinenumber="16">Buttons</a></li>
<li sourcefile="articles/index.md" sourcestartlinenumber="17"><a href="controls/switches.html" sourcefile="articles/index.md" sourcestartlinenumber="17">Switches and Toggles</a></li>
<li sourcefile="articles/index.md" sourcestartlinenumber="18"><a href="controls/layouts.html" sourcefile="articles/index.md" sourcestartlinenumber="18">Layout Controls</a></li>
<li sourcefile="articles/index.md" sourcestartlinenumber="19"><a href="controls/text.html" sourcefile="articles/index.md" sourcestartlinenumber="19">Text and Labels</a></li>
<li sourcefile="articles/index.md" sourcestartlinenumber="20"><a href="controls/images.html" sourcefile="articles/index.md" sourcestartlinenumber="20">Images</a></li>
</ul>
<h2 id="advanced-topics" sourcefile="articles/index.md" sourcestartlinenumber="22">Advanced Topics</h2>
<ul sourcefile="articles/index.md" sourcestartlinenumber="24">
<li sourcefile="articles/index.md" sourcestartlinenumber="24"><a href="advanced/platform-styling.html" sourcefile="articles/index.md" sourcestartlinenumber="24">Platform-Specific Styling</a></li>
<li sourcefile="articles/index.md" sourcestartlinenumber="25"><a href="advanced/layout-system.html" sourcefile="articles/index.md" sourcestartlinenumber="25">Layout System Architecture</a></li>
<li sourcefile="articles/index.md" sourcestartlinenumber="26"><a href="advanced/gradients.html" sourcefile="articles/index.md" sourcestartlinenumber="26">Gradients</a></li>
<li sourcefile="articles/index.md" sourcestartlinenumber="27"><a href="advanced/game-ui.html" sourcefile="articles/index.md" sourcestartlinenumber="27">Game UI &amp; Interactive Games</a></li>
<li sourcefile="articles/index.md" sourcestartlinenumber="28"><a href="advanced/skiascroll.html" sourcefile="articles/index.md" sourcestartlinenumber="28">SkiaScroll &amp; Virtualization</a></li>
<li sourcefile="articles/index.md" sourcestartlinenumber="29"><a href="advanced/gestures.html" sourcefile="articles/index.md" sourcestartlinenumber="29">Gestures &amp; Touch Input</a></li>
</ul>
<h2 id="faq" sourcefile="articles/index.md" sourcestartlinenumber="32">FAQ</h2>
<ul sourcefile="articles/index.md" sourcestartlinenumber="34">
<li sourcefile="articles/index.md" sourcestartlinenumber="34"><p sourcefile="articles/index.md" sourcestartlinenumber="34">Is it DrawnUI or DrawnUi?</p>
</li>
<li sourcefile="articles/index.md" sourcestartlinenumber="35"><p sourcefile="articles/index.md" sourcestartlinenumber="35">Both are totally fine.</p>
</li>
<li sourcefile="articles/index.md" sourcestartlinenumber="37"><p sourcefile="articles/index.md" sourcestartlinenumber="37">How do i create my custom button?</p>
</li>
<li sourcefile="articles/index.md" sourcestartlinenumber="38"><p sourcefile="articles/index.md" sourcestartlinenumber="38">While you can use SkiaButton and set a custom content to it, You can also use a click handler Tapped with ANY control you like.</p>
</li>
<li sourcefile="articles/index.md" sourcestartlinenumber="40"><p sourcefile="articles/index.md" sourcestartlinenumber="40">I have an exting MAUI app, how DrawnUi can be beneficial to me?</p>
</li>
<li sourcefile="articles/index.md" sourcestartlinenumber="41"><p sourcefile="articles/index.md" sourcestartlinenumber="41">You can definetely speed your app by replacing slow UI parts with faster drawn sections, replacing a horde or native controls with just one canvas. Check out the <a href="porting-maui.html" sourcefile="articles/index.md" sourcestartlinenumber="41">Porting MAUI</a> guide.</p>
</li>
<li sourcefile="articles/index.md" sourcestartlinenumber="43"><p sourcefile="articles/index.md" sourcestartlinenumber="43">Knowing that properties are in points, how do i create a line or stroke of exactly 1 pixel?</p>
</li>
<li sourcefile="articles/index.md" sourcestartlinenumber="44"><p sourcefile="articles/index.md" sourcestartlinenumber="44">When working with SKiaShape use a negative value (ex: -1) to pass pixels instead of points to compatible properties like StrokeWidth and similar.</p>
</li>
<li sourcefile="articles/index.md" sourcestartlinenumber="46"><p sourcefile="articles/index.md" sourcestartlinenumber="46">How do i bind SkiaImage source not to a file/url for to an existing bitmap?</p>
</li>
<li sourcefile="articles/index.md" sourcestartlinenumber="47"><p sourcefile="articles/index.md" sourcestartlinenumber="47">Use <code sourcefile="articles/index.md" sourcestartlinenumber="47">ImageBitmap</code> property for that, type is <code sourcefile="articles/index.md" sourcestartlinenumber="47">LoadedImageSource</code>.</p>
</li>
</ul>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/index.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>

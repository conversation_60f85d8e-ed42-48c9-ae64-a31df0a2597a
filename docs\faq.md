# Frequently Asked Questions

**Is it DrawnUI or DrawnUi?**
Both are totally fine.

**How do I create my custom button?**
While you can use SkiaButton and set a custom content to it, you can also use a click handler Tapped with ANY control you like.

**I have an existing MAUI app, how can DrawnUi be beneficial to me?**
You can definitely speed your app by replacing slow UI parts with faster drawn sections, replacing a horde of native controls with just one canvas. Check out the [Porting MAUI](articles/porting-maui.md) guide.

**Knowing that properties are in points, how do I create a line or stroke of exactly 1 pixel?**
When working with SkiaShape use a negative value (ex: -1) to pass pixels instead of points to compatible properties like StrokeWidth and similar.

**How do I bind SkiaImage source not to a file/url but to an existing bitmap?**
Use `ImageBitmap` property for that, type is `LoadedImageSource`.

**Can DrawnUi use MAUI's default Images folder?**
Unfortunately no. DrawnUi can read from Raw folder and from native storage if the app has written there, but not from the Images folder. It's "hardcoded-designed for MAUI views" and not accessible to DrawnUi controls.

**How do I prevent touch input from passing through overlapping controls?**
Use the `BlockGesturesBelow="True"` property on the top control. Note that `InputTransparent` makes the control itself avoid gestures, but doesn't block gestures from reaching controls below it in the Z-axis.

**Does DrawnUi work with .NET 9?**
Yes, DrawnUi works with .NET 9. However, remember that SkiaLabel, SkiaLayout etc. are virtual drawn controls that must be placed inside a `Canvas` control: `<draw:Canvas>your skia controls</draw:Canvas>`. Only Canvas has handlers for normal and hardware accelerated views.

**How do I enable mouse wheel scrolling in SkiaScroll?**
Mouse wheel scrolling is not built-in by default, but you can easily implement it by subclassing SkiaScroll and overriding the `ProcessGestures` method to handle `TouchActionResult.Wheel` events. See the [GitHub discussions](https://github.com/taublast/DrawnUi/discussions/162) for a complete implementation example.

---

## Didn't find your answer?

If you didn't find the answer to your question here, please feel free to ask in our [GitHub Discussions](https://github.com/taublast/DrawnUi/discussions). The community and maintainers are active there and happy to help with any questions about DrawnUi implementation, troubleshooting, or best practices.

You can also:
- Browse existing discussions to see if someone else had a similar question
- Report bugs or request features in [GitHub Issues](https://github.com/taublast/DrawnUi/issues)
- Check out the [complete documentation](articles/) for detailed guides and examples

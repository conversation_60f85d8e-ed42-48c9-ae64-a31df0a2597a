﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AppoMobi.Mobile.Views;

namespace AppoMobi.Mobile;

public class PopupEditTextBak : SkiaLayout
{
    private readonly string _value;
    private readonly Action<string> _callback;
    private readonly string _title;

    public PopupEditTextBak(string Title, string value, Action<string> callback)
    {
        _callback = callback;
        _title = Title;
        _value = value;

        VerticalOptions = LayoutOptions.Fill;
        HorizontalOptions = LayoutOptions.Fill;
        //BackgroundColor = Color.Parse("#66000000");
        //Tapped += (object? sender, ControlTappedEventArgs e) =>
        //{
        //    _ = App.Instance.Singletons.Presentation.Shell.ClosePopupAsync(true);
        //};

        Children = new List<SkiaControl>()
        {
            new AppFrame()
            {
                HorizontalOptions = LayoutOptions.Fill,
                VerticalOptions = LayoutOptions.Center,
                HeightRequest = 100,
                Children =
                {
                    new SkiaStack()
                    {
                        VerticalOptions = LayoutOptions.Start,
                        Children = new List<SkiaControl>()
                        {
                            // HEADER
                            new ModalHeader
                            {
                                VerticalOptions = LayoutOptions.Start,
                                Title = _title
                            },
                            new SkiaStack()
                            {
                                BackgroundColor = AppColors.Background,
                            }
                        }
                    }
                }
            }
        };
    }
}
<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Namespace DrawnUi.Draw | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Namespace DrawnUi.Draw | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../images/favicon.ico">
      <link rel="stylesheet" href="../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../styles/docfx.css">
      <link rel="stylesheet" href="../styles/main.css">
      <meta property="docfx:navrel" content="../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../index.html">
                <img id="logo" class="svg" src="../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="DrawnUi.Draw">

  <h1 id="DrawnUi_Draw" data-uid="DrawnUi.Draw" class="text-break">Namespace DrawnUi.Draw</h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="markdown level0 remarks"></div>
    <h3 id="classes">
Classes
</h3>
      <h4><a class="xref" href="DrawnUi.Draw.ActionOnTickAnimator.html">ActionOnTickAnimator</a></h4>
      <section><p>Just register this animator to run custom code on every frame creating a kind of game loop if needed.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.AddGestures.html">AddGestures</a></h4>
      <section><p>For fast and lazy gestures handling to attach to dran controls inside the canvas only</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.AddGestures.GestureListener.html">AddGestures.GestureListener</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.AdjustBrightnessEffect.html">AdjustBrightnessEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.AdjustRGBEffect.html">AdjustRGBEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.AnimateExtensions.html">AnimateExtensions</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.AnimatorBase.html">AnimatorBase</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.BaseChainedEffect.html">BaseChainedEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.BaseColorFilterEffect.html">BaseColorFilterEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.BaseImageFilterEffect.html">BaseImageFilterEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.BindToParentContextExtension.html">BindToParentContextExtension</a></h4>
      <section><p>Compiled-bindings-friendly implementation for &quot;Source.Parent.BindingContext.Path&quot;</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.BindablePropertyExtension.html">BindablePropertyExtension</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.BlinkAnimator.html">BlinkAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.BlurEffect.html">BlurEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.CachedGradient.html">CachedGradient</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.CachedObject.html">CachedObject</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.CachedShader.html">CachedShader</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.CachedShadow.html">CachedShadow</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.CellWIthHeight.html">CellWIthHeight</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ChainAdjustBrightnessEffect.html">ChainAdjustBrightnessEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ChainAdjustContrastEffect.html">ChainAdjustContrastEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ChainAdjustLightnessEffect.html">ChainAdjustLightnessEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ChainAdjustRGBEffect.html">ChainAdjustRGBEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ChainColorPresetEffect.html">ChainColorPresetEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ChainDropShadowsEffect.html">ChainDropShadowsEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ChainSaturationEffect.html">ChainSaturationEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ChainTintWithAlphaEffect.html">ChainTintWithAlphaEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ColorBlendAnimator.html">ColorBlendAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ColorExtensions.html">ColorExtensions</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ColorPresetEffect.html">ColorPresetEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ContainsPointResult.html">ContainsPointResult</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ContentLayout.html">ContentLayout</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ContrastEffect.html">ContrastEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ControlInStack.html">ControlInStack</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ControlsTracker.html">ControlsTracker</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.CriticallyDampedSpringTimingParameters.html">CriticallyDampedSpringTimingParameters</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.DataContextIterator.html">DataContextIterator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.DebugImage.html">DebugImage</a></h4>
      <section><p>Control for displaying used Surface as a preview image, for debugging purposes.
Do not use this in prod, this will be invalidated every frame, causing non-stop screen update.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.DecelerationTimingParameters.html">DecelerationTimingParameters</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.DecelerationTimingVectorParameters.html">DecelerationTimingVectorParameters</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.DescendingZIndexGestureListenerComparer.html">DescendingZIndexGestureListenerComparer</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.DrawingRect.html">DrawingRect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.DrawnExtensions.html">DrawnExtensions</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.DrawnFontAttributesConverter.html">DrawnFontAttributesConverter</a></h4>
      <section><p>Forked from Microsoft.Maui.Controls as using original class was breaking XAML HotReload for some unknown reason</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.DrawnUiStartupSettings.html">DrawnUiStartupSettings</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.DropShadowEffect.html">DropShadowEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.DynamicGrid-1.html">DynamicGrid&lt;T&gt;</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.EdgeGlowAnimator.html">EdgeGlowAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ElementRenderer.html">ElementRenderer</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.FindTagExtension.html">FindTagExtension</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.FluentExtensions.html">FluentExtensions</a></h4>
      <section><p>Provides extension methods for fluent API design pattern with DrawnUI controls</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.FrameTimeInterpolator.html">FrameTimeInterpolator</a></h4>
      <section><p>Interpolated time between frames, works in seconds. See examples..</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.InfiniteLayout.html">InfiniteLayout</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.KeyboardManager.html">KeyboardManager</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.LayoutStructure.html">LayoutStructure</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.LoadedImageSource.html">LoadedImageSource</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.Looper.html">Looper</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.LottieRefreshIndicator.html">LottieRefreshIndicator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.MauiKeyMapper.html">MauiKeyMapper</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.MeasuredListCell.html">MeasuredListCell</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.MeasuredListCells.html">MeasuredListCells</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ObservableAttachedItemsCollection-1.html">ObservableAttachedItemsCollection&lt;T&gt;</a></h4>
      <section><p>We have to subclass ObservableCollection to avoid it sending empty oldItems upon Reset.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.PendulumAnimator.html">PendulumAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.PerpetualPendulumAnimator.html">PerpetualPendulumAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.PingPongAnimator.html">PingPongAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.Plane.html">Plane</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.PlanesScroll.html">PlanesScroll</a></h4>
      <section><p>Provides the ability to create/draw views directly while scrolling.
Content will be generated dynamically, instead of the usual way.
This control main logic is inside PaintOnPlane override, also it hacks content to work without a real Content.
You have to override <code>GetMeasuredView</code> to provide your views to be drawn upon passed index.
TODO: for horizonal</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.PointIsInsideResult.html">PointIsInsideResult</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ProgressAnimator.html">ProgressAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ProgressTrail.html">ProgressTrail</a></h4>
      <section><p>Progress trail component for linear progress bars.
Similar to SliderTrail but optimized for progress display.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.RangeAnimator.html">RangeAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.RangeVectorAnimator.html">RangeVectorAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.RefreshIndicator.html">RefreshIndicator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.RenderDrawingContext.html">RenderDrawingContext</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.RenderLabel.html">RenderLabel</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.RenderObject.html">RenderObject</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.RenderTreeRenderer.html">RenderTreeRenderer</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.RenderingAnimator.html">RenderingAnimator</a></h4>
      <section><p>This animator renders on canvas instead of just updating a value</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.RippleAnimator.html">RippleAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SaturationEffect.html">SaturationEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ScaledSize.html">ScaledSize</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ScrollFlingAnimator.html">ScrollFlingAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ScrollFlingVectorAnimator.html">ScrollFlingVectorAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ShaderDoubleTexturesEffect.html">ShaderDoubleTexturesEffect</a></h4>
      <section><p>Base shader effect class that has 2 input textures.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.ShimmerAnimator.html">ShimmerAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.Sk3dView.html">Sk3dView</a></h4>
      <section><p>Custom implementation of Android's Camera 3D helper for SkiaSharp</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkCamera3D.html">SkCamera3D</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkCamera3D2.html">SkCamera3D2</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkPatch3D.html">SkPatch3D</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaBackdrop.html">SkiaBackdrop</a></h4>
      <section><p>Warning with CPU-rendering edges will not be blurred: <a href="https://issues.skia.org/issues/40036320">https://issues.skia.org/issues/40036320</a></p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaBevel.html">SkiaBevel</a></h4>
      <section><p>Defines properties for creating bevel or emboss effects on shapes.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaButton.html">SkiaButton</a></h4>
      <section><p>Button-like control, can include any content inside. It's either you use default content (todo templates?..)
or can include any content inside, and properties will by applied by convention to a SkiaLabel with Tag <code>MainLabel</code>, SkiaShape with Tag <code>MainFrame</code>. At the same time you can override ApplyProperties() and apply them to your content yourself.
Convention elements tags: BtnText, BtnShape.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaButton.ButtonLabel.html">SkiaButton.ButtonLabel</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaCheckbox.html">SkiaCheckbox</a></h4>
      <section><p>Switch-like control, can include any content inside. It's aither you use default content (todo templates?..)
or can include any content inside, and properties will by applied by convention to a SkiaShape with Tag <code>Frame</code>, SkiaShape with Tag <code>Thumb</code>. At the same time you can override ApplyProperties() and apply them to your content yourself.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaControl.html">SkiaControl</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.html">SkiaControl.ControlTappedEventArgs</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaControlWithRect.html">SkiaControlWithRect</a></h4>
      <section><p>Used inside RenderingTree. Rect is real drawing position</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaControlsObservable.html">SkiaControlsObservable</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaCursor.html">SkiaCursor</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaDoubleAttachedTexturesEffect.html">SkiaDoubleAttachedTexturesEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaDrawingContext.html">SkiaDrawingContext</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaEditor.html">SkiaEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaEffect.html">SkiaEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaFontManager.html">SkiaFontManager</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaFrame.html">SkiaFrame</a></h4>
      <section><p>Alias for SkiaShape type Rectangle</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaGesturesParameters.html">SkiaGesturesParameters</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaGradient.html">SkiaGradient</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaGrid.html">SkiaGrid</a></h4>
      <section><p>MAUI Grid alternative</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaHotspot.html">SkiaHotspot</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaHotspotZoom.html">SkiaHotspotZoom</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaHoverMask.html">SkiaHoverMask</a></h4>
      <section><p>Paints the parent view with the background color with a clipped viewport oth this view size</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaImage.html">SkiaImage</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaImage.RescaledBitmap.html">SkiaImage.RescaledBitmap</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaImageEffects.html">SkiaImageEffects</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaImageManager.html">SkiaImageManager</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaImageManager.QueueItem.html">SkiaImageManager.QueueItem</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaImageTiles.html">SkiaImageTiles</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaLabel.html">SkiaLabel</a></h4>
      <section><p>A high-performance text rendering control that provides advanced text formatting,
layout, and styling capabilities using SkiaSharp for rendering.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaLabel.DecomposedText.html">SkiaLabel.DecomposedText</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaLabel.EmojiData.html">SkiaLabel.EmojiData</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaLabel.ObjectPools.html">SkiaLabel.ObjectPools</a></h4>
      <section><p>Thread-safe object pools for reducing GC allocations in text measurement</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaLabel.SpanCollection.html">SkiaLabel.SpanCollection</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaLabel.SpanMeasurement.html">SkiaLabel.SpanMeasurement</a></h4>
      <section><p>Span-based measurement methods to avoid string allocations</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaLabel.TextMetrics.html">SkiaLabel.TextMetrics</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaLabelFps.html">SkiaLabelFps</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaLayer.html">SkiaLayer</a></h4>
      <section><p>Absolute layout like MAUI Grid with just one column and row</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaLayout.html">SkiaLayout</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaLayout.BuildWrapLayout.html">SkiaLayout.BuildWrapLayout</a></h4>
      <section><p>Implementation for LayoutType.Wrap</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaLayout.Cell.html">SkiaLayout.Cell</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaLayout.SecondPassArrange.html">SkiaLayout.SecondPassArrange</a></h4>
      <section><p>Cell.Area contains the area for layout</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaLayout.SkiaGridStructure.html">SkiaLayout.SkiaGridStructure</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaMarkdownLabel.html">SkiaMarkdownLabel</a></h4>
      <section><p>Will internally create spans from markdown.
Spans property must not be set directly.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaMauiElement.html">SkiaMauiElement</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaPoint.html">SkiaPoint</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaProgress.html">SkiaProgress</a></h4>
      <section><p>Linear progress bar control with platform-specific styling.
Shows progress from Min to Value within the Min-Max range.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaRangeBase.html">SkiaRangeBase</a></h4>
      <section><p>Base class for range-based controls like sliders and progress bars.
Provides common functionality for value ranges, track management, and platform styling.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.HStack.html">SkiaRow</a></h4>
      <section><p>Horizontal stack,  like MAUI HorizontalStackLayout</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaScroll.html">SkiaScroll</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaScrollLooped.html">SkiaScrollLooped</a></h4>
      <section><p>Cycles content, so the scroll never ands but cycles from the start</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaSetter.html">SkiaSetter</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaShaderEffect.html">SkiaShaderEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaShadow.html">SkiaShadow</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaShape.html">SkiaShape</a></h4>
      <section><p>Extension of SkiaShape that adds bevel and emboss functionality</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaSlider.html">SkiaSlider</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaStack.html">SkiaStack</a></h4>
      <section><p>Vertical stack, like MAUI VerticalStackLayout</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaSvg.html">SkiaSvg</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaSwitch.html">SkiaSwitch</a></h4>
      <section><p>Switch-like control, can include any content inside. It's aither you use default content (todo templates?..)
or can include any content inside, and properties will by applied by convention to a SkiaShape with Tag <code>Frame</code>, SkiaShape with Tag <code>Thumb</code>. At the same time you can override ApplyProperties() and apply them to your content yourself.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaToggle.html">SkiaToggle</a></h4>
      <section><p>Base control for toggling between 2 states.
It provides no gestures support by itsself.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaValueAnimator.html">SkiaValueAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaVectorAnimator.html">SkiaVectorAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaWrap.html">SkiaWrap</a></h4>
      <section><p>A powerful flexible control, a bit like WPF StackPanel, arranges children in a responsive way according available size. Can change the number of Columns to use by default.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SliderThumb.html">SliderThumb</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SliderTrail.html">SliderTrail</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SliderValueDesc.html">SliderValueDesc</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.Snapping.html">Snapping</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SnappingLayout.html">SnappingLayout</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SortedGestureListeners.html">SortedGestureListeners</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SpringExtensions.html">SpringExtensions</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SpringTimingParameters.html">SpringTimingParameters</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SpringTimingVectorParameters.html">SpringTimingVectorParameters</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SpringWithVelocityAnimator.html">SpringWithVelocityAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SpringWithVelocityVectorAnimator.html">SpringWithVelocityVectorAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.StackLayoutStructure.html">StackLayoutStructure</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.StateEffect.html">StateEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.StaticResourcesExtensions.html">StaticResourcesExtensions</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.Super.html">Super</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SvgSpan.html">SvgSpan</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.TemplatedViewsPool.html">TemplatedViewsPool</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.TextLine.html">TextLine</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.TextSpan.html">TextSpan</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.TintEffect.html">TintEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.TintWithAlphaEffect.html">TintWithAlphaEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ToggleAnimator.html">ToggleAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.TrackedObject-1.html">TrackedObject&lt;T&gt;</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.UnderdampedSpringTimingParameters.html">UnderdampedSpringTimingParameters</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.VelocityAccumulator.html">VelocityAccumulator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ViewsAdapter.html">ViewsAdapter</a></h4>
      <section><p>Top level class for working with ItemTemplates. Holds visible views.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.ViewsIterator.html">ViewsIterator</a></h4>
      <section><p>To iterate over virtual views</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.VirtualScroll.html">VirtualScroll</a></h4>
      <section><p>this control gets a view and draws it on a virtual scrolling plane</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.ViscousFluidInterpolator.html">ViscousFluidInterpolator</a></h4>
      <section><p>Ported from google android</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.VisualLayer.html">VisualLayer</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.VisualTreeHandler.html">VisualTreeHandler</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ZoomContent.html">ZoomContent</a></h4>
      <section><p>Wrapper to zoom and pan content by changing the rendering scale so not affecting quality, this is not a transform.TODO add animated movement</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.ZoomEventArgs.html">ZoomEventArgs</a></h4>
      <section></section>
    <h3 id="structs">
Structs
</h3>
      <h4><a class="xref" href="DrawnUi.Draw.ApplySpan.html">ApplySpan</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ChainEffectResult.html">ChainEffectResult</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.CriticallyDampedSpringTimingVectorParameters.html">CriticallyDampedSpringTimingVectorParameters</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.DrawingContext.html">DrawingContext</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.GestureEventProcessingInfo.html">GestureEventProcessingInfo</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.LineGlyph.html">LineGlyph</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.LineSpan.html">LineSpan</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.LinearInterpolationTimingParameters.html">LinearInterpolationTimingParameters</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.MeasureRequest.html">MeasureRequest</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.PlanesScroll.ViewLayoutInfo.html">PlanesScroll.ViewLayoutInfo</a></h4>
      <section><p>Holds layout information for a rendered month cell.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.RangeF.html">RangeF</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ScaledPoint.html">ScaledPoint</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ScaledRect.html">ScaledRect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ScrollToIndexOrder.html">ScrollToIndexOrder</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ScrollToPointOrder.html">ScrollToPointOrder</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaControl.ParentMeasureRequest.html">SkiaControl.ParentMeasureRequest</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaLabel.PooledStringBuilder.html">SkiaLabel.PooledStringBuilder</a></h4>
      <section><p>Helper struct for managing pooled StringBuilder with automatic return</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaShape.ShapePaintArguments.html">SkiaShape.ShapePaintArguments</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.StringReference.html">StringReference</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.UnderdampedSpringTimingVectorParameters.html">UnderdampedSpringTimingVectorParameters</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.UsedGlyph.html">UsedGlyph</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.WindowParameters.html">WindowParameters</a></h4>
      <section></section>
    <h3 id="interfaces">
Interfaces
</h3>
      <h4><a class="xref" href="DrawnUi.Draw.IAfterEffectDelete.html">IAfterEffectDelete</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IAnimatorsManager.html">IAnimatorsManager</a></h4>
      <section><p>This control is responsible for updating screen for running animators</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.IBindingContextDebuggable.html">IBindingContextDebuggable</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ICanBeUpdated.html">ICanBeUpdated</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ICanBeUpdatedWithContext.html">ICanBeUpdatedWithContext</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ICanRenderOnCanvas.html">ICanRenderOnCanvas</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IColorEffect.html">IColorEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IDampingTimingParameters.html">IDampingTimingParameters</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IDampingTimingVectorParameters.html">IDampingTimingVectorParameters</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IDefinesViewport.html">IDefinesViewport</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IDrawnBase.html">IDrawnBase</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IDrawnTextSpan.html">IDrawnTextSpan</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IHasAfterEffects.html">IHasAfterEffects</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IHasBanner.html">IHasBanner</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IImageEffect.html">IImageEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IInsideViewport.html">IInsideViewport</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IInsideWheelStack.html">IInsideWheelStack</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IInterpolator.html">IInterpolator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ILayoutInsideViewport.html">ILayoutInsideViewport</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IOverlayEffect.html">IOverlayEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IPostRendererEffect.html">IPostRendererEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IRefreshIndicator.html">IRefreshIndicator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IRenderEffect.html">IRenderEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IRenderObject.html">IRenderObject</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ISkiaAnimator.html">ISkiaAnimator</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ISkiaCell.html">ISkiaCell</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ISkiaControl.html">ISkiaControl</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ISkiaDrawable.html">ISkiaDrawable</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ISkiaEffect.html">ISkiaEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ISkiaGestureListener.html">ISkiaGestureListener</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ISkiaGestureProcessor.html">ISkiaGestureProcessor</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ISkiaGridLayout.html">ISkiaGridLayout</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ISkiaLayer.html">ISkiaLayer</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ISkiaLayout.html">ISkiaLayout</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ISkiaSharpView.html">ISkiaSharpView</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IStateEffect.html">IStateEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ITimingParameters.html">ITimingParameters</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ITimingVectorParameters.html">ITimingVectorParameters</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IVisibilityAware.html">IVisibilityAware</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.IWithContent.html">IWithContent</a></h4>
      <section></section>
    <h3 id="enums">
Enums
</h3>
      <h4><a class="xref" href="DrawnUi.Draw.AutoSizeType.html">AutoSizeType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.BevelType.html">BevelType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ContextArguments.html">ContextArguments</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.DirectionType.html">DirectionType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.DrawImageAlignment.html">DrawImageAlignment</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.DrawTextAlignment.html">DrawTextAlignment</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.FontWeight.html">FontWeight</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.GesturesMode.html">GesturesMode</a></h4>
      <section><p>Used by the canvas, do not need this for drawn controls</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.GlowPosition.html">GlowPosition</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.GradientType.html">GradientType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.LayoutType.html">LayoutType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.LinearDirectionType.html">LinearDirectionType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.LoadPriority.html">LoadPriority</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.LockTouch.html">LockTouch</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.MauiKey.html">MauiKey</a></h4>
      <section><p>These are platform-independent. They correspond to JavaScript keys.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.MeasuringStrategy.html">MeasuringStrategy</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ObjectAliveType.html">ObjectAliveType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.OrientationType.html">OrientationType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.PanningModeType.html">PanningModeType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.PointedDirectionType.html">PointedDirectionType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.PrebuiltControlStyle.html">PrebuiltControlStyle</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.RangeZone.html">RangeZone</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.RecycleTemplateType.html">RecycleTemplateType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.RecyclingTemplate.html">RecyclingTemplate</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.RelativePositionType.html">RelativePositionType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.RenderingModeType.html">RenderingModeType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ScrollInteractionState.html">ScrollInteractionState</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ShapeType.html">ShapeType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SidePosition.html">SidePosition</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaAnchorBak.html">SkiaAnchorBak</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaButton.ButtonStyleType.html">SkiaButton.ButtonStyleType</a></h4>
      <section><p>Defines the button style variants available for different visual appearances.</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaButton.IconPositionType.html">SkiaButton.IconPositionType</a></h4>
      <section><p>Defines the position of an icon relative to the button text</p>
</section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaCacheType.html">SkiaCacheType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaControl.CacheValidityType.html">SkiaControl.CacheValidityType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaImageEffect.html">SkiaImageEffect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SkiaTouchAnimation.html">SkiaTouchAnimation</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SnapToChildrenType.html">SnapToChildrenType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SourceType.html">SourceType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.SpaceDistribution.html">SpaceDistribution</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.TextTransform.html">TextTransform</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.TransformAspect.html">TransformAspect</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.ViewportScrollType.html">ViewportScrollType</a></h4>
      <section></section>
      <h4><a class="xref" href="DrawnUi.Draw.VirtualisationType.html">VirtualisationType</a></h4>
      <section></section>


</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../styles/docfx.js"></script>
    <script type="text/javascript" src="../styles/main.js"></script>
  </body>
</html>

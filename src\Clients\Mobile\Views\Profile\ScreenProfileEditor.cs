﻿using AppoMobi.Framework.Maui.Converters;
using AppoMobi.Mobile;
using DrawnUi.Draw;
using DrawnUi.Extensions;
using DrawnUi.Views;
using Microsoft.Maui.Controls;
using System.Collections.Generic;
using static System.Net.Mime.MediaTypeNames;

namespace AppoMobi.Mobile.Views
{
    /// <summary>
    /// User profile editor screen
    /// </summary>
    public class ScreenProfileEditor : AppScreen
    {
        private readonly EditUserProfileViewModel Model;

        public ScreenProfileEditor(EditUserProfileViewModel vm)
        {
            Model = vm;
            BindingContext = Model;

            Shell.SetPresentationMode(this, PresentationMode.ModalAnimated);
            BackgroundColor = Colors.Transparent; //for modal

            Margin = UiHelper.ModalInsets;
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;

            Type = LayoutType.Column;
            Spacing = 0;
            CreateContent();
        }

        private void CreateContent()
        {
            Children = new List<SkiaControl>
            {
                new StatusBarPlaceholder(),
                // HEADER
                new ModalHeader
                {
                    Title = ResStrings.MyProfile
                },

                // CONTENT
                new SkiaShape()
                {
                    StrokeColor = AppColors.ControlPrimary,
                    StrokeWidth = 1,
                    BackgroundColor = AppColors.Background,
                    HorizontalOptions = LayoutOptions.Fill,
                    VerticalOptions = LayoutOptions.Fill,
                    Children =
                    {
                        new ScreenVerticalStack()
                        {
                            Padding = new(16),
                            Children = new List<SkiaControl>()
                            {
                                // Avatar
                                /*
                                new PaintAvatar()
                                {
                                    Margin = new Thickness(0, 0, 0, 8),
                                    HorizontalOptions = LayoutOptions.Center,
                                    HeightRequest = 80,
                                    WidthRequest = 80,
                                    StyleId = "Avatar",
                                    VerticalOptions = LayoutOptions.Start
                                }.Adapt((avatar) =>
                                {
        #if DEBUG
                                    avatar.SetBinding(PaintAvatar.SourceProperty, "Presentation.ImageMain.Normal");
        #else
                                        avatar.SetBinding(PaintAvatar.SourceProperty,
                                            static (EditUserProfileViewModel vm) => vm.Presentation.ImageMain.Normal);
        #endif
                                }),
                                */

                                // Personal Information Frame
                                CreatePersonalInfoFrame(),

                                // Validation Errors Label
                                new SkiaLabel()
                                    {
                                        Margin = new Thickness(16, 0, 16, 0), FontSize = 10,
                                        HorizontalOptions = LayoutOptions.Center,
                                        HorizontalTextAlignment = DrawTextAlignment.Center,
                                        TextColor = AppColors.Danger
                                    }
                                    .ObserveBindingContext<SkiaLabel, EditUserProfileViewModel>((me, vm, prop) =>
                                    {
                                        if (prop == nameof(vm.EditorValidationErrorsDesc) ||
                                            prop == nameof(BindingContext))
                                        {
                                            me.Text = vm.EditorValidationErrorsDesc;
                                            me.IsVisible = !string.IsNullOrEmpty(vm.EditorValidationErrorsDesc);
                                        }
                                    }),

                                // Submit Button
                                new AppSubmitButton(ResStrings.BtnOk)
                                    {
                                        Margin = new Thickness(0, 8, 0, 8),
                                    }
                                    .ObserveBindingContext<ButtonMedium, EditUserProfileViewModel>((me, vm, prop) =>
                                    {
                                        bool attached = prop == nameof(BindingContext);
                                        if (attached || prop == nameof(vm.CommandEditorSubmit))
                                        {
                                            me.CommandTapped = vm.CommandEditorSubmit;
                                        }

                                        if (attached || prop == nameof(vm.IsBusy))
                                        {
                                            me.IsVisible = !vm.IsBusy;
                                        }

                                        if (attached || prop == nameof(vm.EditorIsValid))
                                        {
                                            me.Look = vm.EditorIsValid ? BtnStyle.Default : BtnStyle.Disabled;
                                        }
                                    }),

                                // Activity Indicator
                                new AppActivityIndicator()
                                    {
                                        IsRunning = true,
                                    }
                                    .ObserveBindingContext<AppActivityIndicator, EditUserProfileViewModel>((me, vm,
                                        prop) =>
                                    {
                                        bool attached = prop == nameof(BindingContext);
                                        if (attached || prop == nameof(vm.IsBusy))
                                        {
                                            me.IsVisible = vm.IsBusy;
                                        }
                                    }),

                                // Keyboard Offset
                                new KeyboardPlaceholder()
                                {
                                }
                            }
                        }
                    }
                }
            };
        }

        /*

        private SkiaLayout CreateContentLayout()
        {
            return new SkiaLayout
            {
                BackgroundColor = AppColors.BackgroundPrimary,
                Padding = new Thickness(0, 16, 0, 0),
                HorizontalOptions = LayoutOptions.Fill,
                Tag = "DEBUG",
                Split = 1,
                Type = LayoutType.Wrap,
                Children = new List<SkiaControl>
                {
                    // AVATAR SECTION
                    CreateAvatarSection(),

                    // NAME SECTION
                    CreateNameSection(),

                    // WARNING TEXT
                    //new SkiaLabel
                    //{
                    //    Margin = new Thickness(16),
                    //    FontFamily = "FontText",
                    //    FontSize = 12,
                    //    FontWeight = 400,
                    //    HorizontalOptions = LayoutOptions.Center,
                    //    LineBreakMode = LineBreakMode.TailTruncation,
                    //    Text = ResStrings.WarningLocalData,
                    //    TextColor = AppColors.ColorGray
                    //}
                }
            }.Adapt((layout) =>
            {
#if DEBUG
                layout.SetBinding(SkiaLayout.AddMarginTopProperty, "Presentation.NavBarHeightRequest");
#else
                layout.SetBinding(SkiaLayout.AddMarginTopProperty,
                    static (EditUserProfileViewModel vm) => vm.Presentation.NavBarHeightRequest);
#endif
            });
        }

        private SkiaLayout CreateAvatarSection()
        {
            return new SkiaLayout
            {
                Margin = new Thickness(20, 0),
                HeightRequest = 64,
                HorizontalOptions = LayoutOptions.Fill,
                Spacing = 0,
                Children = new List<SkiaControl>
                {
                    // TITLE
                    new SkiaLabel
                    {
                        FontFamily = "FontText",
                        FontSize = 16,
                        FontWeight = 600,
                        LineBreakMode = LineBreakMode.TailTruncation,
                        MaxLines = 1,
                        Text = ResStrings.Avatar,
                        TextColor = AppColors.Text,
                        VerticalOptions = LayoutOptions.Center
                    },

                    // AVATAR CONTAINER
                    new SkiaShape
                    {
                        BackgroundColor = AppColors.BackgroundPrimary,
                        CornerRadius = 9,
                        HorizontalOptions = LayoutOptions.End,
                        LockRatio = 1,
                        UseCache = SkiaCacheType.Image,
                        VerticalOptions = LayoutOptions.Center,
                        WidthRequest = 34,
                        Content = new SkiaLayout
                        {
                            HorizontalOptions = LayoutOptions.Fill,
                            VerticalOptions = LayoutOptions.Fill,
                            Children = new List<SkiaControl>
                            {
                                // DEFAULT AVATAR ICON
                                new SkiaSvg
                                {
                                    HorizontalOptions = LayoutOptions.Center,
                                    LockRatio = 1,
                                    Opacity = 0.75,
                                    SvgString = App.Current.Resources.Get<string>("SvgAvatarEmpty"),
                                    TintColor = AppColors.IconSecondary,
                                    VerticalOptions = LayoutOptions.Center,
                                    WidthRequest = 32,
                                    ZIndex = -1
                                },

                                // USER AVATAR IMAGE
                                new SkiaImage
                                {
                                    Aspect = TransformAspect.AspectCover,
                                    HorizontalOptions = LayoutOptions.Fill,
                                    VerticalOptions = LayoutOptions.Fill
                                }.Adapt((image) =>
                                {
#if DEBUG
                                    image.SetBinding(SkiaImage.SourceProperty, "Presentation.User.Banner");
#else
                                    image.SetBinding(SkiaImage.SourceProperty,
                                        static (EditUserProfileViewModel vm) => vm.Presentation.User.Banner);
#endif
                                })
                            }
                        }
                    },

                    // BOTTOM DIVIDER
                    new SkiaControl
                    {
                        BackgroundColor = AppColors.Line,
                        HeightRequest = 1,
                        HorizontalOptions = LayoutOptions.Fill,
                        VerticalOptions = LayoutOptions.End
                    }
                }
            }.Adapt((layout) =>
            {
                // Add tap gesture for editing avatar
#if DEBUG
                AddGestures.SetCommandTapped(layout, Model.CommandEditAvatar);
#else
                layout.SetBinding(AddGestures.CommandTappedProperty,
                    static (EditUserProfileViewModel vm) => vm.CommandEditAvatar);
#endif
            });
        }

        */

        private SkiaLayout CreateNameSection()
        {
            return new SkiaLayout
            {
                Margin = new Thickness(20, 0),
                HeightRequest = 60,
                HorizontalOptions = LayoutOptions.Fill,
                Children = new List<SkiaControl>
                {
                    // TITLE
                    new SkiaLabel
                    {
                        FontFamily = "FontText",
                        FontSize = 16,
                        FontWeight = 600,
                        LineBreakMode = LineBreakMode.TailTruncation,
                        MaxLines = 1,
                        Text = ResStrings.Name,
                        TextColor = AppColors.Text,
                        VerticalOptions = LayoutOptions.Center
                    },

                    // USER NAME
                    new SkiaLabel
                    {
                        FontFamily = "FontText",
                        FontSize = 18,
                        FontWeight = 500,
                        HorizontalOptions = LayoutOptions.End,
                        LineBreakMode = LineBreakMode.TailTruncation,
                        MaxLines = 1,
                        TextColor = AppColors.Text,
                        VerticalOptions = LayoutOptions.Center
                    }.Adapt((label) => { label.SetBinding(SkiaLabel.TextProperty, "DisplayName"); }),

                    // BOTTOM DIVIDER
                    new SkiaControl
                    {
                        BackgroundColor = AppColors.Line,
                        HeightRequest = 1,
                        HorizontalOptions = LayoutOptions.Fill,
                        VerticalOptions = LayoutOptions.End
                    }
                }
            }.Adapt((layout) => { AddGestures.SetCommandTapped(layout, Model.CommandEditName); });
        }

        protected SkiaLayout CreatePersonalInfoFrame()
        {
            return new SkiaLayout()
            {
                Type = LayoutType.Column,
                Spacing = 16,
                HorizontalOptions = LayoutOptions.Fill,
                Children = new List<SkiaControl>()
                {
                    // First Name Section
                    App.Instance.Presentation.Shell.Elements.CreateInputSection("FirstName", ResStrings.FirstName, 1),

                    // Middle Name Section
                    //CreateInputSection("MiddleName", ResStrings.MiddleName, 0),

                    // Last Name Section
                    App.Instance.Presentation.Shell.Elements.CreateInputSection("LastName", ResStrings.LastName, 2)
                }
            };
        }
    }
}
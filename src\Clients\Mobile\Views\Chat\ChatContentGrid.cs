﻿using DrawnUi.Views;

namespace AppoMobi.Mobile.Views;

public class ChatContentGrid : SkiaLayout
{
    public ChatContentGrid()
    {
        Type = LayoutType.Grid;
        HorizontalOptions = LayoutOptions.Fill;
        VerticalOptions = LayoutOptions.Fill;
        RowSpacing = 0;
    }


    public override ScaledSize OnMeasuring(float widthConstraint, float heightConstraint, float scale)
    {
        var ret = base.OnMeasuring(widthConstraint, heightConstraint, scale);

        Debug.WriteLine($"[CHATGRID] Measured: {ret.Pixels}");

        //if (Superview is Canvas canvas)
        //{
        //    canvas.DumpDebug();
        //}

        return ret;
    }

    //public override void Arrange(SKRect destination, float widthRequest, float heightRequest, float scale)
    //{
    //    base.Arrange(destination, widthRequest, heightRequest, scale);

    //    if (this.GridStructure != null)
    //    {
    //        var check = this.GridStructure.Rows;
    //        Debug.WriteLine($"[CHATGRID] Row0: {GridStructure.Rows[0].Size}");
    //    }
    //}
}
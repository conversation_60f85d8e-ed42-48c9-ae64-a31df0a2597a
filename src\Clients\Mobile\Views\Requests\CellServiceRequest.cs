﻿using System.ComponentModel;
using AppoMobi.Common.Enums.UserData;
using AppoMobi.Common.Extensions;
using AppoMobi.Specials.Localization;

namespace AppoMobi.Mobile.Views;

public class CellServiceRequest : FastCellWithBanner
{
    private SkiaLayout LayoutReview;

    SkiaLabel labelService;
    SkiaLabel labelFrom;
    SkiaLabel labelTo;
    SkiaLabel LabelStatus;
    SkiaSvg SvgStatus;
    SkiaShape FrameStatus;
    SkiaLabel labelDate;
    SkiaLabel labelTime;
    SkiaLabel labelPrice;
    SkiaLabel labelQuestion;
    SkiaShape MainFrame;

    public CellServiceRequest()
    {
        Tag = "RequestCell";

        var cellsHeight = 200.0;

        var tabsCorners = 16.0;
        var tabsHeight = 36.0;

        var cellsCorners = 12.0;

        var infoFontSize = 14.0;
        var infoIconSize = 12.5;
        var infoInterval = 10.0;

        HorizontalOptions = LayoutOptions.Fill;
        HeightRequest = cellsHeight;
        Padding = new(16, 4);
        UseCache = SkiaCacheType.ImageDoubleBuffered;
        Children = new List<SkiaControl>()
        {
            new AppFrame()
            {
                BackgroundColor = AppColors.BackgroundSecondary,
                StrokeColor = AppColors.ControlSecondary,
                StrokeWidth = 1,
                HorizontalOptions = LayoutOptions.Fill,
                VerticalOptions = LayoutOptions.Fill,
                Children =
                {
                    new SkiaLayout()
                    {
                        HorizontalOptions = LayoutOptions.Fill,
                        VerticalOptions = LayoutOptions.Fill,
                        Children = new List<SkiaControl>()
                        {
                            //content
                            new SkiaLayout()
                            {
                                UseCache = SkiaCacheType.Image,
                                HorizontalOptions = LayoutOptions.Fill,
                                VerticalOptions = LayoutOptions.Fill,
                                Padding = new(16, 16),
                                Children = new List<SkiaControl>()
                                {
                                    //colored cell header
                                    new SkiaShape()
                                    {
                                        HeightRequest = 56,
                                        Margin = -16,
                                        HorizontalOptions = LayoutOptions.Fill,
                                        BackgroundColor = AppColors.ControlMinorOpacity8,
                                    },

                                    //arrow down
                                    //new SkiaSvg()
                                    //{
                                    //    Margin = new Thickness(0,9,0,0),
                                    //    HeightRequest = 15,
                                    //    LockRatio = 1,
                                    //    TintColor = AppColors.Text,
                                    //    SvgString = App.Current.Resources
                                    //        .Get<string>(
                                    //            "SvgArrowFromTo"),
                                    //},

                                    //SERVICE
                                    new SkiaLabel()
                                    {
                                        Tag = "ServiceName",
                                        Margin = new Thickness(0, 0, 120, 0),
                                        TextColor = AppColors.Text,
                                        FontSize = 17,
                                        MaxLines = 1,
                                        LineBreakMode = LineBreakMode.TailTruncation,
                                        FontFamily = "FontTextBold"
                                    }.Assign(out labelService),

                                    //address FROM
                                    new SkiaLabel()
                                    {
                                        Margin = new Thickness(0, 53, 120, 0),
                                        //Text = addressFrom,//"123 Main St",
                                        TextColor = AppColors.Text,
                                        FontSize = 15,
                                        MaxLines = 1,
                                        LineBreakMode = LineBreakMode.TailTruncation,
                                        FontFamily = AppFonts.SemiBold
                                    }.Assign(out labelFrom),

                                    new SkiaStack()
                                    {
                                        Spacing = 4,
                                        Margin = new Thickness(0, 74, 0, 0),
                                        Children = new List<SkiaControl>()
                                        {
                                            //address TO
                                            new SkiaLabel()
                                            {
                                                TextColor = AppColors.ControlPrimary,
                                                FontSize = 15,
                                                MaxLines = 2,
                                                LineBreakMode = LineBreakMode.TailTruncation,
                                                FontFamily = AppFonts.Bold
                                            }.Assign(out labelTo),

                                            //Cargo info
                                            new SkiaLabel()
                                            {
                                                TextColor = AppColors.ControlPrimary,
                                                FontSize = 15,
                                                MaxLines = 1,
                                                LineBreakMode = LineBreakMode.TailTruncation,
                                                FontFamily = AppFonts.Normal
                                            }.Assign(out labelQuestion),
                                        }
                                    },


                                    // info TIME etc
                                    new SkiaLayout()
                                    {
                                        VerticalOptions = LayoutOptions.End,
                                        HorizontalOptions = LayoutOptions.Fill,
                                        Type = LayoutType.Row,
                                        Spacing = 3,
                                        Children = new List<SkiaControl>()
                                        {
                                            // day
                                            new SkiaSvg()
                                            {
                                                VerticalOptions = LayoutOptions.Center,
                                                HeightRequest = infoIconSize,
                                                LockRatio = 1,
                                                TintColor = AppColors.Text,
                                                SvgString = App.Current.Resources
                                                    .Get<string>(
                                                        "SvgTimeDate"),
                                            },
                                            new SkiaLabel()
                                            {
                                                VerticalOptions = LayoutOptions.Center,
                                                //Text = "12 мая",
                                                TextColor = AppColors.Text,
                                                FontSize = infoFontSize,
                                                FontFamily = AppFonts.SemiBold
                                            }.Assign(out labelDate),

                                            // time
                                            new SkiaSvg()
                                            {
                                                Margin = new(infoInterval, 0, 0, 0),
                                                VerticalOptions = LayoutOptions.Center,
                                                HeightRequest = infoIconSize,
                                                LockRatio = 1,
                                                TintColor = AppColors.Text,
                                                SvgString = App.Current.Resources
                                                    .Get<string>(
                                                        "SvgTime"),
                                            },
                                            new SkiaLabel()
                                            {
                                                VerticalOptions = LayoutOptions.Center,
                                                //Text = "13:40, 2ч.",
                                                TextColor = AppColors.Text,
                                                FontSize = infoFontSize,
                                                FontFamily = AppFonts.SemiBold
                                            }.Assign(out labelTime),

                                            // cost
                                            new SkiaSvg()
                                            {
                                                Margin = new(infoInterval, 0, 0, 0),
                                                VerticalOptions = LayoutOptions.Center,
                                                HeightRequest = infoIconSize,
                                                LockRatio = 1,
                                                TintColor = AppColors.Text,
                                                SvgString = App.Current.Resources
                                                    .Get<string>(
                                                        "SvgMoney"),
                                            },
                                            new SkiaLabel()
                                            {
                                                VerticalOptions = LayoutOptions.Center,
                                                //Text = "3600 р.",
                                                TextColor = AppColors.Text,
                                                FontSize = infoFontSize,
                                                FontFamily = AppFonts.SemiBold
                                            }.Assign(out labelPrice),
                                        }
                                    },


                                    // STATUS tag
                                    new SkiaShape()
                                    {
                                        Tag = "Status",
                                        StrokeWidth = 0,
                                        StrokeColor = AppColors.ControlHoverMinor,
                                        BackgroundColor = AppColors.PrimaryLight,
                                        CornerRadius = 8,
                                        Padding = new(8, 5),
                                        HorizontalOptions = LayoutOptions.End,
                                        VerticalOptions = LayoutOptions.Start,
                                        Children =
                                        {
                                            new SkiaLayout()
                                            {
                                                Type = LayoutType.Row,
                                                Spacing = 5,
                                                Children = new List<SkiaControl>()
                                                {
                                                    new SkiaSvg()
                                                    {
                                                        VerticalOptions = LayoutOptions.Center,
                                                        HeightRequest = 15,
                                                        LockRatio = 1,
                                                        TintColor = AppColors.Text,
                                                        SvgString = App.Current.Resources
                                                            .Get<string>(
                                                                "SvgStatusDeliveryProcessing"),
                                                    }.Assign(out SvgStatus),
                                                    new SkiaLabel()
                                                    {
                                                        Margin = new(0, 0, 0, 1),
                                                        VerticalOptions = LayoutOptions.Center,
                                                        //Text = statusProcessing,
                                                        TextColor = AppColors.Text,
                                                        FontSize = 12,
                                                        FontFamily = "FontTextSemiBold"
                                                    }.Assign(out LabelStatus)
                                                }
                                            }
                                        }
                                    }.Assign(out FrameStatus),
                                }
                            },

                            //rating overlay
                            new SkiaLayout()
                            {
                                IsVisible = false,
                                UseCache = SkiaCacheType.Image,
                                HorizontalOptions = LayoutOptions.Fill,
                                VerticalOptions = LayoutOptions.Start,
                                HeightRequest = 56,
                                Type = LayoutType.Row,
                                Padding = new(16, 0),
                                Background = AppColors.PrimaryLight,
                                Children = new List<SkiaControl>()
                                {
                                    new SkiaSvg()
                                    {
                                        SvgString = App.Current.Resources.Get<string>("SvgDuoThumb"),
                                        FontAwesomePrimaryColor = AppColors.TextSecondary,
                                        FontAwesomeSecondaryColor = AppColors.ControlPrimary,
                                        HeightRequest = 26,
                                        LockRatio = 1,
                                        VerticalOptions = LayoutOptions.Center,
                                    },

                                    new SkiaLabel()
                                    {
                                        MaxLines = 2,
                                        LineBreakMode = LineBreakMode.TailTruncation,
                                        VerticalOptions = LayoutOptions.Center,
                                        FontSize = 15,
                                        FontFamily = AppFonts.SemiBold,
                                        Text = ResStrings.FinishedTitle
                                    }
                                }
                            }.Assign(out LayoutReview),
                        }
                    }
                }

                //Shadows = new List<SkiaShadow>
                //{
                //    new SkiaShadow
                //    {
                //        Blur = 2,
                //        Opacity = 1.0,
                //        X = 1,
                //        Y = 1,
                //        Color = AppColors.ControlMinor
                //    }
                //}
            }.Assign(out MainFrame),
        };
    }

    protected override void OnBindingContextChanged()
    {
        base.OnBindingContextChanged();

        var dto = BindingContext as ServiceRequest;
        if (dto != null)
        {
            labelService.Text = $"{dto.Service.Name}";

            SetStatus(dto);
            SetHours(dto);
            SetCost(dto);
            SetTime(dto);

            labelFrom.Text = $"{ResStrings.FromSmall} {dto.Building.DisplayDescription}";

            SetAddressTo(dto);

            SetCargoDetails(dto);
        }
    }

    void SetAddressTo(ServiceRequest dto)
    {
        var address = $"{ResStrings.ToSmall} {dto.Address}";
        if (!string.IsNullOrEmpty(dto.AddressSub))
        {
            address += $", {ResStrings.FlatNbShort} {dto.AddressSub}";
        }

        labelTo.Text = address;
    }

    void SetCargoDetails(ServiceRequest dto)
    {
        labelQuestion.Text = $"{dto.Question}";
    }

    void SetStatus(ServiceRequest dto)
    {
        //works only for master, for pro need other..
        var status = dto.Status.Localize();

        LabelStatus.Text = status;

        if (dto.Status == CustomerRequestStatus.Complete
            || dto.Status == CustomerRequestStatus.SlaveCanceled
            || dto.Status == CustomerRequestStatus.Canceled
            || dto.Status == CustomerRequestStatus.Rejected)
        {
            MainFrame.BackgroundColor = AppColors.BackgroundSecondary;
            FrameStatus.BackgroundColor = AppColors.Background;

            SvgStatus.SvgString = App.Current.Resources
                .Get<string>(
                    "SvgStatusDeliveryComplete");

            LayoutReview.IsVisible = dto.Status == CustomerRequestStatus.Complete && dto.Rating == 0;
        }
        else
        {
            MainFrame.BackgroundColor = AppColors.Background;
            FrameStatus.BackgroundColor = AppColors.PrimaryLight;
            SvgStatus.SvgString = App.Current.Resources
                .Get<string>(
                    "SvgStatusDeliveryProcessing");

            LayoutReview.IsVisible = false;
        }
    }

    void SetHours(ServiceRequest dto)
    {
        var hours = 2.0;
        if (dto.Hours > hours)
            hours = dto.Hours;
        labelTime.Text = $"{hours:0} {ResStrings.HoursSmall}";
    }

    void SetCost(ServiceRequest dto)
    {
        decimal price = dto.FixedPrice;
        labelPrice.Text = $"{price:0} ₽";
    }

    void SetTime(ServiceRequest dto)
    {
        labelDate.Text = $"{dto.EditedTime.GetValueOrDefault().ToLocalTime().ToShortDateString()}";
    }

    protected override void ContextPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        base.ContextPropertyChanged(sender, e);

        if (BindingContext is ServiceRequest model)
        {
            if (e.PropertyName == nameof(ServiceRequest.Notify))
            {
                MainFrame.BackgroundColor =
                    model.Notify ? AppColors.BackgroundNotify : AppColors.BackgroundSecondary;
            }
            else if (e.PropertyName == nameof(ServiceRequest.Rating))
            {
                SetStatus(model);
            }
            else if (e.PropertyName == nameof(ServiceRequest.Status))
            {
                SetStatus(model);
            }
            else if (e.PropertyName == nameof(ServiceRequest.Hours))
            {
                SetHours(model);
            }
            else if (e.PropertyName == nameof(ServiceRequest.FixedPrice))
            {
                SetCost(model);
            }
            else if (e.PropertyName == nameof(ServiceRequest.Question))
            {
                SetCargoDetails(model);
            }
            else if (e.PropertyName.IsEither(nameof(ServiceRequest.Address), nameof(ServiceRequest.AddressSub)))
            {
                SetAddressTo(model);
            }
        }
    }
}